<script setup lang="ts">
import NoteDrawer from '@/components/NoteDrawer.vue';
import ComfirmDialog from '@/view/other/linkKlg/ComfirmDialog.vue';
import QuestionDetailDrawer from '@/components/QuestionDetailDrawer.vue';
import ContentRenderer from '@/components/ContentRenderer.vue';
import { emitter } from '@/utils/emitter';

import { Event } from '@/types/event';
import {
  getKlgDetailApi,
  getProofBlockListApi,
  getKlgRefListApi,
  getQuesDetailApi
} from '@/apis/path/klg';
import type { AreaListItem, ProofListItem, RefListItem } from '@/utils/type';
import {
  onMounted,
  ref,
  watch,
  shallowRef,
  toRaw,
  onUnmounted,
  triggerRef,
  nextTick,
  computed
} from 'vue';
import { findKeyByValue } from '@/utils/func';
import {
  KlgType,
  KlgTypeDict,
  WorkerType,
  klgAuditType,
  klgAuditTypeDict,
  proofCondTypeDict
} from '@/utils/constant';
import { storeToRefs } from 'pinia';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { getQuestionListApi, type QuestionData } from '@/apis/path/klg';

import { userInfoStore } from '@/stores/userInfo';
import {
  handleProof,
  transferList2ProofList,
  convertProofContentToStructure
} from '@/utils/lineWordFunction';
import { convertMathTagsToMDLatex } from '@/utils/latexUtils';
// 引入新的 composables
import { useQuestionIcon } from '@/composables/useQuestionIcon';
import { useRenderManager } from '@/composables/useRenderManager';
import { useDrawerManager } from '@/composables/useDrawerManager';

const userStore = userInfoStore();

const drawerControllerStore = useDrawerControllerStore();
const { mode } = storeToRefs(drawerControllerStore);
const curWordModeText = ref();
const wordContent = ref();
const underLineProofHtml = ref();
const questionList = shallowRef<QuestionData[]>([]);

// 存储处理后的内容HTML
const mainContentHTML = ref<any>();
const proofContentHTML = ref<any[]>([]);

// 使用抽屉管理 composable
const { initializeEventListeners, cleanupEventListeners } = useDrawerManager();

// 浮动弹窗相关状态
const floatingVisible = ref(false);
const floatingStyles = ref({});
const sameQuestionList = ref<any[]>([]);
const floatingElement = ref<HTMLElement>();

// 使用问题图标管理 composable
const {
  questionIconVisible,
  questionIconPosition,
  showQuestionIcon,
  handleQuestionIconClick,
  handleDocumentClick,
  questionIconElement
} = useQuestionIcon({
  onIconClick: (selectedText: string) => {
    handleAddQuestion(selectedText);
  }
});

// 计算属性：将proofContentHTML转换为结构化数据用于展示
const structuredProofList = computed(() => {
  if (proofContentHTML.value && proofContentHTML.value.length > 0) {
    const converted = convertProofContentToStructure(proofContentHTML.value);
    return converted;
  }
  return dataForm.value.proofList || [];
});

// 统一的Render管理器 - 支持数组输入
const mainRenderManager = useRenderManager({
  containerSelector: '#underline',
  questionList,
  getContentData: () => {
    // 如果是原理类型，合并证明过程内容数组
    if (dataForm.value.type === KlgType.Principles && underLineProofHtml.value) {
      if (Array.isArray(underLineProofHtml.value)) {
        // 如果是数组，使用展开运算符合并
        const contentArray = [wordContent.value];
        contentArray.push(...underLineProofHtml.value);
        return contentArray;
      }
    } else {
      return wordContent.value;
    }
  },
  onSelect: (data: any) => {
    // 当选中文本时，显示问号图标而不是直接打开弹窗
    // if (data && data.content) {
    //   showQuestionIcon(data);
    // } else {
    //   console.log('❌ 选中文本为空或无效');
    // }
  },
  onClick: (data: any) => {
    // 修改为先显示浮动弹窗，再选择具体问题
    handleShowFloatingElement(data.target);
  },
  onFinish: (arg: any) => {
    const content = arg.content;
    console.log('✅ 渲染完成:', content);
    if (dataForm.value.type === KlgType.Principles && underLineProofHtml.value) {
      // 如果是原理类型，分离主内容和证明过程内容
      mainContentHTML.value = content[0];
      proofContentHTML.value = content.slice(1);
    } else {
      mainContentHTML.value = content;
    }
  }
  // enableDebugLog: true
});

const props = defineProps({
  editable: Boolean,
  klgCode: String
});
const dataForm = ref({
  klgCode: '',
  title: '',
  type: -1,
  author: '',
  saveTime: '',
  synList: [] as String[],
  areaList: [] as AreaListItem[],
  refList: [] as RefListItem[],
  proofList: [] as ProofListItem[],
  note: '',
  cnt: ''
});
const drawerRef = ref();
const curStatus = ref();
const isLoadingQuestions = ref(false);
const lastKlgCode = ref('');

const handleQuestionList = async (klgCode: string) => {
  // 防止watch监听重复调用 getQuestionListApi
  if (isLoadingQuestions.value || lastKlgCode.value === klgCode) {
    return;
  }

  isLoadingQuestions.value = true;
  lastKlgCode.value = klgCode;

  try {
    const res = await getQuestionListApi(klgCode);
    questionList.value = res.data;

    // 如果是原理类型，处理证明过程
    if (dataForm.value.type === KlgType.Principles) {
      underLineProofHtml.value = handleProof(dataForm.value.proofList);
    }

    await mainRenderManager.initializeRender();
  } finally {
    isLoadingQuestions.value = false;
  }
};

// 获取klg详情
const getKlgDetail = async (): Promise<Boolean> => {
  let flag = false;
  try {
    if (props.klgCode) {
      const res = await getKlgDetailApi(props.klgCode);
      // console.log("获取klg详情res:", res)
      if (res.success) {
        emitter.emit(Event.SET_STATUS, res.data.detail.status);
        curStatus.value = res.data.detail.status;
        dataForm.value.klgCode = res.data.detail.klgCode;
        dataForm.value.title = res.data.detail.title;
        dataForm.value.type = res.data.detail.sortId;
        dataForm.value.cnt = res.data.detail.cnt;

        wordContent.value = dataForm.value.cnt; //对文稿做处理
        // wordStore.setConstContent(dataForm.value.cnt)

        dataForm.value.note = res.data.detail.notice;
        dataForm.value.author = res.data.detail.creatorName;
        dataForm.value.saveTime = res.data.detail.modifiedTime;
        //dataForm.value.processedCnt = res.data.detail.processedCnt
        if (res.data.detail.sysTitles !== '') {
          dataForm.value.synList = res.data.detail.sysTitles.split('@@');
        }
        dataForm.value.areaList = res.data.areaList.map((item: any) => {
          return {
            areaCode: item.areaCode,
            label: item.title
          };
        });

        if (dataForm.value.type === KlgType.Principles) {
          await getProofList().then((result) => {
            if (result) {
              handleQuestionList(dataForm.value.klgCode);
              flag = true;
            }
          });
        } else {
          handleQuestionList(dataForm.value.klgCode);
          flag = true;
        }
      }
    }
  } catch (error) {
    console.log(error);
  }
  return flag;
};
// 获取审核信息
const getAuditInfo = () => {
  if (props.klgCode) {
    getKlgRefListApi(props.klgCode).then((res) => {
      if (res.success) {
        dataForm.value.refList = res.data.klgToRefVoList;
      }
    });
  }
};
// 获取论证块列表
const getProofList = async (): Promise<Boolean> => {
  if (props.klgCode) {
    const res = await getProofBlockListApi(props.klgCode);
    if (res.success) {
      dataForm.value.proofList = res.data.klgProofBlocks;
      return true;
    }
  }
  return false;
};
// 问号图标相关函数已移至 useQuestionIcon composable

// 显示浮动弹窗元素 - 优化版本，避免重复请求
const handleShowFloatingElement = async (questionData: HTMLElement) => {
  if (!questionData) {
    console.warn('⚠️ questionData 为空，跳过处理');
    return;
  }

  // 从 questionData 元素中获取 data-qid
  const qidAttr = questionData.getAttribute('data-qid');
  if (!qidAttr) {
    console.error('❌ 未找到问题ID属性');
    return;
  }

  const qids = qidAttr.split(',');

  try {
    // 获取问题详情
    const res = await getQuesDetailApi(qids);

    if (res.success && res.data.list && res.data.list.length > 0) {
      const questionList = res.data.list;
      // 无论有多少个问题，都先显示浮动选择弹窗
      sameQuestionList.value = questionList;

      // 计算浮动弹窗位置
      const rect = questionData.getBoundingClientRect();
      floatingStyles.value = {
        position: 'fixed',
        left: rect.left + 'px',
        top: rect.bottom + 5 + 'px',
        zIndex: 10001
      };

      // 显示浮动弹窗
      floatingVisible.value = true;
    } else {
      console.warn('⚠️ 未获取到有效的问题数据');
    }
  } catch (error) {
    console.error('❌ 获取问题详情失败:', error);
  }
};

// 处理浮动弹窗中问题的点击事件
const handleFloatingQuestionClick = (question: any) => {
  // 隐藏浮动弹窗
  floatingVisible.value = false;

  // 触发显示答案抽屉的自定义事件
  const showAnswerEvent = new CustomEvent('showAnswerFromFloating', {
    detail: { question }
  });
  window.dispatchEvent(showAnswerEvent);
};

// 处理点击外部区域隐藏浮动弹窗
const handleDocumentClickForFloating = (event: MouseEvent) => {
  if (floatingVisible.value && floatingElement.value) {
    const target = event.target as HTMLElement;
    // 如果点击的不是浮动弹窗内部，则隐藏弹窗
    if (!floatingElement.value.contains(target)) {
      floatingVisible.value = false;
    }
  }
};

// 处理添加问题
const handleAddQuestion = (words: string) => {
  const data = {
    mode: 1,
    associatedWords: words,
    keyword: convertMathTagsToMDLatex(words),
    klgCode: dataForm.value.klgCode,
    answers: [] // 添加空的answers数组，避免ComfirmDialog中的undefined错误
  };
  handleQuesDialog(data);
};

const handleQuesDialog = (data: any) => {
  emitter.emit(Event.SHOW_QUESTION_DIALOG, data);
};

// 处理打开drawer
const handleDrawer = () => {
  drawerRef.value.showDrawer(dataForm.value.note);
};
// 处理重新
const handleReEdit = () => {
  window.open(`/editklg?klgCode=${dataForm.value.klgCode}`, '_self');
};

const addQuestionFn = (data: any) => {
  // 使用统一的RenderManager的addQuestion方法添加问题
  mainRenderManager.addQuestion(data.associatedWords, Number(data.questionId));

  // 更新questionList
  questionList.value?.push({
    questionId: data.questionId,
    associatedWords: data.associatedWords
  });

  emitter.emit(Event.REFRESH_QUESTION, true);
};

// 删除问题
const removeQuestionFn = (questionId: string) => {
  // 找到要删除的问题
  const rawQuestionList = toRaw(questionList.value);
  const questionIndex = rawQuestionList?.findIndex((item) => item.questionId == questionId);

  if (questionIndex !== undefined && questionIndex !== -1 && rawQuestionList) {
    const questionToRemove = rawQuestionList[questionIndex];

    // 使用统一的RenderManager的removeQuestion方法删除问题
    mainRenderManager.removeQuestion(questionToRemove.associatedWords, Number(questionId));

    // 从问题列表中移除
    rawQuestionList.splice(questionIndex, 1);
    triggerRef(questionList);
  }
};
// 抽屉点击事件监听已移除，使用新的 QuestionDetailDrawer 组件

// 处理 showAnswerDrawer 自定义事件 - 显示问题详情抽屉
const handleShowAnswerDrawer = (event: CustomEvent) => {
  const { questionData } = event.detail;

  // 从 questionData 元素中获取 data-qid
  const qidAttr = questionData.getAttribute('data-qid');
  if (!qidAttr) {
    console.log('❌ 没有找到 data-qid 属性');
    return;
  }

  const qids = qidAttr.split(',');

  // 获取问题详情并显示新的问题详情抽屉
  getQuesDetailApi(qids)
    .then((res) => {
      // 触发显示问题详情抽屉的事件
      const showDetailEvent = new CustomEvent('showQuestionDetail', {
        detail: { questions: res.data.list }
      });
      window.dispatchEvent(showDetailEvent);
      console.log('✅ 问题详情抽屉已显示');
    })
    .catch((error) => {
      console.error('❌ 获取问题详情失败:', error);
    });
};

onMounted(async () => {
  // 注册事件监听
  emitter.on(Event.ADD_QUESTION, addQuestionFn);
  emitter.on(Event.REMOVE_QUESTION, removeQuestionFn);

  // 初始化抽屉管理器的事件监听
  initializeEventListeners();

  // 添加问号图标的全局点击事件监听
  document.addEventListener('click', handleDocumentClick as EventListener, true);
  // 添加浮动弹窗的全局点击事件监听
  document.addEventListener('click', handleDocumentClickForFloating as EventListener, true);

  // 等待DOM渲染完成
  await nextTick();
});

onUnmounted(() => {
  emitter.off(Event.ADD_QUESTION, addQuestionFn);
  emitter.off(Event.REMOVE_QUESTION, removeQuestionFn);
  cleanupEventListeners();
  document.removeEventListener('click', handleDocumentClick as EventListener, true);
  document.removeEventListener('click', handleDocumentClickForFloating as EventListener, true);
  mainRenderManager.destroyRenderInstance();
});

watch(
  () => props,
  () => {
    if (props.klgCode && props.klgCode !== '' && props.klgCode !== null) {
      getKlgDetail();
      getAuditInfo();
    }
  },
  { deep: true, immediate: true }
);

// 观察划词模式转换（现在只用于UI显示）
watch(
  () => mode,
  (newValue) => {
    curWordModeText.value = newValue.value === true ? '提问模式' : '展示模式';
  },
  { deep: true, immediate: true }
);
defineExpose({
  handleQuestionList,
  questionList
});
</script>
<template>
  <div class="wrapper">
    <div class="up-wrapper">
      <div class="header">
        <span class="header-left">
          <span class="header-title"><span class="ck-content" v-html="dataForm.title"></span></span>

          <span class="header-type">{{ findKeyByValue(dataForm.type, KlgTypeDict) }}</span>
        </span>
        <span class="header-right" @click="handleDrawer">
          <img src="@/assets/image/klg/u1695.svg" />
          <span class="detail-info-right-text">查看编者笔记</span>
        </span>
        <span
          class="header-right"
          v-if="
            curStatus !== klgAuditType.published &&
            curStatus !== klgAuditType.reviewing &&
            curStatus !== klgAuditType.pending &&
            dataForm.author === userStore.getUsername()
          "
          @click="handleReEdit"
        >
          <img src="@/assets/image/klg/u2538.svg" />
          <span class="header-right-text">重新编辑</span>
        </span>
      </div>
      <div class="base-info" style="margin: 5px 0">
        <span class="author-info">
          <span style="margin-right: 10px">作者</span>
          <span>{{ dataForm.author }}</span>
        </span>
        <span class="save-time">
          <span style="margin-right: 10px">保存时间</span>
          <span>{{ dataForm.saveTime }}</span>
        </span>
      </div>
      <div class="detail-info">
        <span class="detail-info-left">
          <span class="info-syn"
            ><span style="white-space: nowrap; margin-right: 75px">同义词</span>
            <div
              v-if="dataForm.synList.length === 0"
              style="white-space: nowrap; color: var(--color-grey)"
            >
              暂无同义词
            </div>
            <div v-else class="info-syn-text-block">
              <el-tag
                effect="plain"
                :disable-transitions="true"
                class="info-syn-text primary"
                v-for="(item, index) in dataForm.synList"
                :key="index"
              >
                <span class="ck-content" v-html="item"> </span>
              </el-tag>
            </div>
          </span>
          <span class="info-syn">
            <span style="white-space: nowrap; margin-right: 60px">所属领域</span>
            <div class="info-syn-text-block" style="margin-top: 5px">
              <el-tag
                effect="plain"
                :disable-transitions="true"
                class="info-syn-text primary"
                v-for="(item, index) in dataForm.areaList"
                :key="index"
              >
                <span class="ck-content" v-html="item.label"> </span>
              </el-tag>
            </div>
          </span>
        </span>
        <span class="detail-info-right">
          {{ findKeyByValue(curStatus, klgAuditTypeDict) || '未知状态' }}
        </span>
      </div>
      <div style="margin-top: 5px" class="line"></div>
      <div class="main-container" id="underline">
        <div class="content-container">
          <ContentRenderer :content="mainContentHTML" />
        </div>
        <div class="proof-container" v-if="dataForm.type === KlgType.Principles">
          <div class="proof-container-title">证明过程</div>
          <div class="proof-block-list">
            <div>
              <div
                class="proof-block"
                v-for="(block, blockIndex) in structuredProofList"
                :key="blockIndex"
              >
                <div
                  class="proof-block-item"
                  v-for="(cond, condIndex) in block.klgProofCondList"
                  :key="condIndex"
                >
                  <span class="proof-item-label">{{
                    findKeyByValue(cond.sort ?? 2, proofCondTypeDict)
                  }}</span>
                  <span class="proof-item-content"> <span v-html="cond.cnt"> </span></span>
                </div>
                <div class="proof-block-item">
                  <span class="proof-item-label">论证结论</span>
                  <span class="proof-item-content"><span v-html="block.conclusion"></span></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="down-wrapper" v-if="dataForm.refList.length != 0">
      <div class="ref-container-title">参考文献</div>

      <div class="ref-list">
        <span class="ref-line" v-for="ref in dataForm.refList" :key="ref.refId">
          <span class="ref-line-name">{{ ref.cntName }}</span>
          <span class="ref-line-chapter">{{ ref.indexPage ? ref.indexPage : '暂无信息' }}</span>
        </span>
      </div>
    </div>
  </div>
  <!-- other -->
  <NoteDrawer ref="drawerRef"></NoteDrawer>
  <QuestionDetailDrawer />

  <!-- 浮动弹窗 -->
  <div
    ref="floatingElement"
    :style="
      floatingVisible ? floatingStyles : { position: 'fixed', top: '-9999px', left: '-9999px' }
    "
    class="floatingContainer"
  >
    <Transition name="scale">
      <div
        v-if="floatingVisible && sameQuestionList && sameQuestionList.length > 0"
        class="floating-content"
      >
        <div
          v-for="question in sameQuestionList"
          :key="question.questionId"
          class="floating-content-item"
          @click="handleFloatingQuestionClick(question)"
        >
          <div style="display: flex; align-items: center">
            <span class="keyword-container">
              【
              <span
                class="questionList ellipsis-text-inline"
                style="word-break: break-all"
                v-html="question.keyword"
              ></span>
              】
            </span>
            <span v-if="question.questionType != '开放性问题'">{{ question.questionType }}</span>
            <span v-else v-html="question.questionDescription"></span>
          </div>
        </div>
      </div>
    </Transition>
  </div>

  <!-- 问号图标 - 统一管理，避免重复 -->
  <div
    v-if="questionIconVisible"
    ref="questionIconElement"
    class="question-icon"
    :style="{
      position: 'fixed',
      left: questionIconPosition.x + 'px',
      top: questionIconPosition.y + 'px',
      zIndex: 10000
    }"
    @click="handleQuestionIconClick"
  >
    <!-- 悬浮提示 -->
    <div class="question-tooltip">提问</div>
    <!-- 问号图标 -->
    <div class="question-icon-circle">
      <img src="@/assets/question.svg" alt="提问" />
    </div>
  </div>
</template>

<style scoped src="./css/KlgInfo.less"></style>
