<script setup lang="ts">
import { useRouter } from 'vue-router';
import { onMounted, ref, watch } from 'vue';
import { getLeftMenu, menuList } from '@/utils/defaultList';

import task0 from '@/assets/image/left/task_0.svg';
import task1 from '@/assets/image/left/task_1.svg';
import doc0 from '@/assets/image/left/doc_0.svg';
import doc1 from '@/assets/image/left/doc_1.svg';
import audit0 from '@/assets/image/left/audit_0.svg';
import audit1 from '@/assets/image/left/audit_1.svg';
import klg0 from '@/assets/image/left/klg_0.svg';
import klg1 from '@/assets/image/left/klg_1.svg';
import per0 from '@/assets/image/left/permission_0.svg';
import per1 from '@/assets/image/left/permission_1.svg';
import domain0 from '@/assets/image/left/domain_0.svg';
import domain1 from '@/assets/image/left/domain_1.svg';

const router = useRouter();
const menus = ref<any[]>([]);

onMounted(() => {
  menus.value = getLeftMenu();
});

// 根据当前路由和菜单项状态返回图片
const getImageSrc = (menu: any) => {
  switch (menu.path) {
    case '/task':
      return menu.isActive ? task1 : task0;
    case '/klg':
      return menu.isActive ? klg1 : klg0;
    case '/document':
      return menu.isActive ? doc1 : doc0;
    case '/permission':
      return menu.isActive ? per1 : per0;
    case '/audit':
      return menu.isActive ? audit1 : audit0;
    case '/domain':
      return menu.isActive ? domain1 : domain0;
    default:
      return;
  }
};
// 监听路由变化，更新菜单项的 isActive 状态
watch(
  () => router.currentRoute.value,
  (newRoute) => {
    menus.value.forEach((item) => {
      if (item) {
        item.isActive = newRoute.path.startsWith(item.path);
      }
    });
  }
);
// 路由跳转
const routerPush = (targetRoute: string) => {
  if (targetRoute === undefined) {
    alert('路由跳转失败');
  } else {
    router.push(targetRoute);
  }
};
</script>
<template>
  <div class="wrapper">
    <div class="menu">
      <el-menu :default-active="$route.path" class="el-menu" :unique-opened="true">
        <div v-for="menu in menus" :key="menu.path">
          <el-sub-menu :index="menu.path">
            <template #title>
              <div>
                <img :src="getImageSrc(menu)" alt="" />
                <span class="el-submenu__title">{{ menu.title }}</span>
              </div>
            </template>
            <div v-for="item in menu.children" :key="item.path">
              <el-menu-item :index="item.path" @click="routerPush(item.path)">{{
                item.title
              }}</el-menu-item>
            </div>
          </el-sub-menu>
        </div>
      </el-menu>
    </div>
    <div class="footer">© 无尽本源 版权所有</div>
  </div>
</template>
<style scoped lang="less">
* {
  vertical-align: baseline;
}

.wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  width: 200px;
  background-color: white;

  .menu {
    width: 100%;
    overflow-y: auto;

    /* 添加自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 3px;
      /* 设置滚动条宽度 */
    }

    &::-webkit-scrollbar-thumb {
      background-color: white;
      /* 设置滚动条thumb颜色 */
      border-radius: 6px;
      /* 设置滚动条thumb圆角 */
      height: 50px;
      /* 设置滚动条的高度 */
    }

    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道颜色 */
    }

    .el-menu {
      display: flex;
      flex-direction: column;
      border: 0;

      .el-submenu__title {
        margin-left: 20px;
      }

      .el-sub-menu {
        &:deep(.el-sub-menu__title) {
          font-family:
            '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0',
            sans-serif;
          font-size: 16px;
          font-weight: 400;
          color: #333333;
          display: flex;
          justify-content: center;
          // height: 500px !important;
          // line-height: 50px !important;`
        }

        &:deep(.el-sub-menu__title:hover) {
          background-color: #f8f8f8f7;
        }

        &:deep(.el-menu-item) {
          font-family:
            '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0',
            sans-serif;
          font-size: 16px;
          font-weight: 400;
          color: #666666;

          display: flex;
          align-items: center;
          justify-content: center;
          //需要设置，因为element自带的里面有padding值
          padding: 0;
          margin-left: 7px;
        }

        &:deep(.el-menu-item:hover) {
          background-color: #f8f8f8f7;
        }

        &:deep(.el-menu-item.is-active) {
          color: var(--color-primary);
          background-color: #f8f8f8f7;
        }
      }

      .el-sub-menu.is-active {
        &:deep(.el-sub-menu__title) {
          color: var(--color-primary);
        }
      }
    }
  }

  .footer {
    position: absolute;
    bottom: 0;
    height: 68px;
    border-top: solid 2px #d8e1e9;
    background-color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: var(--color-grey);

    &::after {
      content: '';
      position: absolute;
      top: 0;
      width: 100%;
      height: 1px;
      margin: 0 20px;
      background-color: var(--color-line);
    }
  }
}
</style>
