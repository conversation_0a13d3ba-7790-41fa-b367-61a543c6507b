/*!
  Theme: Codeschool
  Author: blockloop
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#9ea7a6;background:#232c31}.hljs ::selection,.hljs::selection{background-color:#2a343a;color:#9ea7a6}.hljs-comment{color:#3f4944}.hljs-tag{color:#84898c}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#9ea7a6}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#2a5491}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#43820d}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#a03b1e}.hljs-strong{font-weight:700;color:#a03b1e}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#237986}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#b02f30}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#484d79}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#c59820}.hljs-emphasis{color:#c59820;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#c98344}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
