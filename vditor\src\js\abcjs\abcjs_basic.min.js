/*! abcjs_basic v5.10.3 Copyright © 2009-2019 <PERSON> and <PERSON> (https://abcjs.net) */
/*! For license information please see abcjs_basic_5.10.3-min.js.LICENSE */
!function(e){var t={};function i(r){if(t[r])return t[r].exports;var n=t[r]={i:r,l:!1,exports:{}};return e[r].call(n.exports,n,n.exports,i),n.l=!0,n.exports}i.m=e,i.c=t,i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)i.d(r,n,function(t){return e[t]}.bind(null,n));return r},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=26)}([function(e,t){var i={clone:function(e){var t={};for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t},cloneArray:function(e){for(var t=[],r=0;r<e.length;r++)t.push(i.clone(e[r]));return t},cloneHashOfHash:function(e){var t={};for(var r in e)e.hasOwnProperty(r)&&(t[r]=i.clone(e[r]));return t},cloneHashOfArrayOfHash:function(e){var t={};for(var r in e)e.hasOwnProperty(r)&&(t[r]=i.cloneArray(e[r]));return t},gsub:function(e,t,i){return e.split(t).join(i)},strip:function(e){return e.replace(/^\s+/,"").replace(/\s+$/,"")},startsWith:function(e,t){return 0===e.indexOf(t)},endsWith:function(e,t){var i=e.length-t.length;return i>=0&&e.lastIndexOf(t)===i},each:function(e,t,i){for(var r=0,n=e.length;r<n;r++)t.apply(i,[e[r],r])},last:function(e){return 0===e.length?null:e[e.length-1]},compact:function(e){for(var t=[],i=0;i<e.length;i++)e[i]&&t.push(e[i]);return t},detect:function(e,t){for(var i=0;i<e.length;i++)if(t(e[i]))return!0;return!1}};[Element.prototype,CharacterData.prototype,DocumentType.prototype].forEach((function(e){e.hasOwnProperty("remove")||Object.defineProperty(e,"remove",{configurable:!0,enumerable:!0,writable:!0,value:function(){null!==this.parentNode&&this.parentNode.removeChild(this)}})})),e.exports=i},function(e,t){var i={FONTEM:360,FONTSIZE:30};i.STEP=93*i.FONTSIZE/720,i.SPACE=10,i.TOPNOTE=15,i.STAVEHEIGHT=100,i.INDENT=50,e.exports=i},function(e,t,i){i(3);var r=function(e,t,i,r,n){switch(n=n||{},this.x=0,this.c=e,this.dx=t,this.w=i,this.pitch=r,this.scalex=n.scalex||1,this.scaley=n.scaley||1,this.type=n.type||"symbol",this.pitch2=n.pitch2,this.linewidth=n.linewidth,this.klass=n.klass,this.top=r,void 0!==this.pitch2&&this.pitch2>this.top&&(this.top=this.pitch2),this.bottom=r,void 0!==this.pitch2&&this.pitch2<this.bottom&&(this.bottom=this.pitch2),n.thickness&&(this.top+=n.thickness/2,this.bottom-=n.thickness/2),n.stemHeight&&(n.stemHeight>0?this.top+=n.stemHeight:this.bottom+=n.stemHeight),this.height=n.height?n.height:4,this.centerVertically=!1,this.type){case"debug":this.chordHeightAbove=this.height;break;case"lyric":n.position&&"below"===n.position?this.lyricHeightBelow=this.height:this.lyricHeightAbove=this.height;break;case"chord":n.position&&"below"===n.position?this.chordHeightBelow=this.height:this.chordHeightAbove=this.height;break;case"text":void 0===this.pitch?n.position&&"below"===n.position?this.chordHeightBelow=this.height:this.chordHeightAbove=this.height:this.centerVertically=!0;break;case"part":this.partHeightAbove=this.height}};r.prototype.setX=function(e){this.x=e+this.dx},r.prototype.setUpperAndLowerElements=function(e){switch(this.type){case"part":this.top=e.partHeightAbove+this.height,this.bottom=e.partHeightAbove;break;case"text":case"chord":this.chordHeightAbove?(this.top=e.chordHeightAbove,this.bottom=e.chordHeightAbove):(this.top=e.chordHeightBelow,this.bottom=e.chordHeightBelow);break;case"lyric":this.lyricHeightAbove?(this.top=e.lyricHeightAbove,this.bottom=e.lyricHeightAbove):(this.top=e.lyricHeightBelow,this.bottom=e.lyricHeightBelow);break;case"debug":this.top=e.chordHeightAbove,this.bottom=e.chordHeightAbove}void 0!==this.pitch&&void 0!==this.top||window.console.error("RelativeElement position not set.",this.type,this.pitch,this.top,e)},r.prototype.draw=function(e,t){void 0===this.pitch&&window.console.error(this.type+" Relative Element y-coordinate not set.");var i=e.calcY(this.pitch);switch(this.type){case"symbol":if(null===this.c)return null;var r="symbol";this.klass&&(r+=" "+this.klass),this.graphelem=e.printSymbol(this.x,this.pitch,this.c,this.scalex,this.scaley,e.addClasses(r));break;case"debug":this.graphelem=e.renderText(this.x,e.calcY(15),""+this.c,"debugfont","debug-msg","start");break;case"barNumber":this.graphelem=e.renderText(this.x,i,""+this.c,"measurefont","bar-number","middle");break;case"lyric":this.graphelem=e.renderText(this.x,i,this.c,"vocalfont","lyric","middle");break;case"chord":this.graphelem=e.renderText(this.x,i,this.c,"gchordfont","chord","middle");break;case"decoration":this.graphelem=e.renderText(this.x,i,this.c,"annotationfont","annotation","middle",!0);break;case"text":this.graphelem=e.renderText(this.x,i,this.c,"annotationfont","annotation","start",this.centerVertically);break;case"multimeasure-text":this.graphelem=e.renderText(this.x+this.w/2,i,this.c,"tempofont","rest","middle",!1);break;case"part":this.graphelem=e.renderText(this.x,i,this.c,"partsfont","part","start");break;case"bar":this.graphelem=e.printStem(this.x,this.linewidth,i,t||e.calcY(this.pitch2));break;case"stem":this.graphelem=e.printStem(this.x,this.linewidth,i,e.calcY(this.pitch2));break;case"ledger":this.graphelem=e.printStaveLine(this.x,this.x+this.w,this.pitch)}return 1!==this.scalex&&this.graphelem&&e.scaleExistingElem(this.graphelem,this.scalex,this.scaley,this.x,i),this.graphelem},e.exports=r},function(e,t,i){var r=i(1);e.exports=new function(){"use strict";var e={0:{d:[["M",4.83,-14.97],["c",.33,-.03,1.11,0,1.47,.06],["c",1.68,.36,2.97,1.59,3.78,3.6],["c",1.2,2.97,.81,6.96,-.9,9.27],["c",-.78,1.08,-1.71,1.71,-2.91,1.95],["c",-.45,.09,-1.32,.09,-1.77,0],["c",-.81,-.18,-1.47,-.51,-2.07,-1.02],["c",-2.34,-2.07,-3.15,-6.72,-1.74,-10.2],["c",.87,-2.16,2.28,-3.42,4.14,-3.66],["z"],["m",1.11,.87],["c",-.21,-.06,-.69,-.09,-.87,-.06],["c",-.54,.12,-.87,.42,-1.17,.99],["c",-.36,.66,-.51,1.56,-.6,3],["c",-.03,.75,-.03,4.59,0,5.31],["c",.09,1.5,.27,2.4,.6,3.06],["c",.24,.48,.57,.78,.96,.9],["c",.27,.09,.78,.09,1.05,0],["c",.39,-.12,.72,-.42,.96,-.9],["c",.33,-.66,.51,-1.56,.6,-3.06],["c",.03,-.72,.03,-4.56,0,-5.31],["c",-.09,-1.47,-.27,-2.37,-.6,-3.03],["c",-.24,-.48,-.54,-.78,-.93,-.9],["z"]],w:10.78,h:14.959},1:{d:[["M",3.3,-15.06],["c",.06,-.06,.21,-.03,.66,.15],["c",.81,.39,1.08,.39,1.83,.03],["c",.21,-.09,.39,-.15,.42,-.15],["c",.12,0,.21,.09,.27,.21],["c",.06,.12,.06,.33,.06,5.94],["c",0,3.93,0,5.85,.03,6.03],["c",.06,.36,.15,.69,.27,.96],["c",.36,.75,.93,1.17,1.68,1.26],["c",.3,.03,.39,.09,.39,.3],["c",0,.15,-.03,.18,-.09,.24],["c",-.06,.06,-.09,.06,-.48,.06],["c",-.42,0,-.69,-.03,-2.1,-.24],["c",-.9,-.15,-1.77,-.15,-2.67,0],["c",-1.41,.21,-1.68,.24,-2.1,.24],["c",-.39,0,-.42,0,-.48,-.06],["c",-.06,-.06,-.06,-.09,-.06,-.24],["c",0,-.21,.06,-.27,.36,-.3],["c",.75,-.09,1.32,-.51,1.68,-1.26],["c",.12,-.27,.21,-.6,.27,-.96],["c",.03,-.18,.03,-1.59,.03,-4.29],["c",0,-3.87,0,-4.05,-.06,-4.14],["c",-.09,-.15,-.18,-.24,-.39,-.24],["c",-.12,0,-.15,.03,-.21,.06],["c",-.03,.06,-.45,.99,-.96,2.13],["c",-.48,1.14,-.9,2.1,-.93,2.16],["c",-.06,.15,-.21,.24,-.33,.24],["c",-.24,0,-.42,-.18,-.42,-.39],["c",0,-.06,3.27,-7.62,3.33,-7.74],["z"]],w:8.94,h:15.058},2:{d:[["M",4.23,-14.97],["c",.57,-.06,1.68,0,2.34,.18],["c",.69,.18,1.5,.54,2.01,.9],["c",1.35,.96,1.95,2.25,1.77,3.81],["c",-.15,1.35,-.66,2.34,-1.68,3.15],["c",-.6,.48,-1.44,.93,-3.12,1.65],["c",-1.32,.57,-1.8,.81,-2.37,1.14],["c",-.57,.33,-.57,.33,-.24,.27],["c",.39,-.09,1.26,-.09,1.68,0],["c",.72,.15,1.41,.45,2.1,.9],["c",.99,.63,1.86,.87,2.55,.75],["c",.24,-.06,.42,-.15,.57,-.3],["c",.12,-.09,.3,-.42,.3,-.51],["c",0,-.09,.12,-.21,.24,-.24],["c",.18,-.03,.39,.12,.39,.3],["c",0,.12,-.15,.57,-.3,.87],["c",-.54,1.02,-1.56,1.74,-2.79,2.01],["c",-.42,.09,-1.23,.09,-1.62,.03],["c",-.81,-.18,-1.32,-.45,-2.01,-1.11],["c",-.45,-.45,-.63,-.57,-.96,-.69],["c",-.84,-.27,-1.89,.12,-2.25,.9],["c",-.12,.21,-.21,.54,-.21,.72],["c",0,.12,-.12,.21,-.27,.24],["c",-.15,0,-.27,-.03,-.33,-.15],["c",-.09,-.21,.09,-1.08,.33,-1.71],["c",.24,-.66,.66,-1.26,1.29,-1.89],["c",.45,-.45,.9,-.81,1.92,-1.56],["c",1.29,-.93,1.89,-1.44,2.34,-1.98],["c",.87,-1.05,1.26,-2.19,1.2,-3.63],["c",-.06,-1.29,-.39,-2.31,-.96,-2.91],["c",-.36,-.33,-.72,-.51,-1.17,-.54],["c",-.84,-.03,-1.53,.42,-1.59,1.05],["c",-.03,.33,.12,.6,.57,1.14],["c",.45,.54,.54,.87,.42,1.41],["c",-.15,.63,-.54,1.11,-1.08,1.38],["c",-.63,.33,-1.2,.33,-1.83,0],["c",-.24,-.12,-.33,-.18,-.54,-.39],["c",-.18,-.18,-.27,-.3,-.36,-.51],["c",-.24,-.45,-.27,-.84,-.21,-1.38],["c",.12,-.75,.45,-1.41,1.02,-1.98],["c",.72,-.72,1.74,-1.17,2.85,-1.32],["z"]],w:10.764,h:14.97},3:{d:[["M",3.78,-14.97],["c",.3,-.03,1.41,0,1.83,.06],["c",2.22,.3,3.51,1.32,3.72,2.91],["c",.03,.33,.03,1.26,-.03,1.65],["c",-.12,.84,-.48,1.47,-1.05,1.77],["c",-.27,.15,-.36,.24,-.45,.39],["c",-.09,.21,-.09,.36,0,.57],["c",.09,.15,.18,.24,.51,.39],["c",.75,.42,1.23,1.14,1.41,2.13],["c",.06,.42,.06,1.35,0,1.71],["c",-.18,.81,-.48,1.38,-1.02,1.95],["c",-.75,.72,-1.8,1.2,-3.18,1.38],["c",-.42,.06,-1.56,.06,-1.95,0],["c",-1.89,-.33,-3.18,-1.29,-3.51,-2.64],["c",-.03,-.12,-.03,-.33,-.03,-.6],["c",0,-.36,0,-.42,.06,-.63],["c",.12,-.3,.27,-.51,.51,-.75],["c",.24,-.24,.45,-.39,.75,-.51],["c",.21,-.06,.27,-.06,.6,-.06],["c",.33,0,.39,0,.6,.06],["c",.3,.12,.51,.27,.75,.51],["c",.36,.33,.57,.75,.6,1.2],["c",0,.21,0,.27,-.06,.42],["c",-.09,.18,-.12,.24,-.54,.54],["c",-.51,.36,-.63,.54,-.6,.87],["c",.06,.54,.54,.9,1.38,.99],["c",.36,.06,.72,.03,.96,-.06],["c",.81,-.27,1.29,-1.23,1.44,-2.79],["c",.03,-.45,.03,-1.95,-.03,-2.37],["c",-.09,-.75,-.33,-1.23,-.75,-1.44],["c",-.33,-.18,-.45,-.18,-1.98,-.18],["c",-1.35,0,-1.41,0,-1.5,-.06],["c",-.18,-.12,-.24,-.39,-.12,-.6],["c",.12,-.15,.15,-.15,1.68,-.15],["c",1.5,0,1.62,0,1.89,-.15],["c",.18,-.09,.42,-.36,.54,-.57],["c",.18,-.42,.27,-.9,.3,-1.95],["c",.03,-1.2,-.06,-1.8,-.36,-2.37],["c",-.24,-.48,-.63,-.81,-1.14,-.96],["c",-.3,-.06,-1.08,-.06,-1.38,.03],["c",-.6,.15,-.9,.42,-.96,.84],["c",-.03,.3,.06,.45,.63,.84],["c",.33,.24,.42,.39,.45,.63],["c",.03,.72,-.57,1.5,-1.32,1.65],["c",-1.05,.27,-2.1,-.57,-2.1,-1.65],["c",0,-.45,.15,-.96,.39,-1.38],["c",.12,-.21,.54,-.63,.81,-.81],["c",.57,-.42,1.38,-.69,2.25,-.81],["z"]],w:9.735,h:14.967},4:{d:[["M",8.64,-14.94],["c",.27,-.09,.42,-.12,.54,-.03],["c",.09,.06,.15,.21,.15,.3],["c",-.03,.06,-1.92,2.31,-4.23,5.04],["c",-2.31,2.73,-4.23,4.98,-4.26,5.01],["c",-.03,.06,.12,.06,2.55,.06],["l",2.61,0],["l",0,-2.37],["c",0,-2.19,.03,-2.37,.06,-2.46],["c",.03,-.06,.21,-.18,.57,-.42],["c",1.08,-.72,1.38,-1.08,1.86,-2.16],["c",.12,-.3,.24,-.54,.27,-.57],["c",.12,-.12,.39,-.06,.45,.12],["c",.06,.09,.06,.57,.06,3.96],["l",0,3.9],["l",1.08,0],["c",1.05,0,1.11,0,1.2,.06],["c",.24,.15,.24,.54,0,.69],["c",-.09,.06,-.15,.06,-1.2,.06],["l",-1.08,0],["l",0,.33],["c",0,.57,.09,1.11,.3,1.53],["c",.36,.75,.93,1.17,1.68,1.26],["c",.3,.03,.39,.09,.39,.3],["c",0,.15,-.03,.18,-.09,.24],["c",-.06,.06,-.09,.06,-.48,.06],["c",-.42,0,-.69,-.03,-2.1,-.24],["c",-.9,-.15,-1.77,-.15,-2.67,0],["c",-1.41,.21,-1.68,.24,-2.1,.24],["c",-.39,0,-.42,0,-.48,-.06],["c",-.06,-.06,-.06,-.09,-.06,-.24],["c",0,-.21,.06,-.27,.36,-.3],["c",.75,-.09,1.32,-.51,1.68,-1.26],["c",.21,-.42,.3,-.96,.3,-1.53],["l",0,-.33],["l",-2.7,0],["c",-2.91,0,-2.85,0,-3.09,-.15],["c",-.18,-.12,-.3,-.39,-.27,-.54],["c",.03,-.06,.18,-.24,.33,-.45],["c",.75,-.9,1.59,-2.07,2.13,-3.03],["c",.33,-.54,.84,-1.62,1.05,-2.16],["c",.57,-1.41,.84,-2.64,.9,-4.05],["c",.03,-.63,.06,-.72,.24,-.81],["l",.12,-.06],["l",.45,.12],["c",.66,.18,1.02,.24,1.47,.27],["c",.6,.03,1.23,-.09,2.01,-.33],["z"]],w:11.795,h:14.994},5:{d:[["M",1.02,-14.94],["c",.12,-.09,.03,-.09,1.08,.06],["c",2.49,.36,4.35,.36,6.96,-.06],["c",.57,-.09,.66,-.06,.81,.06],["c",.15,.18,.12,.24,-.15,.51],["c",-1.29,1.26,-3.24,2.04,-5.58,2.31],["c",-.6,.09,-1.2,.12,-1.71,.12],["c",-.39,0,-.45,0,-.57,.06],["c",-.09,.06,-.15,.12,-.21,.21],["l",-.06,.12],["l",0,1.65],["l",0,1.65],["l",.21,-.21],["c",.66,-.57,1.41,-.96,2.19,-1.14],["c",.33,-.06,1.41,-.06,1.95,0],["c",2.61,.36,4.02,1.74,4.26,4.14],["c",.03,.45,.03,1.08,-.03,1.44],["c",-.18,1.02,-.78,2.01,-1.59,2.7],["c",-.72,.57,-1.62,1.02,-2.49,1.2],["c",-1.38,.27,-3.03,.06,-4.2,-.54],["c",-1.08,-.54,-1.71,-1.32,-1.86,-2.28],["c",-.09,-.69,.09,-1.29,.57,-1.74],["c",.24,-.24,.45,-.39,.75,-.51],["c",.21,-.06,.27,-.06,.6,-.06],["c",.33,0,.39,0,.6,.06],["c",.3,.12,.51,.27,.75,.51],["c",.36,.33,.57,.75,.6,1.2],["c",0,.21,0,.27,-.06,.42],["c",-.09,.18,-.12,.24,-.54,.54],["c",-.18,.12,-.36,.3,-.42,.33],["c",-.36,.42,-.18,.99,.36,1.26],["c",.51,.27,1.47,.36,2.01,.27],["c",.93,-.21,1.47,-1.17,1.65,-2.91],["c",.06,-.45,.06,-1.89,0,-2.31],["c",-.15,-1.2,-.51,-2.1,-1.05,-2.55],["c",-.21,-.18,-.54,-.36,-.81,-.39],["c",-.3,-.06,-.84,-.03,-1.26,.06],["c",-.93,.18,-1.65,.6,-2.16,1.2],["c",-.15,.21,-.27,.3,-.39,.3],["c",-.15,0,-.3,-.09,-.36,-.18],["c",-.06,-.09,-.06,-.15,-.06,-3.66],["c",0,-3.39,0,-3.57,.06,-3.66],["c",.03,-.06,.09,-.15,.15,-.18],["z"]],w:10.212,h:14.997},6:{d:[["M",4.98,-14.97],["c",.36,-.03,1.2,0,1.59,.06],["c",.9,.15,1.68,.51,2.25,1.05],["c",.57,.51,.87,1.23,.84,1.98],["c",-.03,.51,-.21,.9,-.6,1.26],["c",-.24,.24,-.45,.39,-.75,.51],["c",-.21,.06,-.27,.06,-.6,.06],["c",-.33,0,-.39,0,-.6,-.06],["c",-.3,-.12,-.51,-.27,-.75,-.51],["c",-.39,-.36,-.57,-.78,-.57,-1.26],["c",0,-.27,0,-.3,.09,-.42],["c",.03,-.09,.18,-.21,.3,-.3],["c",.12,-.09,.3,-.21,.39,-.27],["c",.09,-.06,.21,-.18,.27,-.24],["c",.06,-.12,.09,-.15,.09,-.33],["c",0,-.18,-.03,-.24,-.09,-.36],["c",-.24,-.39,-.75,-.6,-1.38,-.57],["c",-.54,.03,-.9,.18,-1.23,.48],["c",-.81,.72,-1.08,2.16,-.96,5.37],["l",0,.63],["l",.3,-.12],["c",.78,-.27,1.29,-.33,2.1,-.27],["c",1.47,.12,2.49,.54,3.27,1.29],["c",.48,.51,.81,1.11,.96,1.89],["c",.06,.27,.06,.42,.06,.93],["c",0,.54,0,.69,-.06,.96],["c",-.15,.78,-.48,1.38,-.96,1.89],["c",-.54,.51,-1.17,.87,-1.98,1.08],["c",-1.14,.3,-2.4,.33,-3.24,.03],["c",-1.5,-.48,-2.64,-1.89,-3.27,-4.02],["c",-.36,-1.23,-.51,-2.82,-.42,-4.08],["c",.3,-3.66,2.28,-6.3,4.95,-6.66],["z"],["m",.66,7.41],["c",-.27,-.09,-.81,-.12,-1.08,-.06],["c",-.72,.18,-1.08,.69,-1.23,1.71],["c",-.06,.54,-.06,3,0,3.54],["c",.18,1.26,.72,1.77,1.8,1.74],["c",.39,-.03,.63,-.09,.9,-.27],["c",.66,-.42,.9,-1.32,.9,-3.24],["c",0,-2.22,-.36,-3.12,-1.29,-3.42],["z"]],w:9.956,h:14.982},7:{d:[["M",.21,-14.97],["c",.21,-.06,.45,0,.54,.15],["c",.06,.09,.06,.15,.06,.39],["c",0,.24,0,.33,.06,.42],["c",.06,.12,.21,.24,.27,.24],["c",.03,0,.12,-.12,.24,-.21],["c",.96,-1.2,2.58,-1.35,3.99,-.42],["c",.15,.12,.42,.3,.54,.45],["c",.48,.39,.81,.57,1.29,.6],["c",.69,.03,1.5,-.3,2.13,-.87],["c",.09,-.09,.27,-.3,.39,-.45],["c",.12,-.15,.24,-.27,.3,-.3],["c",.18,-.06,.39,.03,.51,.21],["c",.06,.18,.06,.24,-.27,.72],["c",-.18,.24,-.54,.78,-.78,1.17],["c",-2.37,3.54,-3.54,6.27,-3.87,9],["c",-.03,.33,-.03,.66,-.03,1.26],["c",0,.9,0,1.08,.15,1.89],["c",.06,.45,.06,.48,.03,.6],["c",-.06,.09,-.21,.21,-.3,.21],["c",-.03,0,-.27,-.06,-.54,-.15],["c",-.84,-.27,-1.11,-.3,-1.65,-.3],["c",-.57,0,-.84,.03,-1.56,.27],["c",-.6,.18,-.69,.21,-.81,.15],["c",-.12,-.06,-.21,-.18,-.21,-.3],["c",0,-.15,.6,-1.44,1.2,-2.61],["c",1.14,-2.22,2.73,-4.68,5.1,-8.01],["c",.21,-.27,.36,-.48,.33,-.48],["c",0,0,-.12,.06,-.27,.12],["c",-.54,.3,-.99,.39,-1.56,.39],["c",-.75,.03,-1.2,-.18,-1.83,-.75],["c",-.99,-.9,-1.83,-1.17,-2.31,-.72],["c",-.18,.15,-.36,.51,-.45,.84],["c",-.06,.24,-.06,.33,-.09,1.98],["c",0,1.62,-.03,1.74,-.06,1.8],["c",-.15,.24,-.54,.24,-.69,0],["c",-.06,-.09,-.06,-.15,-.06,-3.57],["c",0,-3.42,0,-3.48,.06,-3.57],["c",.03,-.06,.09,-.12,.15,-.15],["z"]],w:10.561,h:15.093},8:{d:[["M",4.98,-14.97],["c",.33,-.03,1.02,-.03,1.32,0],["c",1.32,.12,2.49,.6,3.21,1.32],["c",.39,.39,.66,.81,.78,1.29],["c",.09,.36,.09,1.08,0,1.44],["c",-.21,.84,-.66,1.59,-1.59,2.55],["l",-.3,.3],["l",.27,.18],["c",1.47,.93,2.31,2.31,2.25,3.75],["c",-.03,.75,-.24,1.35,-.63,1.95],["c",-.45,.66,-1.02,1.14,-1.83,1.53],["c",-1.8,.87,-4.2,.87,-6,.03],["c",-1.62,-.78,-2.52,-2.16,-2.46,-3.66],["c",.06,-.99,.54,-1.77,1.8,-2.97],["c",.54,-.51,.54,-.54,.48,-.57],["c",-.39,-.27,-.96,-.78,-1.2,-1.14],["c",-.75,-1.11,-.87,-2.4,-.3,-3.6],["c",.69,-1.35,2.25,-2.25,4.2,-2.4],["z"],["m",1.53,.69],["c",-.42,-.09,-1.11,-.12,-1.38,-.06],["c",-.3,.06,-.6,.18,-.81,.3],["c",-.21,.12,-.6,.51,-.72,.72],["c",-.51,.87,-.42,1.89,.21,2.52],["c",.21,.21,.36,.3,1.95,1.23],["c",.96,.54,1.74,.99,1.77,1.02],["c",.09,0,.63,-.6,.99,-1.11],["c",.21,-.36,.48,-.87,.57,-1.23],["c",.06,-.24,.06,-.36,.06,-.72],["c",0,-.45,-.03,-.66,-.15,-.99],["c",-.39,-.81,-1.29,-1.44,-2.49,-1.68],["z"],["m",-1.44,8.07],["l",-1.89,-1.08],["c",-.03,0,-.18,.15,-.39,.33],["c",-1.2,1.08,-1.65,1.95,-1.59,3],["c",.09,1.59,1.35,2.85,3.21,3.24],["c",.33,.06,.45,.06,.93,.06],["c",.63,0,.81,-.03,1.29,-.27],["c",.9,-.42,1.47,-1.41,1.41,-2.4],["c",-.06,-.66,-.39,-1.29,-.9,-1.65],["c",-.12,-.09,-1.05,-.63,-2.07,-1.23],["z"]],w:10.926,h:14.989},9:{d:[["M",4.23,-14.97],["c",.42,-.03,1.29,0,1.62,.06],["c",.51,.12,.93,.3,1.38,.57],["c",1.53,1.02,2.52,3.24,2.73,5.94],["c",.18,2.55,-.48,4.98,-1.83,6.57],["c",-1.05,1.26,-2.4,1.89,-3.93,1.83],["c",-1.23,-.06,-2.31,-.45,-3.03,-1.14],["c",-.57,-.51,-.87,-1.23,-.84,-1.98],["c",.03,-.51,.21,-.9,.6,-1.26],["c",.24,-.24,.45,-.39,.75,-.51],["c",.21,-.06,.27,-.06,.6,-.06],["c",.33,0,.39,0,.6,.06],["c",.3,.12,.51,.27,.75,.51],["c",.39,.36,.57,.78,.57,1.26],["c",0,.27,0,.3,-.09,.42],["c",-.03,.09,-.18,.21,-.3,.3],["c",-.12,.09,-.3,.21,-.39,.27],["c",-.09,.06,-.21,.18,-.27,.24],["c",-.06,.12,-.06,.15,-.06,.33],["c",0,.18,0,.24,.06,.36],["c",.24,.39,.75,.6,1.38,.57],["c",.54,-.03,.9,-.18,1.23,-.48],["c",.81,-.72,1.08,-2.16,.96,-5.37],["l",0,-.63],["l",-.3,.12],["c",-.78,.27,-1.29,.33,-2.1,.27],["c",-1.47,-.12,-2.49,-.54,-3.27,-1.29],["c",-.48,-.51,-.81,-1.11,-.96,-1.89],["c",-.06,-.27,-.06,-.42,-.06,-.96],["c",0,-.51,0,-.66,.06,-.93],["c",.15,-.78,.48,-1.38,.96,-1.89],["c",.15,-.12,.33,-.27,.42,-.36],["c",.69,-.51,1.62,-.81,2.76,-.93],["z"],["m",1.17,.66],["c",-.21,-.06,-.57,-.06,-.81,-.03],["c",-.78,.12,-1.26,.69,-1.41,1.74],["c",-.12,.63,-.15,1.95,-.09,2.79],["c",.12,1.71,.63,2.4,1.77,2.46],["c",1.08,.03,1.62,-.48,1.8,-1.74],["c",.06,-.54,.06,-3,0,-3.54],["c",-.15,-1.05,-.51,-1.53,-1.26,-1.68],["z"]],w:9.959,h:14.986},"rests.multimeasure":{d:[["M",0,-4],["l",0,16],["l",1,0],["l",0,-5],["l",40,0],["l",0,5],["l",1,0],["l",0,-16],["l",-1,0],["l",0,5],["l",-40,0],["l",0,-5],["z"]],w:42,h:18},"rests.whole":{d:[["M",.06,.03],["l",.09,-.06],["l",5.46,0],["l",5.49,0],["l",.09,.06],["l",.06,.09],["l",0,2.19],["l",0,2.19],["l",-.06,.09],["l",-.09,.06],["l",-5.49,0],["l",-5.46,0],["l",-.09,-.06],["l",-.06,-.09],["l",0,-2.19],["l",0,-2.19],["z"]],w:11.25,h:4.68},"rests.half":{d:[["M",.06,-4.62],["l",.09,-.06],["l",5.46,0],["l",5.49,0],["l",.09,.06],["l",.06,.09],["l",0,2.19],["l",0,2.19],["l",-.06,.09],["l",-.09,.06],["l",-5.49,0],["l",-5.46,0],["l",-.09,-.06],["l",-.06,-.09],["l",0,-2.19],["l",0,-2.19],["z"]],w:11.25,h:4.68},"rests.quarter":{d:[["M",1.89,-11.82],["c",.12,-.06,.24,-.06,.36,-.03],["c",.09,.06,4.74,5.58,4.86,5.82],["c",.21,.39,.15,.78,-.15,1.26],["c",-.24,.33,-.72,.81,-1.62,1.56],["c",-.45,.36,-.87,.75,-.96,.84],["c",-.93,.99,-1.14,2.49,-.6,3.63],["c",.18,.39,.27,.48,1.32,1.68],["c",1.92,2.25,1.83,2.16,1.83,2.34],["c",0,.18,-.18,.36,-.36,.39],["c",-.15,0,-.27,-.06,-.48,-.27],["c",-.75,-.75,-2.46,-1.29,-3.39,-1.08],["c",-.45,.09,-.69,.27,-.9,.69],["c",-.12,.3,-.21,.66,-.24,1.14],["c",-.03,.66,.09,1.35,.3,2.01],["c",.15,.42,.24,.66,.45,.96],["c",.18,.24,.18,.33,.03,.42],["c",-.12,.06,-.18,.03,-.45,-.3],["c",-1.08,-1.38,-2.07,-3.36,-2.4,-4.83],["c",-.27,-1.05,-.15,-1.77,.27,-2.07],["c",.21,-.12,.42,-.15,.87,-.15],["c",.87,.06,2.1,.39,3.3,.9],["l",.39,.18],["l",-1.65,-1.95],["c",-2.52,-2.97,-2.61,-3.09,-2.7,-3.27],["c",-.09,-.24,-.12,-.48,-.03,-.75],["c",.15,-.48,.57,-.96,1.83,-2.01],["c",.45,-.36,.84,-.72,.93,-.78],["c",.69,-.75,1.02,-1.8,.9,-2.79],["c",-.06,-.33,-.21,-.84,-.39,-1.11],["c",-.09,-.15,-.45,-.6,-.81,-1.05],["c",-.36,-.42,-.69,-.81,-.72,-.87],["c",-.09,-.18,0,-.42,.21,-.51],["z"]],w:7.888,h:21.435},"rests.8th":{d:[["M",1.68,-6.12],["c",.66,-.09,1.23,.09,1.68,.51],["c",.27,.3,.39,.54,.57,1.26],["c",.09,.33,.18,.66,.21,.72],["c",.12,.27,.33,.45,.6,.48],["c",.12,0,.18,0,.33,-.09],["c",.39,-.18,1.32,-1.29,1.68,-1.98],["c",.09,-.21,.24,-.3,.39,-.3],["c",.12,0,.27,.09,.33,.18],["c",.03,.06,-.27,1.11,-1.86,6.42],["c",-1.02,3.48,-1.89,6.39,-1.92,6.42],["c",0,.03,-.12,.12,-.24,.15],["c",-.18,.09,-.21,.09,-.45,.09],["c",-.24,0,-.3,0,-.48,-.06],["c",-.09,-.06,-.21,-.12,-.21,-.15],["c",-.06,-.03,.15,-.57,1.68,-4.92],["c",.96,-2.67,1.74,-4.89,1.71,-4.89],["l",-.51,.15],["c",-1.08,.36,-1.74,.48,-2.55,.48],["c",-.66,0,-.84,-.03,-1.32,-.27],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.33,-.45,.84,-.81,1.38,-.9],["z"]],w:7.534,h:13.883},"rests.16th":{d:[["M",3.33,-6.12],["c",.66,-.09,1.23,.09,1.68,.51],["c",.27,.3,.39,.54,.57,1.26],["c",.09,.33,.18,.66,.21,.72],["c",.15,.39,.57,.57,.87,.42],["c",.39,-.18,1.2,-1.23,1.62,-2.07],["c",.06,-.15,.24,-.24,.36,-.24],["c",.12,0,.27,.09,.33,.18],["c",.03,.06,-.45,1.86,-2.67,10.17],["c",-1.5,5.55,-2.73,10.14,-2.76,10.17],["c",-.03,.03,-.12,.12,-.24,.15],["c",-.18,.09,-.21,.09,-.45,.09],["c",-.24,0,-.3,0,-.48,-.06],["c",-.09,-.06,-.21,-.12,-.21,-.15],["c",-.06,-.03,.12,-.57,1.44,-4.92],["c",.81,-2.67,1.47,-4.86,1.47,-4.89],["c",-.03,0,-.27,.06,-.54,.15],["c",-1.08,.36,-1.77,.48,-2.58,.48],["c",-.66,0,-.84,-.03,-1.32,-.27],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.72,-1.05,2.22,-1.23,3.06,-.42],["c",.3,.33,.42,.6,.6,1.38],["c",.09,.45,.21,.78,.33,.9],["c",.09,.09,.27,.18,.45,.21],["c",.12,0,.18,0,.33,-.09],["c",.33,-.15,1.02,-.93,1.41,-1.59],["c",.12,-.21,.18,-.39,.39,-1.08],["c",.66,-2.1,1.17,-3.84,1.17,-3.87],["c",0,0,-.21,.06,-.42,.15],["c",-.51,.15,-1.2,.33,-1.68,.42],["c",-.33,.06,-.51,.06,-.96,.06],["c",-.66,0,-.84,-.03,-1.32,-.27],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.33,-.45,.84,-.81,1.38,-.9],["z"]],w:9.724,h:21.383},"rests.32nd":{d:[["M",4.23,-13.62],["c",.66,-.09,1.23,.09,1.68,.51],["c",.27,.3,.39,.54,.57,1.26],["c",.09,.33,.18,.66,.21,.72],["c",.12,.27,.33,.45,.6,.48],["c",.12,0,.18,0,.27,-.06],["c",.33,-.21,.99,-1.11,1.44,-1.98],["c",.09,-.24,.21,-.33,.39,-.33],["c",.12,0,.27,.09,.33,.18],["c",.03,.06,-.57,2.67,-3.21,13.89],["c",-1.8,7.62,-3.3,13.89,-3.3,13.92],["c",-.03,.06,-.12,.12,-.24,.18],["c",-.21,.09,-.24,.09,-.48,.09],["c",-.24,0,-.3,0,-.48,-.06],["c",-.09,-.06,-.21,-.12,-.21,-.15],["c",-.06,-.03,.09,-.57,1.23,-4.92],["c",.69,-2.67,1.26,-4.86,1.29,-4.89],["c",0,-.03,-.12,-.03,-.48,.12],["c",-1.17,.39,-2.22,.57,-3,.54],["c",-.42,-.03,-.75,-.12,-1.11,-.3],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.72,-1.05,2.22,-1.23,3.06,-.42],["c",.3,.33,.42,.6,.6,1.38],["c",.09,.45,.21,.78,.33,.9],["c",.12,.09,.3,.18,.48,.21],["c",.12,0,.18,0,.3,-.09],["c",.42,-.21,1.29,-1.29,1.56,-1.89],["c",.03,-.12,1.23,-4.59,1.23,-4.65],["c",0,-.03,-.18,.03,-.39,.12],["c",-.63,.18,-1.2,.36,-1.74,.45],["c",-.39,.06,-.54,.06,-1.02,.06],["c",-.66,0,-.84,-.03,-1.32,-.27],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.72,-1.05,2.22,-1.23,3.06,-.42],["c",.3,.33,.42,.6,.6,1.38],["c",.09,.45,.21,.78,.33,.9],["c",.18,.18,.51,.27,.72,.15],["c",.3,-.12,.69,-.57,1.08,-1.17],["c",.42,-.6,.39,-.51,1.05,-3.03],["c",.33,-1.26,.6,-2.31,.6,-2.34],["c",0,0,-.21,.03,-.45,.12],["c",-.57,.18,-1.14,.33,-1.62,.42],["c",-.33,.06,-.51,.06,-.96,.06],["c",-.66,0,-.84,-.03,-1.32,-.27],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.33,-.45,.84,-.81,1.38,-.9],["z"]],w:11.373,h:28.883},"rests.64th":{d:[["M",5.13,-13.62],["c",.66,-.09,1.23,.09,1.68,.51],["c",.27,.3,.39,.54,.57,1.26],["c",.15,.63,.21,.81,.33,.96],["c",.18,.21,.54,.3,.75,.18],["c",.24,-.12,.63,-.66,1.08,-1.56],["c",.33,-.66,.39,-.72,.6,-.72],["c",.12,0,.27,.09,.33,.18],["c",.03,.06,-.69,3.66,-3.54,17.64],["c",-1.95,9.66,-3.57,17.61,-3.57,17.64],["c",-.03,.06,-.12,.12,-.24,.18],["c",-.21,.09,-.24,.09,-.48,.09],["c",-.24,0,-.3,0,-.48,-.06],["c",-.09,-.06,-.21,-.12,-.21,-.15],["c",-.06,-.03,.06,-.57,1.05,-4.95],["c",.6,-2.7,1.08,-4.89,1.08,-4.92],["c",0,0,-.24,.06,-.51,.15],["c",-.66,.24,-1.2,.36,-1.77,.48],["c",-.42,.06,-.57,.06,-1.05,.06],["c",-.69,0,-.87,-.03,-1.35,-.27],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.72,-1.05,2.22,-1.23,3.06,-.42],["c",.3,.33,.42,.6,.6,1.38],["c",.09,.45,.21,.78,.33,.9],["c",.09,.09,.27,.18,.45,.21],["c",.21,.03,.39,-.09,.72,-.42],["c",.45,-.45,1.02,-1.26,1.17,-1.65],["c",.03,-.09,.27,-1.14,.54,-2.34],["c",.27,-1.2,.48,-2.19,.51,-2.22],["c",0,-.03,-.09,-.03,-.48,.12],["c",-1.17,.39,-2.22,.57,-3,.54],["c",-.42,-.03,-.75,-.12,-1.11,-.3],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.36,-.54,.96,-.87,1.65,-.93],["c",.54,-.03,1.02,.15,1.41,.54],["c",.27,.3,.39,.54,.57,1.26],["c",.09,.33,.18,.66,.21,.72],["c",.15,.39,.57,.57,.9,.42],["c",.36,-.18,1.2,-1.26,1.47,-1.89],["c",.03,-.09,.3,-1.2,.57,-2.43],["l",.51,-2.28],["l",-.54,.18],["c",-1.11,.36,-1.8,.48,-2.61,.48],["c",-.66,0,-.84,-.03,-1.32,-.27],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.36,-.54,.96,-.87,1.65,-.93],["c",.54,-.03,1.02,.15,1.41,.54],["c",.27,.3,.39,.54,.57,1.26],["c",.15,.63,.21,.81,.33,.96],["c",.21,.21,.54,.3,.75,.18],["c",.36,-.18,.93,-.93,1.29,-1.68],["c",.12,-.24,.18,-.48,.63,-2.55],["l",.51,-2.31],["c",0,-.03,-.18,.03,-.39,.12],["c",-1.14,.36,-2.1,.54,-2.82,.51],["c",-.42,-.03,-.75,-.12,-1.11,-.3],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.33,-.45,.84,-.81,1.38,-.9],["z"]],w:12.453,h:36.383},"rests.128th":{d:[["M",6.03,-21.12],["c",.66,-.09,1.23,.09,1.68,.51],["c",.27,.3,.39,.54,.57,1.26],["c",.09,.33,.18,.66,.21,.72],["c",.12,.27,.33,.45,.6,.48],["c",.21,0,.33,-.06,.54,-.36],["c",.15,-.21,.54,-.93,.78,-1.47],["c",.15,-.33,.18,-.39,.3,-.48],["c",.18,-.09,.45,0,.51,.15],["c",.03,.09,-7.11,42.75,-7.17,42.84],["c",-.03,.03,-.15,.09,-.24,.15],["c",-.18,.06,-.24,.06,-.45,.06],["c",-.24,0,-.3,0,-.48,-.06],["c",-.09,-.06,-.21,-.12,-.21,-.15],["c",-.06,-.03,.03,-.57,.84,-4.98],["c",.51,-2.7,.93,-4.92,.9,-4.92],["c",0,0,-.15,.06,-.36,.12],["c",-.78,.27,-1.62,.48,-2.31,.57],["c",-.15,.03,-.54,.03,-.81,.03],["c",-.66,0,-.84,-.03,-1.32,-.27],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.36,-.54,.96,-.87,1.65,-.93],["c",.54,-.03,1.02,.15,1.41,.54],["c",.27,.3,.39,.54,.57,1.26],["c",.09,.33,.18,.66,.21,.72],["c",.12,.27,.33,.45,.63,.48],["c",.12,0,.18,0,.3,-.09],["c",.42,-.21,1.14,-1.11,1.5,-1.83],["c",.12,-.27,.12,-.27,.54,-2.52],["c",.24,-1.23,.42,-2.25,.39,-2.25],["c",0,0,-.24,.06,-.51,.18],["c",-1.26,.39,-2.25,.57,-3.06,.54],["c",-.42,-.03,-.75,-.12,-1.11,-.3],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.36,-.54,.96,-.87,1.65,-.93],["c",.54,-.03,1.02,.15,1.41,.54],["c",.27,.3,.39,.54,.57,1.26],["c",.15,.63,.21,.81,.33,.96],["c",.18,.21,.51,.3,.75,.18],["c",.36,-.15,1.05,-.99,1.41,-1.77],["l",.15,-.3],["l",.42,-2.25],["c",.21,-1.26,.42,-2.28,.39,-2.28],["l",-.51,.15],["c",-1.11,.39,-1.89,.51,-2.7,.51],["c",-.66,0,-.84,-.03,-1.32,-.27],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.36,-.54,.96,-.87,1.65,-.93],["c",.54,-.03,1.02,.15,1.41,.54],["c",.27,.3,.39,.54,.57,1.26],["c",.15,.63,.21,.81,.33,.96],["c",.18,.18,.48,.27,.72,.21],["c",.33,-.12,1.14,-1.26,1.41,-1.95],["c",0,-.09,.21,-1.11,.45,-2.34],["c",.21,-1.2,.39,-2.22,.39,-2.28],["c",.03,-.03,0,-.03,-.45,.12],["c",-.57,.18,-1.2,.33,-1.71,.42],["c",-.3,.06,-.51,.06,-.93,.06],["c",-.66,0,-.84,-.03,-1.32,-.27],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.36,-.54,.96,-.87,1.65,-.93],["c",.54,-.03,1.02,.15,1.41,.54],["c",.27,.3,.39,.54,.57,1.26],["c",.09,.33,.18,.66,.21,.72],["c",.12,.27,.33,.45,.6,.48],["c",.18,0,.36,-.09,.57,-.33],["c",.33,-.36,.78,-1.14,.93,-1.56],["c",.03,-.12,.24,-1.2,.45,-2.4],["c",.24,-1.2,.42,-2.22,.42,-2.28],["c",.03,-.03,0,-.03,-.39,.09],["c",-1.05,.36,-1.8,.48,-2.58,.48],["c",-.63,0,-.84,-.03,-1.29,-.27],["c",-1.32,-.63,-1.77,-2.16,-1.02,-3.3],["c",.33,-.45,.84,-.81,1.38,-.9],["z"]],w:12.992,h:43.883},"accidentals.sharp":{d:[["M",5.73,-11.19],["c",.21,-.12,.54,-.03,.66,.24],["c",.06,.12,.06,.21,.06,2.31],["c",0,1.23,0,2.22,.03,2.22],["c",0,0,.27,-.12,.6,-.24],["c",.69,-.27,.78,-.3,.96,-.15],["c",.21,.15,.21,.18,.21,1.38],["c",0,1.02,0,1.11,-.06,1.2],["c",-.03,.06,-.09,.12,-.12,.15],["c",-.06,.03,-.42,.21,-.84,.36],["l",-.75,.33],["l",-.03,2.43],["c",0,1.32,0,2.43,.03,2.43],["c",0,0,.27,-.12,.6,-.24],["c",.69,-.27,.78,-.3,.96,-.15],["c",.21,.15,.21,.18,.21,1.38],["c",0,1.02,0,1.11,-.06,1.2],["c",-.03,.06,-.09,.12,-.12,.15],["c",-.06,.03,-.42,.21,-.84,.36],["l",-.75,.33],["l",-.03,2.52],["c",0,2.28,-.03,2.55,-.06,2.64],["c",-.21,.36,-.72,.36,-.93,0],["c",-.03,-.09,-.06,-.33,-.06,-2.43],["l",0,-2.31],["l",-1.29,.51],["l",-1.26,.51],["l",0,2.43],["c",0,2.58,0,2.52,-.15,2.67],["c",-.06,.09,-.27,.18,-.36,.18],["c",-.12,0,-.33,-.09,-.39,-.18],["c",-.15,-.15,-.15,-.09,-.15,-2.43],["c",0,-1.23,0,-2.22,-.03,-2.22],["c",0,0,-.27,.12,-.6,.24],["c",-.69,.27,-.78,.3,-.96,.15],["c",-.21,-.15,-.21,-.18,-.21,-1.38],["c",0,-1.02,0,-1.11,.06,-1.2],["c",.03,-.06,.09,-.12,.12,-.15],["c",.06,-.03,.42,-.21,.84,-.36],["l",.78,-.33],["l",0,-2.43],["c",0,-1.32,0,-2.43,-.03,-2.43],["c",0,0,-.27,.12,-.6,.24],["c",-.69,.27,-.78,.3,-.96,.15],["c",-.21,-.15,-.21,-.18,-.21,-1.38],["c",0,-1.02,0,-1.11,.06,-1.2],["c",.03,-.06,.09,-.12,.12,-.15],["c",.06,-.03,.42,-.21,.84,-.36],["l",.78,-.33],["l",0,-2.52],["c",0,-2.28,.03,-2.55,.06,-2.64],["c",.21,-.36,.72,-.36,.93,0],["c",.03,.09,.06,.33,.06,2.43],["l",.03,2.31],["l",1.26,-.51],["l",1.26,-.51],["l",0,-2.43],["c",0,-2.28,0,-2.43,.06,-2.55],["c",.06,-.12,.12,-.18,.27,-.24],["z"],["m",-.33,10.65],["l",0,-2.43],["l",-1.29,.51],["l",-1.26,.51],["l",0,2.46],["l",0,2.43],["l",.09,-.03],["c",.06,-.03,.63,-.27,1.29,-.51],["l",1.17,-.48],["l",0,-2.46],["z"]],w:8.25,h:22.462},"accidentals.halfsharp":{d:[["M",2.43,-10.05],["c",.21,-.12,.54,-.03,.66,.24],["c",.06,.12,.06,.21,.06,2.01],["c",0,1.05,0,1.89,.03,1.89],["l",.72,-.48],["c",.69,-.48,.69,-.51,.87,-.51],["c",.15,0,.18,.03,.27,.09],["c",.21,.15,.21,.18,.21,1.41],["c",0,1.11,-.03,1.14,-.09,1.23],["c",-.03,.03,-.48,.39,-1.02,.75],["l",-.99,.66],["l",0,2.37],["c",0,1.32,0,2.37,.03,2.37],["l",.72,-.48],["c",.69,-.48,.69,-.51,.87,-.51],["c",.15,0,.18,.03,.27,.09],["c",.21,.15,.21,.18,.21,1.41],["c",0,1.11,-.03,1.14,-.09,1.23],["c",-.03,.03,-.48,.39,-1.02,.75],["l",-.99,.66],["l",0,2.25],["c",0,1.95,0,2.28,-.06,2.37],["c",-.06,.12,-.12,.21,-.24,.27],["c",-.27,.12,-.54,.03,-.69,-.24],["c",-.06,-.12,-.06,-.21,-.06,-2.01],["c",0,-1.05,0,-1.89,-.03,-1.89],["l",-.72,.48],["c",-.69,.48,-.69,.48,-.87,.48],["c",-.15,0,-.18,0,-.27,-.06],["c",-.21,-.15,-.21,-.18,-.21,-1.41],["c",0,-1.11,.03,-1.14,.09,-1.23],["c",.03,-.03,.48,-.39,1.02,-.75],["l",.99,-.66],["l",0,-2.37],["c",0,-1.32,0,-2.37,-.03,-2.37],["l",-.72,.48],["c",-.69,.48,-.69,.48,-.87,.48],["c",-.15,0,-.18,0,-.27,-.06],["c",-.21,-.15,-.21,-.18,-.21,-1.41],["c",0,-1.11,.03,-1.14,.09,-1.23],["c",.03,-.03,.48,-.39,1.02,-.75],["l",.99,-.66],["l",0,-2.25],["c",0,-2.13,0,-2.28,.06,-2.4],["c",.06,-.12,.12,-.18,.27,-.24],["z"]],w:5.25,h:20.174},"accidentals.nat":{d:[["M",.21,-11.4],["c",.24,-.06,.78,0,.99,.15],["c",.03,.03,.03,.48,0,2.61],["c",-.03,1.44,-.03,2.61,-.03,2.61],["c",0,.03,.75,-.09,1.68,-.24],["c",.96,-.18,1.71,-.27,1.74,-.27],["c",.15,.03,.27,.15,.36,.3],["l",.06,.12],["l",.09,8.67],["c",.09,6.96,.12,8.67,.09,8.67],["c",-.03,.03,-.12,.06,-.21,.09],["c",-.24,.09,-.72,.09,-.96,0],["c",-.09,-.03,-.18,-.06,-.21,-.09],["c",-.03,-.03,-.03,-.48,0,-2.61],["c",.03,-1.44,.03,-2.61,.03,-2.61],["c",0,-.03,-.75,.09,-1.68,.24],["c",-.96,.18,-1.71,.27,-1.74,.27],["c",-.15,-.03,-.27,-.15,-.36,-.3],["l",-.06,-.15],["l",-.09,-7.53],["c",-.06,-4.14,-.09,-8.04,-.12,-8.67],["l",0,-1.11],["l",.15,-.06],["c",.09,-.03,.21,-.06,.27,-.09],["z"],["m",3.75,8.4],["c",0,-.33,0,-.42,-.03,-.42],["c",-.12,0,-2.79,.45,-2.79,.48],["c",-.03,0,-.09,6.3,-.09,6.33],["c",.03,0,2.79,-.45,2.82,-.48],["c",0,0,.09,-4.53,.09,-5.91],["z"]],w:5.4,h:22.8},"accidentals.flat":{d:[["M",-.36,-14.07],["c",.33,-.06,.87,0,1.08,.15],["c",.06,.03,.06,.36,-.03,5.25],["c",-.06,2.85,-.09,5.19,-.09,5.19],["c",0,.03,.12,-.03,.24,-.12],["c",.63,-.42,1.41,-.66,2.19,-.72],["c",.81,-.03,1.47,.21,2.04,.78],["c",.57,.54,.87,1.26,.93,2.04],["c",.03,.57,-.09,1.08,-.36,1.62],["c",-.42,.81,-1.02,1.38,-2.82,2.61],["c",-1.14,.78,-1.44,1.02,-1.8,1.44],["c",-.18,.18,-.39,.39,-.45,.42],["c",-.27,.18,-.57,.15,-.81,-.06],["c",-.06,-.09,-.12,-.18,-.15,-.27],["c",-.03,-.06,-.09,-3.27,-.18,-8.34],["c",-.09,-4.53,-.15,-8.58,-.18,-9.03],["l",0,-.78],["l",.12,-.06],["c",.06,-.03,.18,-.09,.27,-.12],["z"],["m",3.18,11.01],["c",-.21,-.12,-.54,-.15,-.81,-.06],["c",-.54,.15,-.99,.63,-1.17,1.26],["c",-.06,.3,-.12,2.88,-.06,3.87],["c",.03,.42,.03,.81,.06,.9],["l",.03,.12],["l",.45,-.39],["c",.63,-.54,1.26,-1.17,1.56,-1.59],["c",.3,-.42,.6,-.99,.72,-1.41],["c",.18,-.69,.09,-1.47,-.18,-2.07],["c",-.15,-.3,-.33,-.51,-.6,-.63],["z"]],w:6.75,h:18.801},"accidentals.halfflat":{d:[["M",4.83,-14.07],["c",.33,-.06,.87,0,1.08,.15],["c",.06,.03,.06,.6,-.12,9.06],["c",-.09,5.55,-.15,9.06,-.18,9.12],["c",-.03,.09,-.09,.18,-.15,.27],["c",-.24,.21,-.54,.24,-.81,.06],["c",-.06,-.03,-.27,-.24,-.45,-.42],["c",-.36,-.42,-.66,-.66,-1.8,-1.44],["c",-1.23,-.84,-1.83,-1.32,-2.25,-1.77],["c",-.66,-.78,-.96,-1.56,-.93,-2.46],["c",.09,-1.41,1.11,-2.58,2.4,-2.79],["c",.3,-.06,.84,-.03,1.23,.06],["c",.54,.12,1.08,.33,1.53,.63],["c",.12,.09,.24,.15,.24,.12],["c",0,0,-.12,-8.37,-.18,-9.75],["l",0,-.66],["l",.12,-.06],["c",.06,-.03,.18,-.09,.27,-.12],["z"],["m",-1.65,10.95],["c",-.6,-.18,-1.08,.09,-1.38,.69],["c",-.27,.6,-.36,1.38,-.18,2.07],["c",.12,.42,.42,.99,.72,1.41],["c",.3,.42,.93,1.05,1.56,1.59],["l",.48,.39],["l",0,-.12],["c",.03,-.09,.03,-.48,.06,-.9],["c",.03,-.57,.03,-1.08,0,-2.22],["c",-.03,-1.62,-.03,-1.62,-.24,-2.07],["c",-.21,-.42,-.6,-.75,-1.02,-.84],["z"]],w:6.728,h:18.801},"accidentals.dblflat":{d:[["M",-.36,-14.07],["c",.33,-.06,.87,0,1.08,.15],["c",.06,.03,.06,.33,-.03,4.89],["c",-.06,2.67,-.09,5.01,-.09,5.22],["l",0,.36],["l",.15,-.15],["c",.36,-.3,.75,-.51,1.2,-.63],["c",.33,-.09,.96,-.09,1.26,-.03],["c",.27,.09,.63,.27,.87,.45],["l",.21,.15],["l",0,-.27],["c",0,-.15,-.03,-2.43,-.09,-5.1],["c",-.09,-4.56,-.09,-4.86,-.03,-4.89],["c",.15,-.12,.39,-.15,.72,-.15],["c",.3,0,.54,.03,.69,.15],["c",.06,.03,.06,.33,-.03,4.95],["c",-.06,2.7,-.09,5.04,-.09,5.22],["l",.03,.3],["l",.21,-.15],["c",.69,-.48,1.44,-.69,2.28,-.69],["c",.51,0,.78,.03,1.2,.21],["c",1.32,.63,2.01,2.28,1.53,3.69],["c",-.21,.57,-.51,1.02,-1.05,1.56],["c",-.42,.42,-.81,.72,-1.92,1.5],["c",-1.26,.87,-1.5,1.08,-1.86,1.5],["c",-.39,.45,-.54,.54,-.81,.51],["c",-.18,0,-.21,0,-.33,-.06],["l",-.21,-.21],["l",-.06,-.12],["l",-.03,-.99],["c",-.03,-.54,-.03,-1.29,-.06,-1.68],["l",0,-.69],["l",-.21,.24],["c",-.36,.42,-.75,.75,-1.8,1.62],["c",-1.02,.84,-1.2,.99,-1.44,1.38],["c",-.36,.51,-.54,.6,-.9,.51],["c",-.15,-.03,-.39,-.27,-.42,-.42],["c",-.03,-.06,-.09,-3.27,-.18,-8.34],["c",-.09,-4.53,-.15,-8.58,-.18,-9.03],["l",0,-.78],["l",.12,-.06],["c",.06,-.03,.18,-.09,.27,-.12],["z"],["m",2.52,10.98],["c",-.18,-.09,-.48,-.12,-.66,-.06],["c",-.39,.15,-.69,.54,-.84,1.14],["c",-.06,.24,-.06,.39,-.09,1.74],["c",-.03,1.44,0,2.73,.06,3.18],["l",.03,.15],["l",.27,-.27],["c",.93,-.96,1.5,-1.95,1.74,-3.06],["c",.06,-.27,.06,-.39,.06,-.96],["c",0,-.54,0,-.69,-.06,-.93],["c",-.09,-.51,-.27,-.81,-.51,-.93],["z"],["m",5.43,0],["c",-.18,-.09,-.51,-.12,-.72,-.06],["c",-.54,.12,-.96,.63,-1.17,1.26],["c",-.06,.3,-.12,2.88,-.06,3.9],["c",.03,.42,.03,.81,.06,.9],["l",.03,.12],["l",.36,-.3],["c",.42,-.36,1.02,-.96,1.29,-1.29],["c",.36,-.45,.66,-.99,.81,-1.41],["c",.42,-1.23,.15,-2.76,-.6,-3.12],["z"]],w:11.613,h:18.804},"accidentals.dblsharp":{d:[["M",-.18,-3.96],["c",.06,-.03,.12,-.06,.15,-.06],["c",.09,0,2.76,.27,2.79,.3],["c",.12,.03,.15,.12,.15,.51],["c",.06,.96,.24,1.59,.57,2.1],["c",.06,.09,.15,.21,.18,.24],["l",.09,.06],["l",.09,-.06],["c",.03,-.03,.12,-.15,.18,-.24],["c",.33,-.51,.51,-1.14,.57,-2.1],["c",0,-.39,.03,-.45,.12,-.51],["c",.03,0,.66,-.09,1.44,-.15],["c",1.47,-.15,1.5,-.15,1.56,-.03],["c",.03,.06,0,.42,-.09,1.44],["c",-.09,.72,-.15,1.35,-.15,1.38],["c",0,.03,-.03,.09,-.06,.12],["c",-.06,.06,-.12,.09,-.51,.09],["c",-1.08,.06,-1.8,.3,-2.28,.75],["l",-.12,.09],["l",.09,.09],["c",.12,.15,.39,.33,.63,.45],["c",.42,.18,.96,.27,1.68,.33],["c",.39,0,.45,.03,.51,.09],["c",.03,.03,.06,.09,.06,.12],["c",0,.03,.06,.66,.15,1.38],["c",.09,1.02,.12,1.38,.09,1.44],["c",-.06,.12,-.09,.12,-1.56,-.03],["c",-.78,-.06,-1.41,-.15,-1.44,-.15],["c",-.09,-.06,-.12,-.12,-.12,-.54],["c",-.06,-.93,-.24,-1.56,-.57,-2.07],["c",-.06,-.09,-.15,-.21,-.18,-.24],["l",-.09,-.06],["l",-.09,.06],["c",-.03,.03,-.12,.15,-.18,.24],["c",-.33,.51,-.51,1.14,-.57,2.07],["c",0,.42,-.03,.48,-.12,.54],["c",-.03,0,-.66,.09,-1.44,.15],["c",-1.47,.15,-1.5,.15,-1.56,.03],["c",-.03,-.06,0,-.42,.09,-1.44],["c",.09,-.72,.15,-1.35,.15,-1.38],["c",0,-.03,.03,-.09,.06,-.12],["c",.06,-.06,.12,-.09,.51,-.09],["c",.72,-.06,1.26,-.15,1.68,-.33],["c",.24,-.12,.51,-.3,.63,-.45],["l",.09,-.09],["l",-.12,-.09],["c",-.48,-.45,-1.2,-.69,-2.28,-.75],["c",-.39,0,-.45,-.03,-.51,-.09],["c",-.03,-.03,-.06,-.09,-.06,-.12],["c",0,-.03,-.06,-.63,-.12,-1.38],["c",-.09,-.72,-.15,-1.35,-.15,-1.38],["z"]],w:7.95,h:7.977},"dots.dot":{d:[["M",1.32,-1.68],["c",.09,-.03,.27,-.06,.39,-.06],["c",.96,0,1.74,.78,1.74,1.71],["c",0,.96,-.78,1.74,-1.71,1.74],["c",-.96,0,-1.74,-.78,-1.74,-1.71],["c",0,-.78,.54,-1.5,1.32,-1.68],["z"]],w:3.45,h:3.45},"noteheads.dbl":{d:[["M",-.69,-4.02],["c",.18,-.09,.36,-.09,.54,0],["c",.18,.09,.24,.15,.33,.3],["c",.06,.15,.06,.18,.06,1.41],["l",0,1.23],["l",.12,-.18],["c",.72,-1.26,2.64,-2.31,4.86,-2.64],["c",.81,-.15,1.11,-.15,2.13,-.15],["c",.99,0,1.29,0,2.1,.15],["c",.75,.12,1.38,.27,2.04,.54],["c",1.35,.51,2.34,1.26,2.82,2.1],["l",.12,.18],["l",0,-1.23],["c",0,-1.2,0,-1.26,.06,-1.38],["c",.09,-.18,.15,-.24,.33,-.33],["c",.18,-.09,.36,-.09,.54,0],["c",.18,.09,.24,.15,.33,.3],["l",.06,.15],["l",0,3.54],["l",0,3.54],["l",-.06,.15],["c",-.09,.18,-.15,.24,-.33,.33],["c",-.18,.09,-.36,.09,-.54,0],["c",-.18,-.09,-.24,-.15,-.33,-.33],["c",-.06,-.12,-.06,-.18,-.06,-1.38],["l",0,-1.23],["l",-.12,.18],["c",-.48,.84,-1.47,1.59,-2.82,2.1],["c",-.84,.33,-1.71,.54,-2.85,.66],["c",-.45,.06,-2.16,.06,-2.61,0],["c",-1.14,-.12,-2.01,-.33,-2.85,-.66],["c",-1.35,-.51,-2.34,-1.26,-2.82,-2.1],["l",-.12,-.18],["l",0,1.23],["c",0,1.23,0,1.26,-.06,1.38],["c",-.09,.18,-.15,.24,-.33,.33],["c",-.18,.09,-.36,.09,-.54,0],["c",-.18,-.09,-.24,-.15,-.33,-.33],["l",-.06,-.15],["l",0,-3.54],["c",0,-3.48,0,-3.54,.06,-3.66],["c",.09,-.18,.15,-.24,.33,-.33],["z"],["m",7.71,.63],["c",-.36,-.06,-.9,-.06,-1.14,0],["c",-.3,.03,-.66,.24,-.87,.42],["c",-.6,.54,-.9,1.62,-.75,2.82],["c",.12,.93,.51,1.68,1.11,2.31],["c",.75,.72,1.83,1.2,2.85,1.26],["c",1.05,.06,1.83,-.54,2.1,-1.65],["c",.21,-.9,.12,-1.95,-.24,-2.82],["c",-.36,-.81,-1.08,-1.53,-1.95,-1.95],["c",-.3,-.15,-.78,-.3,-1.11,-.39],["z"]],w:16.83,h:8.145},"noteheads.whole":{d:[["M",6.51,-4.05],["c",.51,-.03,2.01,0,2.52,.03],["c",1.41,.18,2.64,.51,3.72,1.08],["c",1.2,.63,1.95,1.41,2.19,2.31],["c",.09,.33,.09,.9,0,1.23],["c",-.24,.9,-.99,1.68,-2.19,2.31],["c",-1.08,.57,-2.28,.9,-3.75,1.08],["c",-.66,.06,-2.31,.06,-2.97,0],["c",-1.47,-.18,-2.67,-.51,-3.75,-1.08],["c",-1.2,-.63,-1.95,-1.41,-2.19,-2.31],["c",-.09,-.33,-.09,-.9,0,-1.23],["c",.24,-.9,.99,-1.68,2.19,-2.31],["c",1.2,-.63,2.61,-.99,4.23,-1.11],["z"],["m",.57,.66],["c",-.87,-.15,-1.53,0,-2.04,.51],["c",-.15,.15,-.24,.27,-.33,.48],["c",-.24,.51,-.36,1.08,-.33,1.77],["c",.03,.69,.18,1.26,.42,1.77],["c",.6,1.17,1.74,1.98,3.18,2.22],["c",1.11,.21,1.95,-.15,2.34,-.99],["c",.24,-.51,.36,-1.08,.33,-1.8],["c",-.06,-1.11,-.45,-2.04,-1.17,-2.76],["c",-.63,-.63,-1.47,-1.05,-2.4,-1.2],["z"]],w:14.985,h:8.097},"noteheads.half":{d:[["M",7.44,-4.05],["c",.06,-.03,.27,-.03,.48,-.03],["c",1.05,0,1.71,.24,2.1,.81],["c",.42,.6,.45,1.35,.18,2.4],["c",-.42,1.59,-1.14,2.73,-2.16,3.39],["c",-1.41,.93,-3.18,1.44,-5.4,1.53],["c",-1.17,.03,-1.89,-.21,-2.28,-.81],["c",-.42,-.6,-.45,-1.35,-.18,-2.4],["c",.42,-1.59,1.14,-2.73,2.16,-3.39],["c",.63,-.42,1.23,-.72,1.98,-.96],["c",.9,-.3,1.65,-.42,3.12,-.54],["z"],["m",1.29,.87],["c",-.27,-.09,-.63,-.12,-.9,-.03],["c",-.72,.24,-1.53,.69,-3.27,1.8],["c",-2.34,1.5,-3.3,2.25,-3.57,2.79],["c",-.36,.72,-.06,1.5,.66,1.77],["c",.24,.12,.69,.09,.99,0],["c",.84,-.3,1.92,-.93,4.14,-2.37],["c",1.62,-1.08,2.37,-1.71,2.61,-2.19],["c",.36,-.72,.06,-1.5,-.66,-1.77],["z"]],w:10.37,h:8.132},"noteheads.quarter":{d:[["M",6.09,-4.05],["c",.36,-.03,1.2,0,1.53,.06],["c",1.17,.24,1.89,.84,2.16,1.83],["c",.06,.18,.06,.3,.06,.66],["c",0,.45,0,.63,-.15,1.08],["c",-.66,2.04,-3.06,3.93,-5.52,4.38],["c",-.54,.09,-1.44,.09,-1.83,.03],["c",-1.23,-.27,-1.98,-.87,-2.25,-1.86],["c",-.06,-.18,-.06,-.3,-.06,-.66],["c",0,-.45,0,-.63,.15,-1.08],["c",.24,-.78,.75,-1.53,1.44,-2.22],["c",1.2,-1.2,2.85,-2.01,4.47,-2.22],["z"]],w:9.81,h:8.094},"noteheads.slash.nostem":{d:[["M",9.3,-7.77],["c",.06,-.06,.18,-.06,1.71,-.06],["l",1.65,0],["l",.09,.09],["c",.06,.06,.06,.09,.06,.15],["c",-.03,.12,-9.21,15.24,-9.3,15.33],["c",-.06,.06,-.18,.06,-1.71,.06],["l",-1.65,0],["l",-.09,-.09],["c",-.06,-.06,-.06,-.09,-.06,-.15],["c",.03,-.12,9.21,-15.24,9.3,-15.33],["z"]],w:12.81,h:15.63},"noteheads.indeterminate":{d:[["M",.78,-4.05],["c",.12,-.03,.24,-.03,.36,.03],["c",.03,.03,.93,.72,1.95,1.56],["l",1.86,1.5],["l",1.86,-1.5],["c",1.02,-.84,1.92,-1.53,1.95,-1.56],["c",.21,-.12,.33,-.09,.75,.24],["c",.3,.27,.36,.36,.36,.54],["c",0,.03,-.03,.12,-.06,.18],["c",-.03,.06,-.9,.75,-1.89,1.56],["l",-1.8,1.47],["c",0,.03,.81,.69,1.8,1.5],["c",.99,.81,1.86,1.5,1.89,1.56],["c",.03,.06,.06,.15,.06,.18],["c",0,.18,-.06,.27,-.36,.54],["c",-.42,.33,-.54,.36,-.75,.24],["c",-.03,-.03,-.93,-.72,-1.95,-1.56],["l",-1.86,-1.5],["l",-1.86,1.5],["c",-1.02,.84,-1.92,1.53,-1.95,1.56],["c",-.21,.12,-.33,.09,-.75,-.24],["c",-.3,-.27,-.36,-.36,-.36,-.54],["c",0,-.03,.03,-.12,.06,-.18],["c",.03,-.06,.9,-.75,1.89,-1.56],["l",1.8,-1.47],["c",0,-.03,-.81,-.69,-1.8,-1.5],["c",-.99,-.81,-1.86,-1.5,-1.89,-1.56],["c",-.06,-.12,-.09,-.21,-.03,-.36],["c",.03,-.09,.57,-.57,.72,-.63],["z"]],w:9.843,h:8.139},"scripts.ufermata":{d:[["M",-.75,-10.77],["c",.12,0,.45,-.03,.69,-.03],["c",2.91,-.03,5.55,1.53,7.41,4.35],["c",1.17,1.71,1.95,3.72,2.43,6.03],["c",.12,.51,.12,.57,.03,.69],["c",-.12,.21,-.48,.27,-.69,.12],["c",-.12,-.09,-.18,-.24,-.27,-.69],["c",-.78,-3.63,-3.42,-6.54,-6.78,-7.38],["c",-.78,-.21,-1.2,-.24,-2.07,-.24],["c",-.63,0,-.84,0,-1.2,.06],["c",-1.83,.27,-3.42,1.08,-4.8,2.37],["c",-1.41,1.35,-2.4,3.21,-2.85,5.19],["c",-.09,.45,-.15,.6,-.27,.69],["c",-.21,.15,-.57,.09,-.69,-.12],["c",-.09,-.12,-.09,-.18,.03,-.69],["c",.33,-1.62,.78,-3,1.47,-4.38],["c",1.77,-3.54,4.44,-5.67,7.56,-5.97],["z"],["m",.33,7.47],["c",1.38,-.3,2.58,.9,2.31,2.25],["c",-.15,.72,-.78,1.35,-1.47,1.5],["c",-1.38,.27,-2.58,-.93,-2.31,-2.31],["c",.15,-.69,.78,-1.29,1.47,-1.44],["z"]],w:19.748,h:11.289},"scripts.dfermata":{d:[["M",-9.63,-.42],["c",.15,-.09,.36,-.06,.51,.03],["c",.12,.09,.18,.24,.27,.66],["c",.78,3.66,3.42,6.57,6.78,7.41],["c",.78,.21,1.2,.24,2.07,.24],["c",.63,0,.84,0,1.2,-.06],["c",1.83,-.27,3.42,-1.08,4.8,-2.37],["c",1.41,-1.35,2.4,-3.21,2.85,-5.22],["c",.09,-.42,.15,-.57,.27,-.66],["c",.21,-.15,.57,-.09,.69,.12],["c",.09,.12,.09,.18,-.03,.69],["c",-.33,1.62,-.78,3,-1.47,4.38],["c",-1.92,3.84,-4.89,6,-8.31,6],["c",-3.42,0,-6.39,-2.16,-8.31,-6],["c",-.48,-.96,-.84,-1.92,-1.14,-2.97],["c",-.18,-.69,-.42,-1.74,-.42,-1.92],["c",0,-.12,.09,-.27,.24,-.33],["z"],["m",9.21,0],["c",1.2,-.27,2.34,.63,2.34,1.86],["c",0,.9,-.66,1.68,-1.5,1.89],["c",-1.38,.27,-2.58,-.93,-2.31,-2.31],["c",.15,-.69,.78,-1.29,1.47,-1.44],["z"]],w:19.744,h:11.274},"scripts.sforzato":{d:[["M",-6.45,-3.69],["c",.06,-.03,.15,-.06,.18,-.06],["c",.06,0,2.85,.72,6.24,1.59],["l",6.33,1.65],["c",.33,.06,.45,.21,.45,.51],["c",0,.3,-.12,.45,-.45,.51],["l",-6.33,1.65],["c",-3.39,.87,-6.18,1.59,-6.21,1.59],["c",-.21,0,-.48,-.24,-.51,-.45],["c",0,-.15,.06,-.36,.18,-.45],["c",.09,-.06,.87,-.27,3.84,-1.05],["c",2.04,-.54,3.84,-.99,4.02,-1.02],["c",.15,-.06,1.14,-.24,2.22,-.42],["c",1.05,-.18,1.92,-.36,1.92,-.36],["c",0,0,-.87,-.18,-1.92,-.36],["c",-1.08,-.18,-2.07,-.36,-2.22,-.42],["c",-.18,-.03,-1.98,-.48,-4.02,-1.02],["c",-2.97,-.78,-3.75,-.99,-3.84,-1.05],["c",-.12,-.09,-.18,-.3,-.18,-.45],["c",.03,-.15,.15,-.3,.3,-.39],["z"]],w:13.5,h:7.5},"scripts.staccato":{d:[["M",-.36,-1.47],["c",.93,-.21,1.86,.51,1.86,1.47],["c",0,.93,-.87,1.65,-1.8,1.47],["c",-.54,-.12,-1.02,-.57,-1.14,-1.08],["c",-.21,-.81,.27,-1.65,1.08,-1.86],["z"]],w:2.989,h:3.004},"scripts.tenuto":{d:[["M",-4.2,-.48],["l",.12,-.06],["l",4.08,0],["l",4.08,0],["l",.12,.06],["c",.39,.21,.39,.75,0,.96],["l",-.12,.06],["l",-4.08,0],["l",-4.08,0],["l",-.12,-.06],["c",-.39,-.21,-.39,-.75,0,-.96],["z"]],w:8.985,h:1.08},"scripts.umarcato":{d:[["M",-.15,-8.19],["c",.15,-.12,.36,-.03,.45,.15],["c",.21,.42,3.45,7.65,3.45,7.71],["c",0,.12,-.12,.27,-.21,.3],["c",-.03,.03,-.51,.03,-1.14,.03],["c",-1.05,0,-1.08,0,-1.17,-.06],["c",-.09,-.06,-.24,-.36,-1.17,-2.4],["c",-.57,-1.29,-1.05,-2.34,-1.08,-2.34],["c",0,-.03,-.51,1.02,-1.08,2.34],["c",-.93,2.07,-1.08,2.34,-1.14,2.4],["c",-.06,.03,-.15,.06,-.18,.06],["c",-.15,0,-.33,-.18,-.33,-.33],["c",0,-.06,3.24,-7.32,3.45,-7.71],["c",.03,-.06,.09,-.15,.15,-.15],["z"]],w:7.5,h:8.245},"scripts.dmarcato":{d:[["M",-3.57,.03],["c",.03,0,.57,-.03,1.17,-.03],["c",1.05,0,1.08,0,1.17,.06],["c",.09,.06,.24,.36,1.17,2.4],["c",.57,1.29,1.05,2.34,1.08,2.34],["c",0,.03,.51,-1.02,1.08,-2.34],["c",.93,-2.07,1.08,-2.34,1.14,-2.4],["c",.06,-.03,.15,-.06,.18,-.06],["c",.15,0,.33,.18,.33,.33],["c",0,.09,-3.45,7.74,-3.54,7.83],["c",-.12,.12,-.3,.12,-.42,0],["c",-.09,-.09,-3.54,-7.74,-3.54,-7.83],["c",0,-.09,.12,-.27,.18,-.3],["z"]],w:7.5,h:8.25},"scripts.stopped":{d:[["M",-.27,-4.08],["c",.18,-.09,.36,-.09,.54,0],["c",.18,.09,.24,.15,.33,.3],["l",.06,.15],["l",0,1.5],["l",0,1.47],["l",1.47,0],["l",1.5,0],["l",.15,.06],["c",.15,.09,.21,.15,.3,.33],["c",.09,.18,.09,.36,0,.54],["c",-.09,.18,-.15,.24,-.33,.33],["c",-.12,.06,-.18,.06,-1.62,.06],["l",-1.47,0],["l",0,1.47],["l",0,1.47],["l",-.06,.15],["c",-.09,.18,-.15,.24,-.33,.33],["c",-.18,.09,-.36,.09,-.54,0],["c",-.18,-.09,-.24,-.15,-.33,-.33],["l",-.06,-.15],["l",0,-1.47],["l",0,-1.47],["l",-1.47,0],["c",-1.44,0,-1.5,0,-1.62,-.06],["c",-.18,-.09,-.24,-.15,-.33,-.33],["c",-.09,-.18,-.09,-.36,0,-.54],["c",.09,-.18,.15,-.24,.33,-.33],["l",.15,-.06],["l",1.47,0],["l",1.47,0],["l",0,-1.47],["c",0,-1.44,0,-1.5,.06,-1.62],["c",.09,-.18,.15,-.24,.33,-.33],["z"]],w:8.295,h:8.295},"scripts.upbow":{d:[["M",-4.65,-15.54],["c",.12,-.09,.36,-.06,.48,.03],["c",.03,.03,.09,.09,.12,.15],["c",.03,.06,.66,2.13,1.41,4.62],["c",1.35,4.41,1.38,4.56,2.01,6.96],["l",.63,2.46],["l",.63,-2.46],["c",.63,-2.4,.66,-2.55,2.01,-6.96],["c",.75,-2.49,1.38,-4.56,1.41,-4.62],["c",.06,-.15,.18,-.21,.36,-.24],["c",.15,0,.3,.06,.39,.18],["c",.15,.21,.24,-.18,-2.1,7.56],["c",-1.2,3.96,-2.22,7.32,-2.25,7.41],["c",0,.12,-.06,.27,-.09,.3],["c",-.12,.21,-.6,.21,-.72,0],["c",-.03,-.03,-.09,-.18,-.09,-.3],["c",-.03,-.09,-1.05,-3.45,-2.25,-7.41],["c",-2.34,-7.74,-2.25,-7.35,-2.1,-7.56],["c",.03,-.03,.09,-.09,.15,-.12],["z"]],w:9.73,h:15.608},"scripts.downbow":{d:[["M",-5.55,-9.93],["l",.09,-.06],["l",5.46,0],["l",5.46,0],["l",.09,.06],["l",.06,.09],["l",0,4.77],["c",0,5.28,0,4.89,-.18,5.01],["c",-.18,.12,-.42,.06,-.54,-.12],["c",-.06,-.09,-.06,-.18,-.06,-2.97],["l",0,-2.85],["l",-4.83,0],["l",-4.83,0],["l",0,2.85],["c",0,2.79,0,2.88,-.06,2.97],["c",-.15,.24,-.51,.24,-.66,0],["c",-.06,-.09,-.06,-.21,-.06,-4.89],["l",0,-4.77],["z"]],w:11.22,h:9.992},"scripts.turn":{d:[["M",-4.77,-3.9],["c",.36,-.06,1.05,-.06,1.44,.03],["c",.78,.15,1.5,.51,2.34,1.14],["c",.6,.45,1.05,.87,2.22,2.01],["c",1.11,1.08,1.62,1.5,2.22,1.86],["c",.6,.36,1.32,.57,1.92,.57],["c",.9,0,1.71,-.57,1.89,-1.35],["c",.24,-.93,-.39,-1.89,-1.35,-2.1],["l",-.15,-.06],["l",-.09,.15],["c",-.03,.09,-.15,.24,-.24,.33],["c",-.72,.72,-2.04,.54,-2.49,-.36],["c",-.48,-.93,.03,-1.86,1.17,-2.19],["c",.3,-.09,1.02,-.09,1.35,0],["c",.99,.27,1.74,.87,2.25,1.83],["c",.69,1.41,.63,3,-.21,4.26],["c",-.21,.3,-.69,.81,-.99,1.02],["c",-.3,.21,-.84,.45,-1.17,.54],["c",-1.23,.36,-2.49,.15,-3.72,-.6],["c",-.75,-.48,-1.41,-1.02,-2.85,-2.46],["c",-1.11,-1.08,-1.62,-1.5,-2.22,-1.86],["c",-.6,-.36,-1.32,-.57,-1.92,-.57],["c",-.9,0,-1.71,.57,-1.89,1.35],["c",-.24,.93,.39,1.89,1.35,2.1],["l",.15,.06],["l",.09,-.15],["c",.03,-.09,.15,-.24,.24,-.33],["c",.72,-.72,2.04,-.54,2.49,.36],["c",.48,.93,-.03,1.86,-1.17,2.19],["c",-.3,.09,-1.02,.09,-1.35,0],["c",-.99,-.27,-1.74,-.87,-2.25,-1.83],["c",-.69,-1.41,-.63,-3,.21,-4.26],["c",.21,-.3,.69,-.81,.99,-1.02],["c",.48,-.33,1.11,-.57,1.74,-.66],["z"]],w:16.366,h:7.893},"scripts.trill":{d:[["M",-.51,-16.02],["c",.12,-.09,.21,-.18,.21,-.18],["l",-.81,4.02],["l",-.81,4.02],["c",.03,0,.51,-.27,1.08,-.6],["c",.6,-.3,1.14,-.63,1.26,-.66],["c",1.14,-.54,2.31,-.6,3.09,-.18],["c",.27,.15,.54,.36,.6,.51],["l",.06,.12],["l",.21,-.21],["c",.9,-.81,2.22,-.99,3.12,-.42],["c",.6,.42,.9,1.14,.78,2.07],["c",-.15,1.29,-1.05,2.31,-1.95,2.25],["c",-.48,-.03,-.78,-.3,-.96,-.81],["c",-.09,-.27,-.09,-.9,-.03,-1.2],["c",.21,-.75,.81,-1.23,1.59,-1.32],["l",.24,-.03],["l",-.09,-.12],["c",-.51,-.66,-1.62,-.63,-2.31,.03],["c",-.39,.42,-.3,.09,-1.23,4.77],["l",-.81,4.14],["c",-.03,0,-.12,-.03,-.21,-.09],["c",-.33,-.15,-.54,-.18,-.99,-.18],["c",-.42,0,-.66,.03,-1.05,.18],["c",-.12,.06,-.21,.09,-.21,.09],["c",0,-.03,.36,-1.86,.81,-4.11],["c",.9,-4.47,.87,-4.26,.69,-4.53],["c",-.21,-.36,-.66,-.51,-1.17,-.36],["c",-.15,.06,-2.22,1.14,-2.58,1.38],["c",-.12,.09,-.12,.09,-.21,.6],["l",-.09,.51],["l",.21,.24],["c",.63,.75,1.02,1.47,1.2,2.19],["c",.06,.27,.06,.36,.06,.81],["c",0,.42,0,.54,-.06,.78],["c",-.15,.54,-.33,.93,-.63,1.35],["c",-.18,.24,-.57,.63,-.81,.78],["c",-.24,.15,-.63,.36,-.84,.42],["c",-.27,.06,-.66,.06,-.87,.03],["c",-.81,-.18,-1.32,-1.05,-1.38,-2.46],["c",-.03,-.6,.03,-.99,.33,-2.46],["c",.21,-1.08,.24,-1.32,.21,-1.29],["c",-1.2,.48,-2.4,.75,-3.21,.72],["c",-.69,-.06,-1.17,-.3,-1.41,-.72],["c",-.39,-.75,-.12,-1.8,.66,-2.46],["c",.24,-.18,.69,-.42,1.02,-.51],["c",.69,-.18,1.53,-.15,2.31,.09],["c",.3,.09,.75,.3,.99,.45],["c",.12,.09,.15,.09,.15,.03],["c",.03,-.03,.33,-1.59,.72,-3.45],["c",.36,-1.86,.66,-3.42,.69,-3.45],["c",0,-.03,.03,-.03,.21,.03],["c",.21,.06,.27,.06,.48,.06],["c",.42,-.03,.78,-.18,1.26,-.48],["c",.15,-.12,.36,-.27,.48,-.39],["z"],["m",-5.73,7.68],["c",-.27,-.03,-.96,-.06,-1.2,-.03],["c",-.81,.12,-1.35,.57,-1.5,1.2],["c",-.18,.66,.12,1.14,.75,1.29],["c",.66,.12,1.92,-.12,3.18,-.66],["l",.33,-.15],["l",.09,-.39],["c",.06,-.21,.09,-.42,.09,-.45],["c",0,-.03,-.45,-.3,-.75,-.45],["c",-.27,-.15,-.66,-.27,-.99,-.36],["z"],["m",4.29,3.63],["c",-.24,-.39,-.51,-.75,-.51,-.69],["c",-.06,.12,-.39,1.92,-.45,2.28],["c",-.09,.54,-.12,1.14,-.06,1.38],["c",.06,.42,.21,.6,.51,.57],["c",.39,-.06,.75,-.48,.93,-1.14],["c",.09,-.33,.09,-1.05,0,-1.38],["c",-.09,-.39,-.24,-.69,-.42,-1.02],["z"]],w:17.963,h:16.49},"scripts.segno":{d:[["M",-3.72,-11.22],["c",.78,-.09,1.59,.03,2.31,.42],["c",1.2,.6,2.01,1.71,2.31,3.09],["c",.09,.42,.09,1.2,.03,1.5],["c",-.15,.45,-.39,.81,-.66,.93],["c",-.33,.18,-.84,.21,-1.23,.15],["c",-.81,-.18,-1.32,-.93,-1.26,-1.89],["c",.03,-.36,.09,-.57,.24,-.9],["c",.15,-.33,.45,-.6,.72,-.75],["c",.12,-.06,.18,-.09,.18,-.12],["c",0,-.03,-.03,-.15,-.09,-.24],["c",-.18,-.45,-.54,-.87,-.96,-1.08],["c",-1.11,-.57,-2.34,-.18,-2.88,.9],["c",-.24,.51,-.33,1.11,-.24,1.83],["c",.27,1.92,1.5,3.54,3.93,5.13],["c",.48,.33,1.26,.78,1.29,.78],["c",.03,0,1.35,-2.19,2.94,-4.89],["l",2.88,-4.89],["l",.84,0],["l",.87,0],["l",-.03,.06],["c",-.15,.21,-6.15,10.41,-6.15,10.44],["c",0,0,.21,.15,.48,.27],["c",2.61,1.47,4.35,3.03,5.13,4.65],["c",1.14,2.34,.51,5.07,-1.44,6.39],["c",-.66,.42,-1.32,.63,-2.13,.69],["c",-2.01,.09,-3.81,-1.41,-4.26,-3.54],["c",-.09,-.42,-.09,-1.2,-.03,-1.5],["c",.15,-.45,.39,-.81,.66,-.93],["c",.33,-.18,.84,-.21,1.23,-.15],["c",.81,.18,1.32,.93,1.26,1.89],["c",-.03,.36,-.09,.57,-.24,.9],["c",-.15,.33,-.45,.6,-.72,.75],["c",-.12,.06,-.18,.09,-.18,.12],["c",0,.03,.03,.15,.09,.24],["c",.18,.45,.54,.87,.96,1.08],["c",1.11,.57,2.34,.18,2.88,-.9],["c",.24,-.51,.33,-1.11,.24,-1.83],["c",-.27,-1.92,-1.5,-3.54,-3.93,-5.13],["c",-.48,-.33,-1.26,-.78,-1.29,-.78],["c",-.03,0,-1.35,2.19,-2.91,4.89],["l",-2.88,4.89],["l",-.87,0],["l",-.87,0],["l",.03,-.06],["c",.15,-.21,6.15,-10.41,6.15,-10.44],["c",0,0,-.21,-.15,-.48,-.3],["c",-2.61,-1.44,-4.35,-3,-5.13,-4.62],["c",-.9,-1.89,-.72,-4.02,.48,-5.52],["c",.69,-.84,1.68,-1.41,2.73,-1.53],["z"],["m",8.76,9.09],["c",.03,-.03,.15,-.03,.27,-.03],["c",.33,.03,.57,.18,.72,.48],["c",.09,.18,.09,.57,0,.75],["c",-.09,.18,-.21,.3,-.36,.39],["c",-.15,.06,-.21,.06,-.39,.06],["c",-.21,0,-.27,0,-.39,-.06],["c",-.3,-.15,-.48,-.45,-.48,-.75],["c",0,-.39,.24,-.72,.63,-.84],["z"],["m",-10.53,2.61],["c",.03,-.03,.15,-.03,.27,-.03],["c",.33,.03,.57,.18,.72,.48],["c",.09,.18,.09,.57,0,.75],["c",-.09,.18,-.21,.3,-.36,.39],["c",-.15,.06,-.21,.06,-.39,.06],["c",-.21,0,-.27,0,-.39,-.06],["c",-.3,-.15,-.48,-.45,-.48,-.75],["c",0,-.39,.24,-.72,.63,-.84],["z"]],w:15,h:22.504},"scripts.coda":{d:[["M",-.21,-10.47],["c",.18,-.12,.42,-.06,.54,.12],["c",.06,.09,.06,.18,.06,1.5],["l",0,1.38],["l",.18,0],["c",.39,.06,.96,.24,1.38,.48],["c",1.68,.93,2.82,3.24,3.03,6.12],["c",.03,.24,.03,.45,.03,.45],["c",0,.03,.6,.03,1.35,.03],["c",1.5,0,1.47,0,1.59,.18],["c",.09,.12,.09,.3,0,.42],["c",-.12,.18,-.09,.18,-1.59,.18],["c",-.75,0,-1.35,0,-1.35,.03],["c",0,0,0,.21,-.03,.42],["c",-.24,3.15,-1.53,5.58,-3.45,6.36],["c",-.27,.12,-.72,.24,-.96,.27],["l",-.18,0],["l",0,1.38],["c",0,1.32,0,1.41,-.06,1.5],["c",-.15,.24,-.51,.24,-.66,0],["c",-.06,-.09,-.06,-.18,-.06,-1.5],["l",0,-1.38],["l",-.18,0],["c",-.39,-.06,-.96,-.24,-1.38,-.48],["c",-1.68,-.93,-2.82,-3.24,-3.03,-6.15],["c",-.03,-.21,-.03,-.42,-.03,-.42],["c",0,-.03,-.6,-.03,-1.35,-.03],["c",-1.5,0,-1.47,0,-1.59,-.18],["c",-.09,-.12,-.09,-.3,0,-.42],["c",.12,-.18,.09,-.18,1.59,-.18],["c",.75,0,1.35,0,1.35,-.03],["c",0,0,0,-.21,.03,-.45],["c",.24,-3.12,1.53,-5.55,3.45,-6.33],["c",.27,-.12,.72,-.24,.96,-.27],["l",.18,0],["l",0,-1.38],["c",0,-1.53,0,-1.5,.18,-1.62],["z"],["m",-.18,6.93],["c",0,-2.97,0,-3.15,-.06,-3.15],["c",-.09,0,-.51,.15,-.66,.21],["c",-.87,.51,-1.38,1.62,-1.56,3.51],["c",-.06,.54,-.12,1.59,-.12,2.16],["l",0,.42],["l",1.2,0],["l",1.2,0],["l",0,-3.15],["z"],["m",1.17,-3.06],["c",-.09,-.03,-.21,-.06,-.27,-.09],["l",-.12,0],["l",0,3.15],["l",0,3.15],["l",1.2,0],["l",1.2,0],["l",0,-.81],["c",-.06,-2.4,-.33,-3.69,-.93,-4.59],["c",-.27,-.39,-.66,-.69,-1.08,-.81],["z"],["m",-1.17,10.14],["l",0,-3.15],["l",-1.2,0],["l",-1.2,0],["l",0,.81],["c",.03,.96,.06,1.47,.15,2.13],["c",.24,2.04,.96,3.12,2.13,3.36],["l",.12,0],["l",0,-3.15],["z"],["m",3.18,-2.34],["l",0,-.81],["l",-1.2,0],["l",-1.2,0],["l",0,3.15],["l",0,3.15],["l",.12,0],["c",1.17,-.24,1.89,-1.32,2.13,-3.36],["c",.09,-.66,.12,-1.17,.15,-2.13],["z"]],w:16.035,h:21.062},"scripts.comma":{d:[["M",1.14,-4.62],["c",.3,-.12,.69,-.03,.93,.15],["c",.12,.12,.36,.45,.51,.78],["c",.9,1.77,.54,4.05,-1.08,6.75],["c",-.36,.63,-.87,1.38,-.96,1.44],["c",-.18,.12,-.42,.06,-.54,-.12],["c",-.09,-.18,-.09,-.3,.12,-.6],["c",.96,-1.44,1.44,-2.97,1.38,-4.35],["c",-.06,-.93,-.3,-1.68,-.78,-2.46],["c",-.27,-.39,-.33,-.63,-.24,-.96],["c",.09,-.27,.36,-.54,.66,-.63],["z"]],w:3.042,h:9.237},"scripts.roll":{d:[["M",1.95,-6],["c",.21,-.09,.36,-.09,.57,0],["c",.39,.15,.63,.39,1.47,1.35],["c",.66,.75,.78,.87,1.08,1.05],["c",.75,.45,1.65,.42,2.4,-.06],["c",.12,-.09,.27,-.27,.54,-.6],["c",.42,-.54,.51,-.63,.69,-.63],["c",.09,0,.3,.12,.36,.21],["c",.09,.12,.12,.3,.03,.42],["c",-.06,.12,-3.15,3.9,-3.3,4.08],["c",-.06,.06,-.18,.12,-.27,.18],["c",-.27,.12,-.6,.06,-.99,-.27],["c",-.27,-.21,-.42,-.39,-1.08,-1.14],["c",-.63,-.72,-.81,-.9,-1.17,-1.08],["c",-.36,-.18,-.57,-.21,-.99,-.21],["c",-.39,0,-.63,.03,-.93,.18],["c",-.36,.15,-.51,.27,-.9,.81],["c",-.24,.27,-.45,.51,-.48,.54],["c",-.12,.09,-.27,.06,-.39,0],["c",-.24,-.15,-.33,-.39,-.21,-.6],["c",.09,-.12,3.18,-3.87,3.33,-4.02],["c",.06,-.06,.18,-.15,.24,-.21],["z"]],w:10.817,h:6.125},"scripts.prall":{d:[["M",-4.38,-3.69],["c",.06,-.03,.18,-.06,.24,-.06],["c",.3,0,.27,-.03,1.89,1.95],["l",1.53,1.83],["c",.03,0,.57,-.84,1.23,-1.83],["c",1.14,-1.68,1.23,-1.83,1.35,-1.89],["c",.06,-.03,.18,-.06,.24,-.06],["c",.3,0,.27,-.03,1.89,1.95],["l",1.53,1.83],["l",.48,-.69],["c",.51,-.78,.54,-.84,.69,-.9],["c",.42,-.18,.87,.15,.81,.6],["c",-.03,.12,-.3,.51,-1.5,2.37],["c",-1.38,2.07,-1.5,2.22,-1.62,2.28],["c",-.06,.03,-.18,.06,-.24,.06],["c",-.3,0,-.27,.03,-1.89,-1.95],["l",-1.53,-1.83],["c",-.03,0,-.57,.84,-1.23,1.83],["c",-1.14,1.68,-1.23,1.83,-1.35,1.89],["c",-.06,.03,-.18,.06,-.24,.06],["c",-.3,0,-.27,.03,-1.89,-1.95],["l",-1.53,-1.83],["l",-.48,.69],["c",-.51,.78,-.54,.84,-.69,.9],["c",-.42,.18,-.87,-.15,-.81,-.6],["c",.03,-.12,.3,-.51,1.5,-2.37],["c",1.38,-2.07,1.5,-2.22,1.62,-2.28],["z"]],w:15.011,h:7.5},"scripts.arpeggio":{d:[["M",1.5,0],["c",1.5,2,1.5,3,1.5,3],["s",0,1,-2,1.5],["s",-.5,3,1,5.5],["l",1.5,0],["s",-1.75,-2,-1.9,-3.25],["s",2.15,-.6,2.95,-1.6],["s",.45,-1,.5,-1.25],["s",0,-1,-2,-3.9],["l",-1.5,0],["z"]],w:5,h:10},"scripts.mordent":{d:[["M",-.21,-4.95],["c",.27,-.15,.63,0,.75,.27],["c",.06,.12,.06,.24,.06,1.44],["l",0,1.29],["l",.57,-.84],["c",.51,-.75,.57,-.84,.69,-.9],["c",.06,-.03,.18,-.06,.24,-.06],["c",.3,0,.27,-.03,1.89,1.95],["l",1.53,1.83],["l",.48,-.69],["c",.51,-.78,.54,-.84,.69,-.9],["c",.42,-.18,.87,.15,.81,.6],["c",-.03,.12,-.3,.51,-1.5,2.37],["c",-1.38,2.07,-1.5,2.22,-1.62,2.28],["c",-.06,.03,-.18,.06,-.24,.06],["c",-.3,0,-.27,.03,-1.83,-1.89],["c",-.81,-.99,-1.5,-1.8,-1.53,-1.86],["c",-.06,-.03,-.06,-.03,-.12,.03],["c",-.06,.06,-.06,.15,-.06,2.28],["c",0,1.95,0,2.25,-.06,2.34],["c",-.18,.45,-.81,.48,-1.05,.03],["c",-.03,-.06,-.06,-.24,-.06,-1.41],["l",0,-1.35],["l",-.57,.84],["c",-.54,.78,-.6,.87,-.72,.93],["c",-.06,.03,-.18,.06,-.24,.06],["c",-.3,0,-.27,.03,-1.89,-1.95],["l",-1.53,-1.83],["l",-.48,.69],["c",-.51,.78,-.54,.84,-.69,.9],["c",-.42,.18,-.87,-.15,-.81,-.6],["c",.03,-.12,.3,-.51,1.5,-2.37],["c",1.38,-2.07,1.5,-2.22,1.62,-2.28],["c",.06,-.03,.18,-.06,.24,-.06],["c",.3,0,.27,-.03,1.89,1.95],["l",1.53,1.83],["c",.03,0,.06,-.06,.09,-.09],["c",.06,-.12,.06,-.15,.06,-2.28],["c",0,-1.92,0,-2.22,.06,-2.31],["c",.06,-.15,.15,-.24,.3,-.3],["z"]],w:15.011,h:10.012},"flags.u8th":{d:[["M",-.42,3.75],["l",0,-3.75],["l",.21,0],["l",.21,0],["l",0,.18],["c",0,.3,.06,.84,.12,1.23],["c",.24,1.53,.9,3.12,2.13,5.16],["l",.99,1.59],["c",.87,1.44,1.38,2.34,1.77,3.09],["c",.81,1.68,1.2,3.06,1.26,4.53],["c",.03,1.53,-.21,3.27,-.75,5.01],["c",-.21,.69,-.51,1.5,-.6,1.59],["c",-.09,.12,-.27,.21,-.42,.21],["c",-.15,0,-.42,-.12,-.51,-.21],["c",-.15,-.18,-.18,-.42,-.09,-.66],["c",.15,-.33,.45,-1.2,.57,-1.62],["c",.42,-1.38,.6,-2.58,.6,-3.9],["c",0,-.66,0,-.81,-.06,-1.11],["c",-.39,-2.07,-1.8,-4.26,-4.59,-7.14],["l",-.42,-.45],["l",-.21,0],["l",-.21,0],["l",0,-3.75],["z"]],w:6.692,h:22.59},"flags.u16th":{d:[["M",-.42,7.5],["l",0,-7.5],["l",.21,0],["l",.21,0],["l",0,.39],["c",.06,1.08,.39,2.19,.99,3.39],["c",.45,.9,.87,1.59,1.95,3.12],["c",1.29,1.86,1.77,2.64,2.22,3.57],["c",.45,.93,.72,1.8,.87,2.64],["c",.06,.51,.06,1.5,0,1.92],["c",-.12,.6,-.3,1.2,-.54,1.71],["l",-.09,.24],["l",.18,.45],["c",.51,1.2,.72,2.22,.69,3.42],["c",-.06,1.53,-.39,3.03,-.99,4.53],["c",-.3,.75,-.36,.81,-.57,.9],["c",-.15,.09,-.33,.06,-.48,0],["c",-.18,-.09,-.27,-.18,-.33,-.33],["c",-.09,-.18,-.06,-.3,.12,-.75],["c",.66,-1.41,1.02,-2.88,1.08,-4.32],["c",0,-.6,-.03,-1.05,-.18,-1.59],["c",-.3,-1.2,-.99,-2.4,-2.25,-3.87],["c",-.42,-.48,-1.53,-1.62,-2.19,-2.22],["l",-.45,-.42],["l",-.03,1.11],["l",0,1.11],["l",-.21,0],["l",-.21,0],["l",0,-7.5],["z"],["m",1.65,.09],["c",-.3,-.3,-.69,-.72,-.9,-.87],["l",-.33,-.33],["l",0,.15],["c",0,.3,.06,.81,.15,1.26],["c",.27,1.29,.87,2.61,2.04,4.29],["c",.15,.24,.6,.87,.96,1.38],["l",1.08,1.53],["l",.42,.63],["c",.03,0,.12,-.36,.21,-.72],["c",.06,-.33,.06,-1.2,0,-1.62],["c",-.33,-1.71,-1.44,-3.48,-3.63,-5.7],["z"]],w:6.693,h:26.337},"flags.u32nd":{d:[["M",-.42,11.25],["l",0,-11.25],["l",.21,0],["l",.21,0],["l",0,.36],["c",.09,1.68,.69,3.27,2.07,5.46],["l",.87,1.35],["c",1.02,1.62,1.47,2.37,1.86,3.18],["c",.48,1.02,.78,1.92,.93,2.88],["c",.06,.48,.06,1.5,0,1.89],["c",-.09,.42,-.21,.87,-.36,1.26],["l",-.12,.3],["l",.15,.39],["c",.69,1.56,.84,2.88,.54,4.38],["c",-.09,.45,-.27,1.08,-.45,1.47],["l",-.12,.24],["l",.18,.36],["c",.33,.72,.57,1.56,.69,2.34],["c",.12,1.02,-.06,2.52,-.42,3.84],["c",-.27,.93,-.75,2.13,-.93,2.31],["c",-.18,.15,-.45,.18,-.66,.09],["c",-.18,-.09,-.27,-.18,-.33,-.33],["c",-.09,-.18,-.06,-.3,.06,-.6],["c",.21,-.36,.42,-.9,.57,-1.38],["c",.51,-1.41,.69,-3.06,.48,-4.08],["c",-.15,-.81,-.57,-1.68,-1.2,-2.55],["c",-.72,-.99,-1.83,-2.13,-3.3,-3.33],["l",-.48,-.42],["l",-.03,1.53],["l",0,1.56],["l",-.21,0],["l",-.21,0],["l",0,-11.25],["z"],["m",1.26,-3.96],["c",-.27,-.3,-.54,-.6,-.66,-.72],["l",-.18,-.21],["l",0,.42],["c",.06,.87,.24,1.74,.66,2.67],["c",.36,.87,.96,1.86,1.92,3.18],["c",.21,.33,.63,.87,.87,1.23],["c",.27,.39,.6,.84,.75,1.08],["l",.27,.39],["l",.03,-.12],["c",.12,-.45,.15,-1.05,.09,-1.59],["c",-.27,-1.86,-1.38,-3.78,-3.75,-6.33],["z"],["m",-.27,6.09],["c",-.27,-.21,-.48,-.42,-.51,-.45],["c",-.06,-.03,-.06,-.03,-.06,.21],["c",0,.9,.3,2.04,.81,3.09],["c",.48,1.02,.96,1.77,2.37,3.63],["c",.6,.78,1.05,1.44,1.29,1.77],["c",.06,.12,.15,.21,.15,.18],["c",.03,-.03,.18,-.57,.24,-.87],["c",.06,-.45,.06,-1.32,-.03,-1.74],["c",-.09,-.48,-.24,-.9,-.51,-1.44],["c",-.66,-1.35,-1.83,-2.7,-3.75,-4.38],["z"]],w:6.697,h:32.145},"flags.u64th":{d:[["M",-.42,15],["l",0,-15],["l",.21,0],["l",.21,0],["l",0,.36],["c",.06,1.2,.39,2.37,1.02,3.66],["c",.39,.81,.84,1.56,1.8,3.09],["c",.81,1.26,1.05,1.68,1.35,2.22],["c",.87,1.5,1.35,2.79,1.56,4.08],["c",.06,.54,.06,1.56,-.03,2.04],["c",-.09,.48,-.21,.99,-.36,1.35],["l",-.12,.27],["l",.12,.27],["c",.09,.15,.21,.45,.27,.66],["c",.69,1.89,.63,3.66,-.18,5.46],["l",-.18,.39],["l",.15,.33],["c",.3,.66,.51,1.44,.63,2.1],["c",.06,.48,.06,1.35,0,1.71],["c",-.15,.57,-.42,1.2,-.78,1.68],["l",-.21,.27],["l",.18,.33],["c",.57,1.05,.93,2.13,1.02,3.18],["c",.06,.72,0,1.83,-.21,2.79],["c",-.18,1.02,-.63,2.34,-1.02,3.09],["c",-.15,.33,-.48,.45,-.78,.3],["c",-.18,-.09,-.27,-.18,-.33,-.33],["c",-.09,-.18,-.06,-.3,.03,-.54],["c",.75,-1.5,1.23,-3.45,1.17,-4.89],["c",-.06,-1.02,-.42,-2.01,-1.17,-3.15],["c",-.48,-.72,-1.02,-1.35,-1.89,-2.22],["c",-.57,-.57,-1.56,-1.5,-1.92,-1.77],["l",-.12,-.09],["l",0,1.68],["l",0,1.68],["l",-.21,0],["l",-.21,0],["l",0,-15],["z"],["m",.93,-8.07],["c",-.27,-.3,-.48,-.54,-.51,-.54],["c",0,0,0,.69,.03,1.02],["c",.15,1.47,.75,2.94,2.04,4.83],["l",1.08,1.53],["c",.39,.57,.84,1.2,.99,1.44],["c",.15,.24,.3,.45,.3,.45],["c",0,0,.03,-.09,.06,-.21],["c",.36,-1.59,-.15,-3.33,-1.47,-5.4],["c",-.63,-.93,-1.35,-1.83,-2.52,-3.12],["z"],["m",.06,6.72],["c",-.24,-.21,-.48,-.42,-.51,-.45],["l",-.06,-.06],["l",0,.33],["c",0,1.2,.3,2.34,.93,3.6],["c",.45,.9,.96,1.68,2.25,3.51],["c",.39,.54,.84,1.17,1.02,1.44],["c",.21,.33,.33,.51,.33,.48],["c",.06,-.09,.21,-.63,.3,-.99],["c",.06,-.33,.06,-.45,.06,-.96],["c",0,-.6,-.03,-.84,-.18,-1.35],["c",-.3,-1.08,-1.02,-2.28,-2.13,-3.57],["c",-.39,-.45,-1.44,-1.47,-2.01,-1.98],["z"],["m",0,6.72],["c",-.24,-.21,-.48,-.39,-.51,-.42],["l",-.06,-.06],["l",0,.33],["c",0,1.41,.45,2.82,1.38,4.35],["c",.42,.72,.72,1.14,1.86,2.73],["c",.36,.45,.75,.99,.87,1.2],["c",.15,.21,.3,.36,.3,.36],["c",.06,0,.3,-.48,.39,-.75],["c",.09,-.36,.12,-.63,.12,-1.05],["c",-.06,-1.05,-.45,-2.04,-1.2,-3.18],["c",-.57,-.87,-1.11,-1.53,-2.07,-2.49],["c",-.36,-.33,-.84,-.78,-1.08,-1.02],["z"]],w:6.682,h:39.694},"flags.d8th":{d:[["M",5.67,-21.63],["c",.24,-.12,.54,-.06,.69,.15],["c",.06,.06,.21,.36,.39,.66],["c",.84,1.77,1.26,3.36,1.32,5.1],["c",.03,1.29,-.21,2.37,-.81,3.63],["c",-.6,1.23,-1.26,2.13,-3.21,4.38],["c",-1.35,1.53,-1.86,2.19,-2.4,2.97],["c",-.63,.93,-1.11,1.92,-1.38,2.79],["c",-.15,.54,-.27,1.35,-.27,1.8],["l",0,.15],["l",-.21,0],["l",-.21,0],["l",0,-3.75],["l",0,-3.75],["l",.21,0],["l",.21,0],["l",.48,-.3],["c",1.83,-1.11,3.12,-2.1,4.17,-3.12],["c",.78,-.81,1.32,-1.53,1.71,-2.31],["c",.45,-.93,.6,-1.74,.51,-2.88],["c",-.12,-1.56,-.63,-3.18,-1.47,-4.68],["c",-.12,-.21,-.15,-.33,-.06,-.51],["c",.06,-.15,.15,-.24,.33,-.33],["z"]],w:8.492,h:21.691},"flags.ugrace":{d:[["M",6.03,6.93],["c",.15,-.09,.33,-.06,.51,0],["c",.15,.09,.21,.15,.3,.33],["c",.09,.18,.06,.39,-.03,.54],["c",-.06,.15,-10.89,8.88,-11.07,8.97],["c",-.15,.09,-.33,.06,-.48,0],["c",-.18,-.09,-.24,-.15,-.33,-.33],["c",-.09,-.18,-.06,-.39,.03,-.54],["c",.06,-.15,10.89,-8.88,11.07,-8.97],["z"]],w:12.019,h:9.954},"flags.dgrace":{d:[["M",-6.06,-15.93],["c",.18,-.09,.33,-.12,.48,-.06],["c",.18,.09,14.01,8.04,14.1,8.1],["c",.12,.12,.18,.33,.18,.51],["c",-.03,.21,-.15,.39,-.36,.48],["c",-.18,.09,-.33,.12,-.48,.06],["c",-.18,-.09,-14.01,-8.04,-14.1,-8.1],["c",-.12,-.12,-.18,-.33,-.18,-.51],["c",.03,-.21,.15,-.39,.36,-.48],["z"]],w:15.12,h:9.212},"flags.d16th":{d:[["M",6.84,-22.53],["c",.27,-.12,.57,-.06,.72,.15],["c",.15,.15,.33,.87,.45,1.56],["c",.06,.33,.06,1.35,0,1.65],["c",-.06,.33,-.15,.78,-.27,1.11],["c",-.12,.33,-.45,.96,-.66,1.32],["l",-.18,.27],["l",.09,.18],["c",.48,1.02,.72,2.25,.69,3.3],["c",-.06,1.23,-.42,2.28,-1.26,3.45],["c",-.57,.87,-.99,1.32,-3,3.39],["c",-1.56,1.56,-2.22,2.4,-2.76,3.45],["c",-.42,.84,-.66,1.8,-.66,2.55],["l",0,.15],["l",-.21,0],["l",-.21,0],["l",0,-7.5],["l",0,-7.5],["l",.21,0],["l",.21,0],["l",0,1.14],["l",0,1.11],["l",.27,-.15],["c",1.11,-.57,1.77,-.99,2.52,-1.47],["c",2.37,-1.56,3.69,-3.15,4.05,-4.83],["c",.03,-.18,.03,-.39,.03,-.78],["c",0,-.6,-.03,-.93,-.24,-1.5],["c",-.06,-.18,-.12,-.39,-.15,-.45],["c",-.03,-.24,.12,-.48,.36,-.6],["z"],["m",-.63,7.5],["c",-.06,-.18,-.15,-.36,-.15,-.36],["c",-.03,0,-.03,.03,-.06,.06],["c",-.06,.12,-.96,1.02,-1.95,1.98],["c",-.63,.57,-1.26,1.17,-1.44,1.35],["c",-1.53,1.62,-2.28,2.85,-2.55,4.32],["c",-.03,.18,-.03,.54,-.06,.99],["l",0,.69],["l",.18,-.09],["c",.93,-.54,2.1,-1.29,2.82,-1.83],["c",.69,-.51,1.02,-.81,1.53,-1.29],["c",1.86,-1.89,2.37,-3.66,1.68,-5.82],["z"]],w:8.475,h:22.591},"flags.d32nd":{d:[["M",6.84,-29.13],["c",.27,-.12,.57,-.06,.72,.15],["c",.12,.12,.27,.63,.36,1.11],["c",.33,1.59,.06,3.06,-.81,4.47],["l",-.18,.27],["l",.09,.15],["c",.12,.24,.33,.69,.45,1.05],["c",.63,1.83,.45,3.57,-.57,5.22],["l",-.18,.3],["l",.15,.27],["c",.42,.87,.6,1.71,.57,2.61],["c",-.06,1.29,-.48,2.46,-1.35,3.78],["c",-.54,.81,-.93,1.29,-2.46,3],["c",-.51,.54,-1.05,1.17,-1.26,1.41],["c",-1.56,1.86,-2.25,3.36,-2.37,5.01],["l",0,.33],["l",-.21,0],["l",-.21,0],["l",0,-11.25],["l",0,-11.25],["l",.21,0],["l",.21,0],["l",0,1.35],["l",.03,1.35],["l",.78,-.39],["c",1.38,-.69,2.34,-1.26,3.24,-1.92],["c",1.38,-1.02,2.28,-2.13,2.64,-3.21],["c",.15,-.48,.18,-.72,.18,-1.29],["c",0,-.57,-.06,-.9,-.24,-1.47],["c",-.06,-.18,-.12,-.39,-.15,-.45],["c",-.03,-.24,.12,-.48,.36,-.6],["z"],["m",-.63,7.2],["c",-.09,-.18,-.12,-.21,-.12,-.15],["c",-.03,.09,-1.02,1.08,-2.04,2.04],["c",-1.17,1.08,-1.65,1.56,-2.07,2.04],["c",-.84,.96,-1.38,1.86,-1.68,2.76],["c",-.21,.57,-.27,.99,-.3,1.65],["l",0,.54],["l",.66,-.33],["c",3.57,-1.86,5.49,-3.69,5.94,-5.7],["c",.06,-.39,.06,-1.2,-.03,-1.65],["c",-.06,-.39,-.24,-.9,-.36,-1.2],["z"],["m",-.06,7.2],["c",-.06,-.15,-.12,-.33,-.15,-.45],["l",-.06,-.18],["l",-.18,.21],["l",-1.83,1.83],["c",-.87,.9,-1.77,1.8,-1.95,2.01],["c",-1.08,1.29,-1.62,2.31,-1.89,3.51],["c",-.06,.3,-.06,.51,-.09,.93],["l",0,.57],["l",.09,-.06],["c",.75,-.45,1.89,-1.26,2.52,-1.74],["c",.81,-.66,1.74,-1.53,2.22,-2.16],["c",1.26,-1.53,1.68,-3.06,1.32,-4.47],["z"]],w:8.385,h:29.191},"flags.d64th":{d:[["M",7.08,-32.88],["c",.3,-.12,.66,-.03,.78,.24],["c",.18,.33,.27,2.1,.15,2.64],["c",-.09,.39,-.21,.78,-.39,1.08],["l",-.15,.3],["l",.09,.27],["c",.03,.12,.09,.45,.12,.69],["c",.27,1.44,.18,2.55,-.3,3.6],["l",-.12,.33],["l",.06,.42],["c",.27,1.35,.33,2.82,.21,3.63],["c",-.12,.6,-.3,1.23,-.57,1.8],["l",-.15,.27],["l",.03,.42],["c",.06,1.02,.06,2.7,.03,3.06],["c",-.15,1.47,-.66,2.76,-1.74,4.41],["c",-.45,.69,-.75,1.11,-1.74,2.37],["c",-1.05,1.38,-1.5,1.98,-1.95,2.73],["c",-.93,1.5,-1.38,2.82,-1.44,4.2],["l",0,.42],["l",-.21,0],["l",-.21,0],["l",0,-15],["l",0,-15],["l",.21,0],["l",.21,0],["l",0,1.86],["l",0,1.89],["c",0,0,.21,-.03,.45,-.09],["c",2.22,-.39,4.08,-1.11,5.19,-2.01],["c",.63,-.54,1.02,-1.14,1.2,-1.8],["c",.06,-.3,.06,-1.14,-.03,-1.65],["c",-.03,-.18,-.06,-.39,-.09,-.48],["c",-.03,-.24,.12,-.48,.36,-.6],["z"],["m",-.45,6.15],["c",-.03,-.18,-.06,-.42,-.06,-.54],["l",-.03,-.18],["l",-.33,.3],["c",-.42,.36,-.87,.72,-1.68,1.29],["c",-1.98,1.38,-2.25,1.59,-2.85,2.16],["c",-.75,.69,-1.23,1.44,-1.47,2.19],["c",-.15,.45,-.18,.63,-.21,1.35],["l",0,.66],["l",.39,-.18],["c",1.83,-.9,3.45,-1.95,4.47,-2.91],["c",.93,-.9,1.53,-1.83,1.74,-2.82],["c",.06,-.33,.06,-.87,.03,-1.32],["z"],["m",-.27,4.86],["c",-.03,-.21,-.06,-.36,-.06,-.36],["c",0,-.03,-.12,.09,-.24,.24],["c",-.39,.48,-.99,1.08,-2.16,2.19],["c",-1.47,1.38,-1.92,1.83,-2.46,2.49],["c",-.66,.87,-1.08,1.74,-1.29,2.58],["c",-.09,.42,-.15,.87,-.15,1.44],["l",0,.54],["l",.48,-.33],["c",1.5,-1.02,2.58,-1.89,3.51,-2.82],["c",1.47,-1.47,2.25,-2.85,2.4,-4.26],["c",.03,-.39,.03,-1.17,-.03,-1.71],["z"],["m",-.66,7.68],["c",.03,-.15,.03,-.6,.03,-.99],["l",0,-.72],["l",-.27,.33],["l",-1.74,1.98],["c",-1.77,1.92,-2.43,2.76,-2.97,3.9],["c",-.51,1.02,-.72,1.77,-.75,2.91],["c",0,.63,0,.63,.06,.6],["c",.03,-.03,.3,-.27,.63,-.54],["c",.66,-.6,1.86,-1.8,2.31,-2.31],["c",1.65,-1.89,2.52,-3.54,2.7,-5.16],["z"]],w:8.485,h:32.932},"clefs.C":{d:[["M",.06,-14.94],["l",.09,-.06],["l",1.92,0],["l",1.92,0],["l",.09,.06],["l",.06,.09],["l",0,14.85],["l",0,14.82],["l",-.06,.09],["l",-.09,.06],["l",-1.92,0],["l",-1.92,0],["l",-.09,-.06],["l",-.06,-.09],["l",0,-14.82],["l",0,-14.85],["z"],["m",5.37,0],["c",.09,-.06,.09,-.06,.57,-.06],["c",.45,0,.45,0,.54,.06],["l",.06,.09],["l",0,7.14],["l",0,7.11],["l",.09,-.06],["c",.18,-.18,.72,-.84,.96,-1.2],["c",.3,-.45,.66,-1.17,.84,-1.65],["c",.36,-.9,.57,-1.83,.6,-2.79],["c",.03,-.48,.03,-.54,.09,-.63],["c",.12,-.18,.36,-.21,.54,-.12],["c",.18,.09,.21,.15,.24,.66],["c",.06,.87,.21,1.56,.57,2.22],["c",.51,1.02,1.26,1.68,2.22,1.92],["c",.21,.06,.33,.06,.78,.06],["c",.45,0,.57,0,.84,-.06],["c",.45,-.12,.81,-.33,1.08,-.6],["c",.57,-.57,.87,-1.41,.99,-2.88],["c",.06,-.54,.06,-3,0,-3.57],["c",-.21,-2.58,-.84,-3.87,-2.16,-4.5],["c",-.48,-.21,-1.17,-.36,-1.77,-.36],["c",-.69,0,-1.29,.27,-1.5,.72],["c",-.06,.15,-.06,.21,-.06,.42],["c",0,.24,0,.3,.06,.45],["c",.12,.24,.24,.39,.63,.66],["c",.42,.3,.57,.48,.69,.72],["c",.06,.15,.06,.21,.06,.48],["c",0,.39,-.03,.63,-.21,.96],["c",-.3,.6,-.87,1.08,-1.5,1.26],["c",-.27,.06,-.87,.06,-1.14,0],["c",-.78,-.24,-1.44,-.87,-1.65,-1.68],["c",-.12,-.42,-.09,-1.17,.09,-1.71],["c",.51,-1.65,1.98,-2.82,3.81,-3.09],["c",.84,-.09,2.46,.03,3.51,.27],["c",2.22,.57,3.69,1.8,4.44,3.75],["c",.36,.93,.57,2.13,.57,3.36],["c",0,1.44,-.48,2.73,-1.38,3.81],["c",-1.26,1.5,-3.27,2.43,-5.28,2.43],["c",-.48,0,-.51,0,-.75,-.09],["c",-.15,-.03,-.48,-.21,-.78,-.36],["c",-.69,-.36,-.87,-.42,-1.26,-.42],["c",-.27,0,-.3,0,-.51,.09],["c",-.57,.3,-.81,.9,-.81,2.1],["c",0,1.23,.24,1.83,.81,2.13],["c",.21,.09,.24,.09,.51,.09],["c",.39,0,.57,-.06,1.26,-.42],["c",.3,-.15,.63,-.33,.78,-.36],["c",.24,-.09,.27,-.09,.75,-.09],["c",2.01,0,4.02,.93,5.28,2.4],["c",.9,1.11,1.38,2.4,1.38,3.84],["c",0,1.5,-.3,2.88,-.84,3.96],["c",-.78,1.59,-2.19,2.64,-4.17,3.15],["c",-1.05,.24,-2.67,.36,-3.51,.27],["c",-1.83,-.27,-3.3,-1.44,-3.81,-3.09],["c",-.18,-.54,-.21,-1.29,-.09,-1.74],["c",.15,-.6,.63,-1.2,1.23,-1.47],["c",.36,-.18,.57,-.21,.99,-.21],["c",.42,0,.63,.03,1.02,.21],["c",.42,.21,.84,.63,1.05,1.05],["c",.18,.36,.21,.6,.21,.96],["c",0,.3,0,.36,-.06,.51],["c",-.12,.24,-.27,.42,-.69,.72],["c",-.57,.42,-.69,.63,-.69,1.08],["c",0,.24,0,.3,.06,.45],["c",.12,.21,.3,.39,.57,.54],["c",.42,.18,.87,.21,1.53,.15],["c",1.08,-.15,1.8,-.57,2.34,-1.32],["c",.54,-.75,.84,-1.83,.99,-3.51],["c",.06,-.57,.06,-3.03,0,-3.57],["c",-.12,-1.47,-.42,-2.31,-.99,-2.88],["c",-.27,-.27,-.63,-.48,-1.08,-.6],["c",-.27,-.06,-.39,-.06,-.84,-.06],["c",-.45,0,-.57,0,-.78,.06],["c",-1.14,.27,-2.01,1.17,-2.46,2.49],["c",-.21,.57,-.3,.99,-.33,1.65],["c",-.03,.51,-.06,.57,-.24,.66],["c",-.12,.06,-.27,.06,-.39,0],["c",-.21,-.09,-.21,-.15,-.24,-.75],["c",-.09,-1.92,-.78,-3.72,-2.01,-5.19],["c",-.18,-.21,-.36,-.42,-.39,-.45],["l",-.09,-.06],["l",0,7.11],["l",0,7.14],["l",-.06,.09],["c",-.09,.06,-.09,.06,-.54,.06],["c",-.48,0,-.48,0,-.57,-.06],["l",-.06,-.09],["l",0,-14.82],["l",0,-14.85],["z"]],w:20.31,h:29.97},"clefs.F":{d:[["M",6.3,-7.8],["c",.36,-.03,1.65,0,2.13,.03],["c",3.6,.42,6.03,2.1,6.93,4.86],["c",.27,.84,.36,1.5,.36,2.58],["c",0,.9,-.03,1.35,-.18,2.16],["c",-.78,3.78,-3.54,7.08,-8.37,9.96],["c",-1.74,1.05,-3.87,2.13,-6.18,3.12],["c",-.39,.18,-.75,.33,-.81,.36],["c",-.06,.03,-.15,.06,-.18,.06],["c",-.15,0,-.33,-.18,-.33,-.33],["c",0,-.15,.06,-.21,.51,-.48],["c",3,-1.77,5.13,-3.21,6.84,-4.74],["c",.51,-.45,1.59,-1.5,1.95,-1.95],["c",1.89,-2.19,2.88,-4.32,3.15,-6.78],["c",.06,-.42,.06,-1.77,0,-2.19],["c",-.24,-2.01,-.93,-3.63,-2.04,-4.71],["c",-.63,-.63,-1.29,-1.02,-2.07,-1.2],["c",-1.62,-.39,-3.36,.15,-4.56,1.44],["c",-.54,.6,-1.05,1.47,-1.32,2.22],["l",-.09,.21],["l",.24,-.12],["c",.39,-.21,.63,-.24,1.11,-.24],["c",.3,0,.45,0,.66,.06],["c",1.92,.48,2.85,2.55,1.95,4.38],["c",-.45,.99,-1.41,1.62,-2.46,1.71],["c",-1.47,.09,-2.91,-.87,-3.39,-2.25],["c",-.18,-.57,-.21,-1.32,-.03,-2.28],["c",.39,-2.25,1.83,-4.2,3.81,-5.19],["c",.69,-.36,1.59,-.6,2.37,-.69],["z"],["m",11.58,2.52],["c",.84,-.21,1.71,.3,1.89,1.14],["c",.3,1.17,-.72,2.19,-1.89,1.89],["c",-.99,-.21,-1.5,-1.32,-1.02,-2.25],["c",.18,-.39,.6,-.69,1.02,-.78],["z"],["m",0,7.5],["c",.84,-.21,1.71,.3,1.89,1.14],["c",.21,.87,-.3,1.71,-1.14,1.89],["c",-.87,.21,-1.71,-.3,-1.89,-1.14],["c",-.21,-.84,.3,-1.71,1.14,-1.89],["z"]],w:20.153,h:23.142},"clefs.G":{d:[["M",9.69,-37.41],["c",.09,-.09,.24,-.06,.36,0],["c",.12,.09,.57,.6,.96,1.11],["c",1.77,2.34,3.21,5.85,3.57,8.73],["c",.21,1.56,.03,3.27,-.45,4.86],["c",-.69,2.31,-1.92,4.47,-4.23,7.44],["c",-.3,.39,-.57,.72,-.6,.75],["c",-.03,.06,0,.15,.18,.78],["c",.54,1.68,1.38,4.44,1.68,5.49],["l",.09,.42],["l",.39,0],["c",1.47,.09,2.76,.51,3.96,1.29],["c",1.83,1.23,3.06,3.21,3.39,5.52],["c",.09,.45,.12,1.29,.06,1.74],["c",-.09,1.02,-.33,1.83,-.75,2.73],["c",-.84,1.71,-2.28,3.06,-4.02,3.72],["l",-.33,.12],["l",.03,1.26],["c",0,1.74,-.06,3.63,-.21,4.62],["c",-.45,3.06,-2.19,5.49,-4.47,6.21],["c",-.57,.18,-.9,.21,-1.59,.21],["c",-.69,0,-1.02,-.03,-1.65,-.21],["c",-1.14,-.27,-2.13,-.84,-2.94,-1.65],["c",-.99,-.99,-1.56,-2.16,-1.71,-3.54],["c",-.09,-.81,.06,-1.53,.45,-2.13],["c",.63,-.99,1.83,-1.56,3,-1.53],["c",1.5,.09,2.64,1.32,2.73,2.94],["c",.06,1.47,-.93,2.7,-2.37,2.97],["c",-.45,.06,-.84,.03,-1.29,-.09],["l",-.21,-.09],["l",.09,.12],["c",.39,.54,.78,.93,1.32,1.26],["c",1.35,.87,3.06,1.02,4.35,.36],["c",1.44,-.72,2.52,-2.28,2.97,-4.35],["c",.15,-.66,.24,-1.5,.3,-3.03],["c",.03,-.84,.03,-2.94,0,-3],["c",-.03,0,-.18,0,-.36,.03],["c",-.66,.12,-.99,.12,-1.83,.12],["c",-1.05,0,-1.71,-.06,-2.61,-.3],["c",-4.02,-.99,-7.11,-4.35,-7.8,-8.46],["c",-.12,-.66,-.12,-.99,-.12,-1.83],["c",0,-.84,0,-1.14,.15,-1.92],["c",.36,-2.28,1.41,-4.62,3.3,-7.29],["l",2.79,-3.6],["c",.54,-.66,.96,-1.2,.96,-1.23],["c",0,-.03,-.09,-.33,-.18,-.69],["c",-.96,-3.21,-1.41,-5.28,-1.59,-7.68],["c",-.12,-1.38,-.15,-3.09,-.06,-3.96],["c",.33,-2.67,1.38,-5.07,3.12,-7.08],["c",.36,-.42,.99,-1.05,1.17,-1.14],["z"],["m",2.01,4.71],["c",-.15,-.3,-.3,-.54,-.3,-.54],["c",-.03,0,-.18,.09,-.3,.21],["c",-2.4,1.74,-3.87,4.2,-4.26,7.11],["c",-.06,.54,-.06,1.41,-.03,1.89],["c",.09,1.29,.48,3.12,1.08,5.22],["c",.15,.42,.24,.78,.24,.81],["c",0,.03,.84,-1.11,1.23,-1.68],["c",1.89,-2.73,2.88,-5.07,3.15,-7.53],["c",.09,-.57,.12,-1.74,.06,-2.37],["c",-.09,-1.23,-.27,-1.92,-.87,-3.12],["z"],["m",-2.94,20.7],["c",-.21,-.72,-.39,-1.32,-.42,-1.32],["c",0,0,-1.2,1.47,-1.86,2.37],["c",-2.79,3.63,-4.02,6.3,-4.35,9.3],["c",-.03,.21,-.03,.69,-.03,1.08],["c",0,.69,0,.75,.06,1.11],["c",.12,.54,.27,.99,.51,1.47],["c",.69,1.38,1.83,2.55,3.42,3.42],["c",.96,.54,2.07,.9,3.21,1.08],["c",.78,.12,2.04,.12,2.94,-.03],["c",.51,-.06,.45,-.03,.42,-.3],["c",-.24,-3.33,-.72,-6.33,-1.62,-10.08],["c",-.09,-.39,-.18,-.75,-.18,-.78],["c",-.03,-.03,-.42,0,-.81,.09],["c",-.9,.18,-1.65,.57,-2.22,1.14],["c",-.72,.72,-1.08,1.65,-1.05,2.64],["c",.06,.96,.48,1.83,1.23,2.58],["c",.36,.36,.72,.63,1.17,.9],["c",.33,.18,.36,.21,.42,.33],["c",.18,.42,-.18,.9,-.6,.87],["c",-.18,-.03,-.84,-.36,-1.26,-.63],["c",-.78,-.51,-1.38,-1.11,-1.86,-1.83],["c",-1.77,-2.7,-.99,-6.42,1.71,-8.19],["c",.3,-.21,.81,-.48,1.17,-.63],["c",.3,-.09,1.02,-.3,1.14,-.3],["c",.06,0,.09,0,.09,-.03],["c",.03,-.03,-.51,-1.92,-1.23,-4.26],["z"],["m",3.78,7.41],["c",-.18,-.03,-.36,-.06,-.39,-.06],["c",-.03,0,0,.21,.18,1.02],["c",.75,3.18,1.26,6.3,1.5,9.09],["c",.06,.72,0,.69,.51,.42],["c",.78,-.36,1.44,-.96,1.98,-1.77],["c",1.08,-1.62,1.2,-3.69,.3,-5.55],["c",-.81,-1.62,-2.31,-2.79,-4.08,-3.15],["z"]],w:19.051,h:57.057},"clefs.perc":{d:[["M",5.07,-7.44],["l",.09,-.06],["l",1.53,0],["l",1.53,0],["l",.09,.06],["l",.06,.09],["l",0,7.35],["l",0,7.32],["l",-.06,.09],["l",-.09,.06],["l",-1.53,0],["l",-1.53,0],["l",-.09,-.06],["l",-.06,-.09],["l",0,-7.32],["l",0,-7.35],["z"],["m",6.63,0],["l",.09,-.06],["l",1.53,0],["l",1.53,0],["l",.09,.06],["l",.06,.09],["l",0,7.35],["l",0,7.32],["l",-.06,.09],["l",-.09,.06],["l",-1.53,0],["l",-1.53,0],["l",-.09,-.06],["l",-.06,-.09],["l",0,-7.32],["l",0,-7.35],["z"]],w:9.99,h:14.97},"timesig.common":{d:[["M",6.66,-7.83],["c",.72,-.06,1.41,-.03,1.98,.09],["c",1.2,.27,2.34,.96,3.09,1.92],["c",.63,.81,1.08,1.86,1.14,2.73],["c",.06,1.02,-.51,1.92,-1.44,2.22],["c",-.24,.09,-.3,.09,-.63,.09],["c",-.33,0,-.42,0,-.63,-.06],["c",-.66,-.24,-1.14,-.63,-1.41,-1.2],["c",-.15,-.3,-.21,-.51,-.24,-.9],["c",-.06,-1.08,.57,-2.04,1.56,-2.37],["c",.18,-.06,.27,-.06,.63,-.06],["l",.45,0],["c",.06,.03,.09,.03,.09,0],["c",0,0,-.09,-.12,-.24,-.27],["c",-1.02,-1.11,-2.55,-1.68,-4.08,-1.5],["c",-1.29,.15,-2.04,.69,-2.4,1.74],["c",-.36,.93,-.42,1.89,-.42,5.37],["c",0,2.97,.06,3.96,.24,4.77],["c",.24,1.08,.63,1.68,1.41,2.07],["c",.81,.39,2.16,.45,3.18,.09],["c",1.29,-.45,2.37,-1.53,3.03,-2.97],["c",.15,-.33,.33,-.87,.39,-1.17],["c",.09,-.24,.15,-.36,.3,-.39],["c",.21,-.03,.42,.15,.39,.36],["c",-.06,.39,-.42,1.38,-.69,1.89],["c",-.96,1.8,-2.49,2.94,-4.23,3.18],["c",-.99,.12,-2.58,-.06,-3.63,-.45],["c",-.96,-.36,-1.71,-.84,-2.4,-1.5],["c",-1.11,-1.11,-1.8,-2.61,-2.04,-4.56],["c",-.06,-.6,-.06,-2.01,0,-2.61],["c",.24,-1.95,.9,-3.45,2.01,-4.56],["c",.69,-.66,1.44,-1.11,2.37,-1.47],["c",.63,-.24,1.47,-.42,2.22,-.48],["z"]],w:13.038,h:15.689},"timesig.cut":{d:[["M",6.24,-10.44],["c",.09,-.06,.09,-.06,.48,-.06],["c",.36,0,.36,0,.45,.06],["l",.06,.09],["l",0,1.23],["l",0,1.26],["l",.27,0],["c",1.26,0,2.49,.45,3.48,1.29],["c",1.05,.87,1.8,2.28,1.89,3.48],["c",.06,1.02,-.51,1.92,-1.44,2.22],["c",-.24,.09,-.3,.09,-.63,.09],["c",-.33,0,-.42,0,-.63,-.06],["c",-.66,-.24,-1.14,-.63,-1.41,-1.2],["c",-.15,-.3,-.21,-.51,-.24,-.9],["c",-.06,-1.08,.57,-2.04,1.56,-2.37],["c",.18,-.06,.27,-.06,.63,-.06],["l",.45,0],["c",.06,.03,.09,.03,.09,0],["c",0,-.03,-.45,-.51,-.66,-.69],["c",-.87,-.69,-1.83,-1.05,-2.94,-1.11],["l",-.42,0],["l",0,7.17],["l",0,7.14],["l",.42,0],["c",.69,-.03,1.23,-.18,1.86,-.51],["c",1.05,-.51,1.89,-1.47,2.46,-2.7],["c",.15,-.33,.33,-.87,.39,-1.17],["c",.09,-.24,.15,-.36,.3,-.39],["c",.21,-.03,.42,.15,.39,.36],["c",-.03,.24,-.21,.78,-.39,1.2],["c",-.96,2.37,-2.94,3.9,-5.13,3.9],["l",-.3,0],["l",0,1.26],["l",0,1.23],["l",-.06,.09],["c",-.09,.06,-.09,.06,-.45,.06],["c",-.39,0,-.39,0,-.48,-.06],["l",-.06,-.09],["l",0,-1.29],["l",0,-1.29],["l",-.21,-.03],["c",-1.23,-.21,-2.31,-.63,-3.21,-1.29],["c",-.15,-.09,-.45,-.36,-.66,-.57],["c",-1.11,-1.11,-1.8,-2.61,-2.04,-4.56],["c",-.06,-.6,-.06,-2.01,0,-2.61],["c",.24,-1.95,.93,-3.45,2.04,-4.59],["c",.42,-.39,.78,-.66,1.26,-.93],["c",.75,-.45,1.65,-.75,2.61,-.9],["l",.21,-.03],["l",0,-1.29],["l",0,-1.29],["z"],["m",-.06,10.44],["c",0,-5.58,0,-6.99,-.03,-6.99],["c",-.15,0,-.63,.27,-.87,.45],["c",-.45,.36,-.75,.93,-.93,1.77],["c",-.18,.81,-.24,1.8,-.24,4.74],["c",0,2.97,.06,3.96,.24,4.77],["c",.24,1.08,.66,1.68,1.41,2.07],["c",.12,.06,.3,.12,.33,.15],["l",.09,0],["l",0,-6.96],["z"]],w:13.038,h:20.97},"timesig.imperfectum":{d:[["M",13,-5],["a",8,8,0,1,0,0,10]],w:13.038,h:20.97},"timesig.imperfectum2":{d:[["M",13,-5],["a",8,8,0,1,0,0,10]],w:13.038,h:20.97},"timesig.perfectum":{d:[["M",13,-5],["a",8,8,0,1,0,0,10]],w:13.038,h:20.97},"timesig.perfectum2":{d:[["M",13,-5],["a",8,8,0,1,0,0,10]],w:13.038,h:20.97},f:{d:[["M",9.93,-14.28],["c",1.53,-.18,2.88,.45,3.12,1.5],["c",.12,.51,0,1.32,-.27,1.86],["c",-.15,.3,-.42,.57,-.63,.69],["c",-.69,.36,-1.56,.03,-1.83,-.69],["c",-.09,-.24,-.09,-.69,0,-.87],["c",.06,-.12,.21,-.24,.45,-.42],["c",.42,-.24,.57,-.45,.6,-.72],["c",.03,-.33,-.09,-.39,-.63,-.42],["c",-.3,0,-.45,0,-.6,.03],["c",-.81,.21,-1.35,.93,-1.74,2.46],["c",-.06,.27,-.48,2.25,-.48,2.31],["c",0,.03,.39,.03,.9,.03],["c",.72,0,.9,0,.99,.06],["c",.42,.15,.45,.72,.03,.9],["c",-.12,.06,-.24,.06,-1.17,.06],["l",-1.05,0],["l",-.78,2.55],["c",-.45,1.41,-.87,2.79,-.96,3.06],["c",-.87,2.37,-2.37,4.74,-3.78,5.91],["c",-1.05,.9,-2.04,1.23,-3.09,1.08],["c",-1.11,-.18,-1.89,-.78,-2.04,-1.59],["c",-.12,-.66,.15,-1.71,.54,-2.19],["c",.69,-.75,1.86,-.54,2.22,.39],["c",.06,.15,.09,.27,.09,.48],["c",0,.24,-.03,.27,-.12,.42],["c",-.03,.09,-.15,.18,-.27,.27],["c",-.09,.06,-.27,.21,-.36,.27],["c",-.24,.18,-.36,.36,-.39,.6],["c",-.03,.33,.09,.39,.63,.42],["c",.42,0,.63,-.03,.9,-.15],["c",.6,-.3,.96,-.96,1.38,-2.64],["c",.09,-.42,.63,-2.55,1.17,-4.77],["l",1.02,-4.08],["c",0,-.03,-.36,-.03,-.81,-.03],["c",-.72,0,-.81,0,-.93,-.06],["c",-.42,-.18,-.39,-.75,.03,-.9],["c",.09,-.06,.27,-.06,1.05,-.06],["l",.96,0],["l",0,-.09],["c",.06,-.18,.3,-.72,.51,-1.17],["c",1.2,-2.46,3.3,-4.23,5.34,-4.5],["z"]],w:16.155,h:19.445},m:{d:[["M",2.79,-8.91],["c",.09,0,.3,-.03,.45,-.03],["c",.24,.03,.3,.03,.45,.12],["c",.36,.15,.63,.54,.75,1.02],["l",.03,.21],["l",.33,-.3],["c",.69,-.69,1.38,-1.02,2.07,-1.02],["c",.27,0,.33,0,.48,.06],["c",.21,.09,.48,.36,.63,.6],["c",.03,.09,.12,.27,.18,.42],["c",.03,.15,.09,.27,.12,.27],["c",0,0,.09,-.09,.18,-.21],["c",.33,-.39,.87,-.81,1.29,-.99],["c",.78,-.33,1.47,-.21,2.01,.33],["c",.3,.33,.48,.69,.6,1.14],["c",.09,.42,.06,.54,-.54,3.06],["c",-.33,1.29,-.57,2.4,-.57,2.43],["c",0,.12,.09,.21,.21,.21],["c",.24,0,.75,-.3,1.2,-.72],["c",.45,-.39,.6,-.45,.78,-.27],["c",.18,.18,.09,.36,-.45,.87],["c",-1.05,.96,-1.83,1.47,-2.58,1.71],["c",-.93,.33,-1.53,.21,-1.8,-.33],["c",-.06,-.15,-.06,-.21,-.06,-.45],["c",0,-.24,.03,-.48,.6,-2.82],["c",.42,-1.71,.6,-2.64,.63,-2.79],["c",.03,-.57,-.3,-.75,-.84,-.48],["c",-.24,.12,-.54,.39,-.66,.63],["c",-.03,.09,-.42,1.38,-.9,3],["c",-.9,3.15,-.84,3,-1.14,3.15],["l",-.15,.09],["l",-.78,0],["c",-.6,0,-.78,0,-.84,-.06],["c",-.09,-.03,-.18,-.18,-.18,-.27],["c",0,-.03,.36,-1.38,.84,-2.97],["c",.57,-2.04,.81,-2.97,.84,-3.12],["c",.03,-.54,-.3,-.72,-.84,-.45],["c",-.24,.12,-.57,.42,-.66,.63],["c",-.06,.09,-.51,1.44,-1.05,2.97],["c",-.51,1.56,-.99,2.85,-.99,2.91],["c",-.06,.12,-.21,.24,-.36,.3],["c",-.12,.06,-.21,.06,-.9,.06],["c",-.6,0,-.78,0,-.84,-.06],["c",-.09,-.03,-.18,-.18,-.18,-.27],["c",0,-.03,.45,-1.38,.99,-2.97],["c",1.05,-3.18,1.05,-3.18,.93,-3.45],["c",-.12,-.27,-.39,-.3,-.72,-.15],["c",-.54,.27,-1.14,1.17,-1.56,2.4],["c",-.06,.15,-.15,.3,-.18,.36],["c",-.21,.21,-.57,.27,-.72,.09],["c",-.09,-.09,-.06,-.21,.06,-.63],["c",.48,-1.26,1.26,-2.46,2.01,-3.21],["c",.57,-.54,1.2,-.87,1.83,-1.02],["z"]],w:14.687,h:9.126},p:{d:[["M",1.92,-8.7],["c",.27,-.09,.81,-.06,1.11,.03],["c",.54,.18,.93,.51,1.17,.99],["c",.09,.15,.15,.33,.18,.36],["l",0,.12],["l",.3,-.27],["c",.66,-.6,1.35,-1.02,2.13,-1.2],["c",.21,-.06,.33,-.06,.78,-.06],["c",.45,0,.51,0,.84,.09],["c",1.29,.33,2.07,1.32,2.25,2.79],["c",.09,.81,-.09,2.01,-.45,2.79],["c",-.54,1.26,-1.86,2.55,-3.18,3.03],["c",-.45,.18,-.81,.24,-1.29,.24],["c",-.69,-.03,-1.35,-.18,-1.86,-.45],["c",-.3,-.15,-.51,-.18,-.69,-.09],["c",-.09,.03,-.18,.09,-.18,.12],["c",-.09,.12,-1.05,2.94,-1.05,3.06],["c",0,.24,.18,.48,.51,.63],["c",.18,.06,.54,.15,.75,.15],["c",.21,0,.36,.06,.42,.18],["c",.12,.18,.06,.42,-.12,.54],["c",-.09,.03,-.15,.03,-.78,0],["c",-1.98,-.15,-3.81,-.15,-5.79,0],["c",-.63,.03,-.69,.03,-.78,0],["c",-.24,-.15,-.24,-.57,.03,-.66],["c",.06,-.03,.48,-.09,.99,-.12],["c",.87,-.06,1.11,-.09,1.35,-.21],["c",.18,-.06,.33,-.18,.39,-.3],["c",.06,-.12,3.24,-9.42,3.27,-9.6],["c",.06,-.33,.03,-.57,-.15,-.69],["c",-.09,-.06,-.12,-.06,-.3,-.06],["c",-.69,.06,-1.53,1.02,-2.28,2.61],["c",-.09,.21,-.21,.45,-.27,.51],["c",-.09,.12,-.33,.24,-.48,.24],["c",-.18,0,-.36,-.15,-.36,-.3],["c",0,-.24,.78,-1.83,1.26,-2.55],["c",.72,-1.11,1.47,-1.74,2.28,-1.92],["z"],["m",5.37,1.47],["c",-.27,-.12,-.75,-.03,-1.14,.21],["c",-.75,.48,-1.47,1.68,-1.89,3.15],["c",-.45,1.47,-.42,2.34,0,2.7],["c",.45,.39,1.26,.21,1.83,-.36],["c",.51,-.51,.99,-1.68,1.38,-3.27],["c",.3,-1.17,.33,-1.74,.15,-2.13],["c",-.09,-.15,-.15,-.21,-.33,-.3],["z"]],w:14.689,h:13.127},r:{d:[["M",6.33,-9.12],["c",.27,-.03,.93,0,1.2,.06],["c",.84,.21,1.23,.81,1.02,1.53],["c",-.24,.75,-.9,1.17,-1.56,.96],["c",-.33,-.09,-.51,-.3,-.66,-.75],["c",-.03,-.12,-.09,-.24,-.12,-.3],["c",-.09,-.15,-.3,-.24,-.48,-.24],["c",-.57,0,-1.38,.54,-1.65,1.08],["c",-.06,.15,-.33,1.17,-.9,3.27],["c",-.57,2.31,-.81,3.12,-.87,3.21],["c",-.03,.06,-.12,.15,-.18,.21],["l",-.12,.06],["l",-.81,.03],["c",-.69,0,-.81,0,-.9,-.03],["c",-.09,-.06,-.18,-.21,-.18,-.3],["c",0,-.06,.39,-1.62,.9,-3.51],["c",.84,-3.24,.87,-3.45,.87,-3.72],["c",0,-.21,0,-.27,-.03,-.36],["c",-.12,-.15,-.21,-.24,-.42,-.24],["c",-.24,0,-.45,.15,-.78,.42],["c",-.33,.36,-.45,.54,-.72,1.14],["c",-.03,.12,-.21,.24,-.36,.27],["c",-.12,0,-.15,0,-.24,-.06],["c",-.18,-.12,-.18,-.21,-.06,-.54],["c",.21,-.57,.42,-.93,.78,-1.32],["c",.54,-.51,1.2,-.81,1.95,-.87],["c",.81,-.03,1.53,.3,1.92,.87],["l",.12,.18],["l",.09,-.09],["c",.57,-.45,1.41,-.84,2.19,-.96],["z"]],w:9.41,h:9.132},s:{d:[["M",4.47,-8.73],["c",.09,0,.36,-.03,.57,-.03],["c",.75,.03,1.29,.24,1.71,.63],["c",.51,.54,.66,1.26,.36,1.83],["c",-.24,.42,-.63,.57,-1.11,.42],["c",-.33,-.09,-.6,-.36,-.6,-.57],["c",0,-.03,.06,-.21,.15,-.39],["c",.12,-.21,.15,-.33,.18,-.48],["c",0,-.24,-.06,-.48,-.15,-.6],["c",-.15,-.21,-.42,-.24,-.75,-.15],["c",-.27,.06,-.48,.18,-.69,.36],["c",-.39,.39,-.51,.96,-.33,1.38],["c",.09,.21,.42,.51,.78,.72],["c",1.11,.69,1.59,1.11,1.89,1.68],["c",.21,.39,.24,.78,.15,1.29],["c",-.18,1.2,-1.17,2.16,-2.52,2.52],["c",-1.02,.24,-1.95,.12,-2.7,-.42],["c",-.72,-.51,-.99,-1.47,-.6,-2.19],["c",.24,-.48,.72,-.63,1.17,-.42],["c",.33,.18,.54,.45,.57,.81],["c",0,.21,-.03,.3,-.33,.51],["c",-.33,.24,-.39,.42,-.27,.69],["c",.06,.15,.21,.27,.45,.33],["c",.3,.09,.87,.09,1.2,0],["c",.75,-.21,1.23,-.72,1.29,-1.35],["c",.03,-.42,-.15,-.81,-.54,-1.2],["c",-.24,-.24,-.48,-.42,-1.41,-1.02],["c",-.69,-.42,-1.05,-.93,-1.05,-1.47],["c",0,-.39,.12,-.87,.3,-1.23],["c",.27,-.57,.78,-1.05,1.38,-1.35],["c",.24,-.12,.63,-.27,.9,-.3],["z"]],w:6.632,h:8.758},z:{d:[["M",2.64,-7.95],["c",.36,-.09,.81,-.03,1.71,.27],["c",.78,.21,.96,.27,1.74,.3],["c",.87,.06,1.02,.03,1.38,-.21],["c",.21,-.15,.33,-.15,.48,-.06],["c",.15,.09,.21,.3,.15,.45],["c",-.03,.06,-1.26,1.26,-2.76,2.67],["l",-2.73,2.55],["l",.54,.03],["c",.54,.03,.72,.03,2.01,.15],["c",.36,.03,.9,.06,1.2,.09],["c",.66,0,.81,-.03,1.02,-.24],["c",.3,-.3,.39,-.72,.27,-1.23],["c",-.06,-.27,-.06,-.27,-.03,-.39],["c",.15,-.3,.54,-.27,.69,.03],["c",.15,.33,.27,1.02,.27,1.5],["c",0,1.47,-1.11,2.7,-2.52,2.79],["c",-.57,.03,-1.02,-.09,-2.01,-.51],["c",-1.02,-.42,-1.23,-.48,-2.13,-.54],["c",-.81,-.06,-.96,-.03,-1.26,.18],["c",-.12,.06,-.24,.12,-.27,.12],["c",-.27,0,-.45,-.3,-.36,-.51],["c",.03,-.06,1.32,-1.32,2.91,-2.79],["l",2.88,-2.73],["c",-.03,0,-.21,.03,-.42,.06],["c",-.21,.03,-.78,.09,-1.23,.12],["c",-1.11,.12,-1.23,.15,-1.95,.27],["c",-.72,.15,-1.17,.18,-1.29,.09],["c",-.27,-.18,-.21,-.75,.12,-1.26],["c",.39,-.6,.93,-1.02,1.59,-1.2],["z"]],w:8.573,h:8.743},"+":{d:[["M",3.48,-9.3],["c",.18,-.09,.36,-.09,.54,0],["c",.18,.09,.24,.15,.33,.3],["l",.06,.15],["l",0,1.29],["l",0,1.29],["l",1.29,0],["c",1.23,0,1.29,0,1.41,.06],["c",.06,.03,.15,.09,.18,.12],["c",.12,.09,.21,.33,.21,.48],["c",0,.15,-.09,.39,-.21,.48],["c",-.03,.03,-.12,.09,-.18,.12],["c",-.12,.06,-.18,.06,-1.41,.06],["l",-1.29,0],["l",0,1.29],["c",0,1.23,0,1.29,-.06,1.41],["c",-.09,.18,-.15,.24,-.3,.33],["c",-.21,.09,-.39,.09,-.57,0],["c",-.18,-.09,-.24,-.15,-.33,-.33],["c",-.06,-.12,-.06,-.18,-.06,-1.41],["l",0,-1.29],["l",-1.29,0],["c",-1.23,0,-1.29,0,-1.41,-.06],["c",-.18,-.09,-.24,-.15,-.33,-.33],["c",-.09,-.18,-.09,-.36,0,-.54],["c",.09,-.18,.15,-.24,.33,-.33],["l",.15,-.06],["l",1.26,0],["l",1.29,0],["l",0,-1.29],["c",0,-1.23,0,-1.29,.06,-1.41],["c",.09,-.18,.15,-.24,.33,-.33],["z"]],w:7.507,h:7.515},",":{d:[["M",1.32,-3.36],["c",.57,-.15,1.17,.03,1.59,.45],["c",.45,.45,.6,.96,.51,1.89],["c",-.09,1.23,-.42,2.46,-.99,3.93],["c",-.3,.72,-.72,1.62,-.78,1.68],["c",-.18,.21,-.51,.18,-.66,-.06],["c",-.03,-.06,-.06,-.15,-.06,-.18],["c",0,-.06,.12,-.33,.24,-.63],["c",.84,-1.8,1.02,-2.61,.69,-3.24],["c",-.12,-.24,-.27,-.36,-.75,-.6],["c",-.36,-.15,-.42,-.21,-.6,-.39],["c",-.69,-.69,-.69,-1.71,0,-2.4],["c",.21,-.21,.51,-.39,.81,-.45],["z"]],w:3.452,h:8.143},"-":{d:[["M",.18,-5.34],["c",.09,-.06,.15,-.06,2.31,-.06],["c",2.46,0,2.37,0,2.46,.21],["c",.12,.21,.03,.42,-.15,.54],["c",-.09,.06,-.15,.06,-2.28,.06],["c",-2.16,0,-2.22,0,-2.31,-.06],["c",-.27,-.15,-.27,-.54,-.03,-.69],["z"]],w:5.001,h:.81},".":{d:[["M",1.32,-3.36],["c",1.05,-.27,2.1,.57,2.1,1.65],["c",0,1.08,-1.05,1.92,-2.1,1.65],["c",-.9,-.21,-1.5,-1.14,-1.26,-2.04],["c",.12,-.63,.63,-1.11,1.26,-1.26],["z"]],w:3.413,h:3.402},"scripts.wedge":{d:[["M",-3.66,-7.44],["c",.06,-.09,0,-.09,.81,.03],["c",1.86,.3,3.84,.3,5.73,0],["c",.78,-.12,.72,-.12,.78,-.03],["c",.15,.15,.12,.24,-.24,.6],["c",-.93,.93,-1.98,2.76,-2.67,4.62],["c",-.3,.78,-.51,1.71,-.51,2.13],["c",0,.15,0,.18,-.06,.27],["c",-.12,.09,-.24,.09,-.36,0],["c",-.06,-.09,-.06,-.12,-.06,-.27],["c",0,-.42,-.21,-1.35,-.51,-2.13],["c",-.69,-1.86,-1.74,-3.69,-2.67,-4.62],["c",-.36,-.36,-.39,-.45,-.24,-.6],["z"]],w:7.49,h:7.752},"scripts.thumb":{d:[["M",-.54,-3.69],["c",.15,-.03,.36,-.06,.51,-.06],["c",1.44,0,2.58,1.11,2.94,2.85],["c",.09,.48,.09,1.32,0,1.8],["c",-.27,1.41,-1.08,2.43,-2.16,2.73],["l",-.18,.06],["l",0,.12],["c",.03,.06,.06,.45,.09,.87],["c",.03,.57,.03,.78,0,.84],["c",-.09,.27,-.39,.48,-.66,.48],["c",-.27,0,-.57,-.21,-.66,-.48],["c",-.03,-.06,-.03,-.27,0,-.84],["c",.03,-.42,.06,-.81,.09,-.87],["l",0,-.12],["l",-.18,-.06],["c",-1.08,-.3,-1.89,-1.32,-2.16,-2.73],["c",-.09,-.48,-.09,-1.32,0,-1.8],["c",.15,-.84,.51,-1.53,1.02,-2.04],["c",.39,-.39,.84,-.63,1.35,-.75],["z"],["m",1.05,.9],["c",-.15,-.09,-.21,-.09,-.45,-.12],["c",-.15,0,-.3,.03,-.39,.03],["c",-.57,.18,-.9,.72,-1.08,1.74],["c",-.06,.48,-.06,1.8,0,2.28],["c",.15,.9,.42,1.44,.9,1.65],["c",.18,.09,.21,.09,.51,.09],["c",.3,0,.33,0,.51,-.09],["c",.48,-.21,.75,-.75,.9,-1.65],["c",.03,-.27,.03,-.54,.03,-1.14],["c",0,-.6,0,-.87,-.03,-1.14],["c",-.15,-.9,-.45,-1.44,-.9,-1.65],["z"]],w:5.955,h:9.75},"scripts.open":{d:[["M",-.54,-3.69],["c",.15,-.03,.36,-.06,.51,-.06],["c",1.44,0,2.58,1.11,2.94,2.85],["c",.09,.48,.09,1.32,0,1.8],["c",-.33,1.74,-1.47,2.85,-2.91,2.85],["c",-1.44,0,-2.58,-1.11,-2.91,-2.85],["c",-.09,-.48,-.09,-1.32,0,-1.8],["c",.15,-.84,.51,-1.53,1.02,-2.04],["c",.39,-.39,.84,-.63,1.35,-.75],["z"],["m",1.11,.9],["c",-.21,-.09,-.27,-.09,-.51,-.12],["c",-.3,0,-.42,.03,-.66,.15],["c",-.24,.12,-.51,.39,-.66,.63],["c",-.54,.93,-.63,2.64,-.21,3.81],["c",.21,.54,.51,.9,.93,1.11],["c",.21,.09,.24,.09,.54,.09],["c",.3,0,.33,0,.54,-.09],["c",.42,-.21,.72,-.57,.93,-1.11],["c",.36,-.99,.36,-2.37,0,-3.36],["c",-.21,-.54,-.51,-.9,-.9,-1.11],["z"]],w:5.955,h:7.5},"scripts.longphrase":{d:[["M",1.47,-15.09],["c",.36,-.09,.66,-.18,.69,-.18],["c",.06,0,.06,.54,.06,11.25],["l",0,11.25],["l",-.63,.15],["c",-.66,.18,-1.44,.39,-1.5,.39],["c",-.03,0,-.03,-3.39,-.03,-11.25],["l",0,-11.25],["l",.36,-.09],["c",.21,-.06,.66,-.18,1.05,-.27],["z"]],w:2.16,h:23.04},"scripts.mediumphrase":{d:[["M",1.47,-7.59],["c",.36,-.09,.66,-.18,.69,-.18],["c",.06,0,.06,.39,.06,7.5],["l",0,7.5],["l",-.63,.15],["c",-.66,.18,-1.44,.39,-1.5,.39],["c",-.03,0,-.03,-2.28,-.03,-7.5],["l",0,-7.5],["l",.36,-.09],["c",.21,-.06,.66,-.18,1.05,-.27],["z"]],w:2.16,h:15.54},"scripts.shortphrase":{d:[["M",1.47,-7.59],["c",.36,-.09,.66,-.18,.69,-.18],["c",.06,0,.06,.21,.06,3.75],["l",0,3.75],["l",-.42,.09],["c",-.57,.18,-1.65,.45,-1.71,.45],["c",-.03,0,-.03,-.72,-.03,-3.75],["l",0,-3.75],["l",.36,-.09],["c",.21,-.06,.66,-.18,1.05,-.27],["z"]],w:2.16,h:8.04},"scripts.snap":{d:[["M",4.5,-3.39],["c",.36,-.03,.96,-.03,1.35,0],["c",1.56,.15,3.15,.9,4.2,2.01],["c",.24,.27,.33,.42,.33,.6],["c",0,.27,.03,.24,-2.46,2.22],["c",-1.29,1.02,-2.4,1.86,-2.49,1.92],["c",-.18,.09,-.3,.09,-.48,0],["c",-.09,-.06,-1.2,-.9,-2.49,-1.92],["c",-2.49,-1.98,-2.46,-1.95,-2.46,-2.22],["c",0,-.18,.09,-.33,.33,-.6],["c",1.05,-1.08,2.64,-1.86,4.17,-2.01],["z"],["m",1.29,1.17],["c",-1.47,-.15,-2.97,.3,-4.14,1.2],["l",-.18,.15],["l",.06,.09],["c",.15,.12,3.63,2.85,3.66,2.85],["c",.03,0,3.51,-2.73,3.66,-2.85],["l",.06,-.09],["l",-.18,-.15],["c",-.84,-.66,-1.89,-1.08,-2.94,-1.2],["z"]],w:10.38,h:6.84},"noteheads.slash.whole":{d:[["M",5,-5],["l",1,1],["l",-5,5],["l",-1,-1],["z"],["m",4,6],["l",-5,-5],["l",2,-2],["l",5,5],["z"],["m",0,-2],["l",1,1],["l",-5,5],["l",-1,-1],["z"],["m",-4,6],["l",-5,-5],["l",2,-2],["l",5,5],["z"]],w:10.81,h:15.63},"noteheads.slash.quarter":{d:[["M",9,-6],["l",0,4],["l",-9,9],["l",0,-4],["z"]],w:9,h:9},"noteheads.harmonic.quarter":{d:[["M",3.63,-4.02],["c",.09,-.06,.18,-.09,.24,-.03],["c",.03,.03,.87,.93,1.83,2.01],["c",1.5,1.65,1.8,1.98,1.8,2.04],["c",0,.06,-.3,.39,-1.8,2.04],["c",-.96,1.08,-1.8,1.98,-1.83,2.01],["c",-.06,.06,-.15,.03,-.24,-.03],["c",-.12,-.09,-3.54,-3.84,-3.6,-3.93],["c",-.03,-.03,-.03,-.09,-.03,-.15],["c",.03,-.06,3.45,-3.84,3.63,-3.96],["z"]],w:7.5,h:8.165}};this.printSymbol=function(t,i,r,n,a){if(!e[r])return null;var s=this.pathClone(e[r].d);s[0][1]+=t,s[0][2]+=i;for(var o="",c=0;c<s.length;c++)o+=s[c].join(" ");return n.path({path:o,stroke:"none",fill:"#000000",class:a})},this.getPathForSymbol=function(t,i,r,n,a){if(n=n||1,a=a||1,!e[r])return null;var s=this.pathClone(e[r].d);return 1===n&&1===a||this.pathScale(s,n,a),s[0][1]+=t,s[0][2]+=i,s},this.getSymbolWidth=function(t){return e[t]?e[t].w:0},this.getSymbolHeight=function(t){return e[t]?e[t].h:0},this.symbolHeightInPitches=function(e){return this.getSymbolHeight(e)/r.STEP},this.getSymbolAlign=function(e){return"scripts"===e.substring(0,7)&&"scripts.roll"!==e?"center":"left"},this.pathClone=function(e){for(var t=[],i=0,r=e.length;i<r;i++){t[i]=[];for(var n=0,a=e[i].length;n<a;n++)t[i][n]=e[i][n]}return t},this.pathScale=function(e,t,i){for(var r=0,n=e.length;r<n;r++){var a,s,o=e[r];for(a=1,s=o.length;a<s;a++)o[a]*=a%2?t:i}},this.getYCorr=function(e){switch(e){case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":case"+":return-2;case"timesig.common":case"timesig.cut":return 0;case"flags.d32nd":return-1;case"flags.d64th":return-2;case"flags.u32nd":return 1;case"flags.u64th":return 3;case"rests.whole":return 1;case"rests.half":case"rests.8th":case"rests.quarter":case"rests.16th":case"rests.32nd":case"rests.64th":return-1;case"f":case"m":case"p":case"s":case"z":return-4;case"scripts.trill":case"scripts.upbow":case"scripts.downbow":return-2;case"scripts.ufermata":case"scripts.wedge":case"scripts.roll":case"scripts.shortphrase":case"scripts.longphrase":return-1;case"scripts.dfermata":return 1;default:return 0}}}},function(e,t,i){var r=i(1),n=function(e,t,i,r,n,a){a||(a={}),this.tuneNumber=n,this.abcelem=e,this.duration=t,this.durationClass=a.durationClassOveride?a.durationClassOveride:this.duration,this.minspacing=i||0,this.x=0,this.children=[],this.heads=[],this.extra=[],this.extraw=0,this.w=0,this.right=[],this.invisible=!1,this.bottom=void 0,this.top=void 0,this.type=r,this.specialY={tempoHeightAbove:0,partHeightAbove:0,volumeHeightAbove:0,dynamicHeightAbove:0,endingHeightAbove:0,chordHeightAbove:0,lyricHeightAbove:0,lyricHeightBelow:0,chordHeightBelow:0,volumeHeightBelow:0,dynamicHeightBelow:0}};n.prototype.setUpperAndLowerElements=function(e){for(var t=0;t<this.children.length;t++){var i=this.children[t];for(var r in this.specialY)this.specialY.hasOwnProperty(r)&&i[r]&&(i.pitch=e[r],void 0===i.top&&(i.setUpperAndLowerElements(e),this.pushTop(i.top),this.pushBottom(i.bottom)))}},n.prototype.getMinWidth=function(){return this.w},n.prototype.getExtraWidth=function(){return-this.extraw},n.prototype.addExtra=function(e){e.dx<this.extraw&&(this.extraw=e.dx),this.extra[this.extra.length]=e,this.addChild(e)},n.prototype.addHead=function(e){e.dx<this.extraw&&(this.extraw=e.dx),this.heads[this.heads.length]=e,this.addRight(e)},n.prototype.addRight=function(e){e.dx+e.w>this.w&&(this.w=e.dx+e.w),this.right[this.right.length]=e,this.addChild(e)},n.prototype.addCentered=function(e){var t=e.w/2;-t<this.extraw&&(this.extraw=-t),this.extra[this.extra.length]=e,e.dx+t>this.w&&(this.w=e.dx+t),this.right[this.right.length]=e,this.addChild(e)},n.prototype.setLimit=function(e,t){t[e]&&(this.specialY[e]?this.specialY[e]=Math.max(this.specialY[e],t[e]):this.specialY[e]=t[e])},n.prototype.addChild=function(e){e.parent=this,this.children[this.children.length]=e,this.pushTop(e.top),this.pushBottom(e.bottom),this.setLimit("tempoHeightAbove",e),this.setLimit("partHeightAbove",e),this.setLimit("volumeHeightAbove",e),this.setLimit("dynamicHeightAbove",e),this.setLimit("endingHeightAbove",e),this.setLimit("chordHeightAbove",e),this.setLimit("lyricHeightAbove",e),this.setLimit("lyricHeightBelow",e),this.setLimit("chordHeightBelow",e),this.setLimit("volumeHeightBelow",e),this.setLimit("dynamicHeightBelow",e)},n.prototype.pushTop=function(e){void 0!==e&&(void 0===this.top?this.top=e:this.top=Math.max(e,this.top))},n.prototype.pushBottom=function(e){void 0!==e&&(void 0===this.bottom?this.bottom=e:this.bottom=Math.min(e,this.bottom))},n.prototype.setX=function(e){this.x=e;for(var t=0;t<this.children.length;t++)this.children[t].setX(e)},n.prototype.setHint=function(){this.hint=!0},n.prototype.draw=function(e,t){if(!this.invisible){this.elemset=[],e.beginGroup();for(var i=0;i<this.children.length;i++){0;var n=this.children[i].draw(e,t);n&&this.elemset.push(n)}var a=this.type;if(("note"===this.type||"rest"===this.type)&&(a=(a+=" d"+this.durationClass).replace(/\./g,"-"),this.abcelem.pitches))for(var s=0;s<this.abcelem.pitches.length;s++)a+=" p"+this.abcelem.pitches[s].pitch;var o=e.endGroup(a);o&&this.elemset.push(o),this.klass&&this.setClass("mark","","#00ff00"),this.hint&&this.setClass("abcjs-hint","",null);var c=e.printShadedBox(this.x,e.calcY(this.top),this.w,e.calcY(this.bottom)-e.calcY(this.top),"#000000",0),l=this,h=e.controller;c.addEventListener("mouseup",(function(){var e=[];if(l.elemset)for(var t=0;t<l.elemset.length;t++){var i=l.elemset[t];i&&e.push(i.getAttribute("class"))}h.notifySelect(l,l.tuneNumber,e)})),this.abcelem.abselem=this;r.STEP}},n.prototype.isIE=!1,n.prototype.setClass=function(e,t,i){for(var r=0;r<this.elemset.length;r++){var n=this.elemset[r];n.setAttribute("fill",i);var a=n.getAttribute("class");a||(a=""),a=(a=a.replace(t,"")).replace(e,""),e.length>0&&(a.length>0&&" "!==a.charAt(a.length-1)&&(a+=" "),a+=e),n.setAttribute("class",a)}},n.prototype.highlight=function(e,t){void 0===e&&(e="abcjs-note_selected"),void 0===t&&(t="#ff0000"),this.setClass(e,"",t)},n.prototype.unhighlight=function(e,t){void 0===e&&(e="abcjs-note_selected"),void 0===t&&(t="#000000"),this.setClass("",e,t)},e.exports=n},function(e,t){function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}e.exports=function(){for(var e,t,r,n,a,s=0,o=arguments[s++],c=[];o;){if(t=/^[^\x25]+/.exec(o))c.push(t[0]);else if(t=/^\x25{2}/.exec(o))c.push("%");else{if(!(t=/^\x25(?:(\d+)\$)?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-fosuxX])/.exec(o)))throw"Huh ?!";if(null==(e=arguments[t[1]||s++])||null==e)throw"Too few arguments.";if(/[^s]/.test(t[7])&&"number"!=typeof e)throw"Expecting number but found "+i(e);switch(t[7]){case"b":e=e.toString(2);break;case"c":e=String.fromCharCode(e);break;case"d":e=parseInt(e);break;case"e":e=t[6]?e.toExponential(t[6]):e.toExponential();break;case"f":e=t[6]?parseFloat(e).toFixed(t[6]):parseFloat(e);break;case"o":e=e.toString(8);break;case"s":e=(e=String(e))&&t[6]?e.substring(0,t[6]):e;break;case"u":e=Math.abs(e);break;case"x":e=e.toString(16);break;case"X":e=e.toString(16).toUpperCase()}e=/[def]/.test(t[7])&&t[2]&&e>0?"+"+e:e,n=t[3]?"0"==t[3]?"0":t[3].charAt(1):" ",a=t[5]-String(e).length,r=t[5]?str_repeat(n,a):"",c.push(t[4]?e+r:r+e)}o=o.substring(t[0].length)}return c.join("")}},function(e,t){e.exports=function(){return window.abcjsAudioContext}},function(e,t,i){var r=i(6);e.exports=function(){var e=r();return e?void 0!==e.resume:!!window.Promise&&(window.AudioContext||window.webkitAudioContext||navigator.mozAudioContext||navigator.msAudioContext)}},function(e,t){function i(e,t){for(var i=[],r=null,n=0;n<e.length;n++){var a=e[n];a.top!==r&&(i.push({milliseconds:a.milliseconds-t,top:a.top,bottom:a.top+a.height}),r=a.top)}return i}e.exports=function(e,t){var r=this;if(t||(t={}),r.qpm=t.qpm,!r.qpm){var n=e.metaText?e.metaText.tempo:null;r.qpm=e.getBpm(n)}r.extraMeasuresAtBeginning=t.extraMeasuresAtBeginning?t.extraMeasuresAtBeginning:0,r.beatCallback=t.beatCallback,r.eventCallback=t.eventCallback,r.lineEndCallback=t.lineEndCallback,r.lineEndAnticipation=t.lineEndAnticipation?t.lineEndAnticipation:0,r.beatSubdivisions=t.beatSubdivisions?parseInt(t.beatSubdivisions,10):1,r.replaceTarget=function(e){e.setTiming(r.qpm,r.extraMeasuresAtBeginning),0!==e.noteTimings.length&&(r.lineEndCallback&&(r.lineEndTimings=i(e.noteTimings,r.lineEndAnticipation)),r.noteTimings=e.noteTimings)},r.replaceTarget(e),0!==r.noteTimings.length&&(r.noteTimings=e.noteTimings,r.millisecondsPerBeat=1e3/(r.qpm/60)/r.beatSubdivisions,r.lastMoment=r.noteTimings[r.noteTimings.length-1].milliseconds,r.totalBeats=Math.round(r.lastMoment/r.millisecondsPerBeat),r.startTime=null,r.currentBeat=0,r.currentEvent=0,r.isPaused=!1,r.isRunning=!1,r.pausedTime=null,r.justUnpaused=!1,r.newSeekPercent=0,r.justSeeked=!1,r.doTiming=function(e){if(r.startTime){if(r.justUnpaused){var t=e-r.pausedTime;r.startTime+=t}}else r.startTime=e;if(r.justUnpaused=!1,r.justSeeked&&(!function(e){var t=(e-r.startTime)/r.lastMoment-r.newSeekPercent,i=r.lastMoment*t;r.startTime=r.startTime+i;var n=e-r.startTime;n+=50;var a=r.currentBeat;for(r.currentBeat=Math.floor(n/r.millisecondsPerBeat),r.beatCallback&&a!==r.currentBeat&&r.beatCallback(r.currentBeat/r.beatSubdivisions,r.totalBeats/r.beatSubdivisions,r.lastMoment),r.currentEvent=0;r.noteTimings.length>r.currentEvent&&r.noteTimings[r.currentEvent].milliseconds<n;)r.currentEvent++;r.eventCallback&&r.currentEvent>0&&"event"===r.noteTimings[r.currentEvent-1].type&&r.eventCallback(r.noteTimings[r.currentEvent-1])}(e),r.justSeeked=!1),r.isPaused)r.pausedTime=e;else if(r.isRunning){var i=e-r.startTime;for(i+=50;r.noteTimings.length>r.currentEvent&&r.noteTimings[r.currentEvent].milliseconds<i;)r.eventCallback&&"event"===r.noteTimings[r.currentEvent].type&&r.eventCallback(r.noteTimings[r.currentEvent]),r.currentEvent++;i<r.lastMoment?(requestAnimationFrame(r.doTiming),r.currentBeat*r.millisecondsPerBeat<i&&(r.beatCallback&&r.beatCallback(r.currentBeat/r.beatSubdivisions,r.totalBeats/r.beatSubdivisions,r.lastMoment),r.currentBeat++)):r.currentBeat<=r.totalBeats&&r.beatCallback&&(r.beatCallback(r.currentBeat/r.beatSubdivisions,r.totalBeats/r.beatSubdivisions,r.lastMoment),r.currentBeat++,requestAnimationFrame(r.doTiming)),r.lineEndCallback&&r.lineEndTimings.length&&r.lineEndTimings[0].milliseconds<=i&&(r.lineEndCallback(r.lineEndTimings[0]),r.lineEndTimings.shift()),i>=r.lastMoment&&r.eventCallback&&r.eventCallback(null)}},r.start=function(){r.isRunning=!0,r.isPaused&&(r.isPaused=!1,r.justUnpaused=!0),requestAnimationFrame(r.doTiming)},r.pause=function(){r.isPaused=!0,r.isRunning=!1},r.reset=function(){r.currentBeat=0,r.currentEvent=0,r.startTime=null,r.pausedTime=null,r.lineEndCallback&&(r.lineEndTimings=i(r.noteTimings,r.lineEndAnticipation))},r.stop=function(){r.pause(),r.reset()},r.setProgress=function(e){e<0&&(e=0),e>1&&(e=1),r.newSeekPercent=e,r.justSeeked=!0,requestAnimationFrame(r.doTiming)})}},function(e,t,i){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var n=i(0),a=i(10),s={};!function(){"use strict";s.numberOfTunes=function(e){var t=e.split("\nX:").length;return 0===t&&(t=1),t};var e=s.TuneBook=function(e){for(var t=this,i="",r=(e=n.strip(e)).split("\nX:"),a=1;a<r.length;a++)r[a]="X:"+r[a];var s=0;if(t.tunes=[],n.each(r,(function(e){t.tunes.push({abc:e,startPos:s}),s+=e.length+1})),t.tunes.length>1&&!n.startsWith(t.tunes[0].abc,"X:")){var o=t.tunes.shift().abc.split("\n");n.each(o,(function(e){n.startsWith(e,"%%")&&(i+=e+"\n")}))}t.header=i,n.each(t.tunes,(function(e){var t=e.abc.indexOf("\n\n");t>0&&(e.abc=e.abc.substring(0,t)),e.pure=e.abc,e.abc=i+e.abc;var r=e.pure.split("T:");r.length>1?(r=r[1].split("\n"),e.title=r[0].replace(/^\s+|\s+$/g,"")):e.title="";var n=e.pure.substring(2,e.pure.indexOf("\n"));e.id=n.replace(/^\s+|\s+$/g,"")}))};e.prototype.getTuneById=function(e){for(var t=0;t<this.tunes.length;t++)if(this.tunes[t].id===e)return this.tunes[t];return null},e.prototype.getTuneByTitle=function(e){for(var t=0;t<this.tunes.length;t++)if(this.tunes[t].title===e)return this.tunes[t];return null},s.parseOnly=function(e,t){for(var i=s.numberOfTunes(e),r=[],n=0;n<i;n++)r.push(1);return s.renderEngine((function(){}),r,e,t)},s.renderEngine=function(t,i,n,s){var o=[];if(void 0!==i&&void 0!==n){var c;(!(c=i)||c.propertyIsEnumerable("length")||"object"!==r(c)||"number"!=typeof c.length)&&(i=[i]),void 0===s&&(s={});for(var l=s.startingTune?parseInt(s.startingTune,10):0,h=new e(n),u=new a,d=0;d<i.length;d++){var p=i[d];if("string"==typeof p&&(p=document.getElementById(p)),p)if(l>=0&&l<h.tunes.length){u.parse(h.tunes[l].abc,s,h.tunes[l].startPos-h.header.length);var f=u.getTune(),m=t(p,f,d,h.tunes[l].abc);o.push(m||f)}else p.hasOwnProperty("innerHTML")&&(p.innerHTML="");l++}return o}},s.extractMeasures=function(t){for(var i=[],r=new e(t),n=0;n<r.tunes.length;n++){for(var a=r.tunes[n],o=a.abc.split("K:"),c=o[1].split("\n"),l=o[0]+"K:"+c[0]+"\n",h=null,u=null,d=null,p=[],f=!1,m=s.parseOnly(a.abc)[0],g=m.getPickupLength()>0,v=0;v<m.lines.length;v++){var b=m.lines[v];if(b.staff)for(var y=0;y<1;y++)for(var w=b.staff[y],k=0;k<1;k++)for(var x=w.voices[k],S=0;S<x.length;S++){var _=x[S];if(null===d&&_.startChar>=0&&(d=_.startChar,u=void 0===_.chord?h:null),_.chord&&(h=_),"bar"===_.el_type){if(f){var T={abc:a.abc.substring(d,_.endChar)};(h=u&&u.chord&&u.chord.length>0?u.chord[0].name:null)&&(T.lastChord=h),_.startEnding&&(T.startEnding=_.startEnding),_.endEnding&&(T.endEnding=_.endEnding),p.push(T),d=null,f=!1}}else"note"===_.el_type&&(f=!0)}}i.push({header:l,measures:p,hasPickup:g})}return i}}(),e.exports=s},function(e,t,i){var r=i(0),n=i(11),a=i(31),s=i(12),o=i(32),c=i(16),l=i(17),h=i(18);e.exports=function(){"use strict";var e=new h,t=new o;function i(e,t,i){e.positioning||(e.positioning={}),e.positioning[t]=i}function u(e,t,i){e.fonts||(e.fonts={}),e.fonts[t]=i}this.getTune=function(){return{formatting:e.formatting,lines:e.lines,media:e.media,metaText:e.metaText,version:e.version,addElementToEvents:e.addElementToEvents,addUsefulCallbackInfo:e.addUsefulCallbackInfo,getBarLength:e.getBarLength,getBeatLength:e.getBeatLength,getBeatsPerMeasure:e.getBeatsPerMeasure,getBpm:e.getBpm,getMeter:e.getMeter,getMeterFraction:e.getMeterFraction,getPickupLength:e.getPickupLength,getKeySignature:e.getKeySignature,makeVoicesArray:e.makeVoicesArray,millisecondsPerMeasure:e.millisecondsPerMeasure,setupEvents:e.setupEvents,setTiming:e.setTiming}};var d={reset:function(){for(var e in this)this.hasOwnProperty(e)&&"function"!=typeof this[e]&&delete this[e];this.iChar=0,this.key={accidentals:[],root:"none",acc:"",mode:""},this.meter=null,this.origMeter=null,this.hasMainTitle=!1,this.default_length=.125,this.clef={type:"treble",verticalPos:0},this.next_note_duration=0,this.start_new_line=!0,this.is_in_header=!0,this.is_in_history=!1,this.partForNextLine={},this.havent_set_length=!0,this.voices={},this.staves=[],this.macros={},this.currBarNumber=1,this.barCounter={},this.inTextBlock=!1,this.inPsBlock=!1,this.ignoredDecorations=[],this.textBlock="",this.score_is_present=!1,this.inEnding=!1,this.inTie=[!1],this.inTieChord={},this.vocalPosition="auto",this.dynamicPosition="auto",this.chordPosition="auto",this.ornamentPosition="auto",this.volumePosition="auto",this.openSlurs=[],this.freegchord=!1},differentFont:function(e,t){return this[e].decoration!==t[e].decoration||(this[e].face!==t[e].face||(this[e].size!==t[e].size||(this[e].style!==t[e].style||this[e].weight!==t[e].weight)))},addFormattingOptions:function(e,t,r){"note"===r?("auto"!==this.vocalPosition&&i(e,"vocalPosition",this.vocalPosition),"auto"!==this.dynamicPosition&&i(e,"dynamicPosition",this.dynamicPosition),"auto"!==this.chordPosition&&i(e,"chordPosition",this.chordPosition),"auto"!==this.ornamentPosition&&i(e,"ornamentPosition",this.ornamentPosition),"auto"!==this.volumePosition&&i(e,"volumePosition",this.volumePosition),this.differentFont("annotationfont",t)&&u(e,"annotationfont",this.annotationfont),this.differentFont("gchordfont",t)&&u(e,"gchordfont",this.gchordfont),this.differentFont("vocalfont",t)&&u(e,"vocalfont",this.vocalfont),this.differentFont("tripletfont",t)&&u(e,"tripletfont",this.tripletfont)):"bar"===r&&("auto"!==this.dynamicPosition&&i(e,"dynamicPosition",this.dynamicPosition),"auto"!==this.chordPosition&&i(e,"chordPosition",this.chordPosition),"auto"!==this.ornamentPosition&&i(e,"ornamentPosition",this.ornamentPosition),"auto"!==this.volumePosition&&i(e,"volumePosition",this.volumePosition),this.differentFont("measurefont",t)&&u(e,"measurefont",this.measurefont),this.differentFont("repeatfont",t)&&u(e,"repeatfont",this.repeatfont))}},p=function(e){var t=r.gsub(e,""," ");return t=r.gsub(t,"&","&amp;"),t=r.gsub(t,"<","&lt;"),r.gsub(t,">","&gt;")},f=function(t,i,r){i||(i=" ");var n=i.charAt(r);" "===n&&(n="SPACE");var a,s=p(i.substring(0,r))+'<span style="text-decoration:underline;font-size:1.3em;font-weight:bold;">'+n+"</span>"+p(i.substring(r+1));!function(e){d.warnings||(d.warnings=[]),d.warnings.push(e)}("Music Line:"+e.getNumLines()+":"+(r+1)+": "+t+":  "+s),a={message:t,line:i,startChar:d.iChar+r,column:r},d.warningObjects||(d.warningObjects=[]),d.warningObjects.push(a)},m=new a(t,f,d,e);this.getWarnings=function(){return d.warnings},this.getWarningObjects=function(){return d.warningObjects};var g=function(e,i){if('"'===e.charAt(i)){var r=t.getBrackettedSubstring(e,i,5);if(r[2]||f("Missing the closing quote while parsing the chord symbol",e,i),r[0]>0&&r[1].length>0&&"^"===r[1].charAt(0))r[1]=r[1].substring(1),r[2]="above";else if(r[0]>0&&r[1].length>0&&"_"===r[1].charAt(0))r[1]=r[1].substring(1),r[2]="below";else if(r[0]>0&&r[1].length>0&&"<"===r[1].charAt(0))r[1]=r[1].substring(1),r[2]="left";else if(r[0]>0&&r[1].length>0&&">"===r[1].charAt(0))r[1]=r[1].substring(1),r[2]="right";else if(r[0]>0&&r[1].length>0&&"@"===r[1].charAt(0)){r[1]=r[1].substring(1);var n=t.getFloat(r[1]);0===n.digits&&f("Missing first position in absolutely positioned annotation.",e,i),r[1]=r[1].substring(n.digits),","!==r[1][0]&&f("Missing comma absolutely positioned annotation.",e,i),r[1]=r[1].substring(1);var a=t.getFloat(r[1]);0===a.digits&&f("Missing second position in absolutely positioned annotation.",e,i),r[1]=r[1].substring(a.digits);var s=t.skipWhiteSpace(r[1]);r[1]=r[1].substring(s),r[2]=null,r[3]={x:n.value,y:a.value}}else!0!==d.freegchord&&(r[1]=r[1].replace(/([ABCDEFG0-9])b/g,"$1♭"),r[1]=r[1].replace(/([ABCDEFG0-9])#/g,"$1♯")),r[2]="default",r[1]=c.chordName(d,r[1]);return r}return[0,""]},v=["trill","lowermordent","uppermordent","mordent","pralltriller","accent","fermata","invertedfermata","tenuto","0","1","2","3","4","5","+","wedge","open","thumb","snap","turn","roll","breath","shortphrase","mediumphrase","longphrase","segno","coda","D.S.","D.C.","fine","slide","^","marcato","upbow","downbow","/","//","///","////","trem1","trem2","trem3","trem4","turnx","invertedturn","invertedturnx","trill(","trill)","arpeggio","xstem","mark","umarcato","style=normal","style=harmonic","style=rhythm","style=x"],b=["p","pp","f","ff","mf","mp","ppp","pppp","fff","ffff","sfz"],y=["crescendo(","crescendo)","diminuendo(","diminuendo)"],w=[["<","accent"],[">","accent"],["tr","trill"],["plus","+"],["emphasis","accent"],["^","umarcato"],["marcato","umarcato"]],k=[["<(","crescendo("],["<)","crescendo)"],[">(","diminuendo("],[">)","diminuendo)"]],x=function(e,i){var n=d.macros[e.charAt(i)];if(void 0!==n)return"!"!==n.charAt(0)&&"+"!==n.charAt(0)||(n=n.substring(1)),"!"!==n.charAt(n.length-1)&&"+"!==n.charAt(n.length-1)||(n=n.substring(0,n.length-1)),r.detect(v,(function(e){return n===e}))?[1,n]:r.detect(b,(function(e){return n===e}))?("hidden"===d.volumePosition&&(n=""),[1,n]):r.detect(y,(function(e){return"hidden"===d.dynamicPosition&&(n=""),n===e}))?[1,n]:(r.detect(d.ignoredDecorations,(function(e){return n===e}))||f("Unknown macro: "+n,e,i),[1,""]);switch(e.charAt(i)){case".":return[1,"staccato"];case"u":return[1,"upbow"];case"v":return[1,"downbow"];case"~":return[1,"irishroll"];case"!":case"+":var a=t.getBrackettedSubstring(e,i,5);return a[1].length>0&&("^"===a[1].charAt(0)||"_"===a[1].charAt(0))&&(a[1]=a[1].substring(1)),r.detect(v,(function(e){return a[1]===e}))?a:r.detect(b,(function(e){return a[1]===e}))?("hidden"===d.volumePosition&&(a[1]=""),a):r.detect(y,(function(e){return a[1]===e}))?("hidden"===d.dynamicPosition&&(a[1]=""),a):r.detect(w,(function(e){return a[1]===e[0]&&(a[1]=e[1],!0)}))?a:r.detect(k,(function(e){return a[1]===e[0]&&(a[1]=e[1],!0)}))?("hidden"===d.dynamicPosition&&(a[1]=""),a):"!"!==e.charAt(i)||1!==a[0]&&"!"===e.charAt(i+a[0]-1)?(f("Unknown decoration: "+a[1],e,i),a[1]="",a):[1,null];case"H":return[1,"fermata"];case"J":return[1,"slide"];case"L":return[1,"accent"];case"M":return[1,"mordent"];case"O":return[1,"coda"];case"P":return[1,"pralltriller"];case"R":return[1,"roll"];case"S":return[1,"segno"];case"T":return[1,"trill"]}return[0,0]},S=function(e,i){for(var r=i;t.isWhiteSpace(e.charAt(i));)i++;return[i-r]},_=function(e,i){var r=t.getBarLine(e,i);if(0===r.len)return[0,""];if(r.warn)return f(r.warn,e,i),[r.len,""];for(var n=0;n<e.length&&" "===e.charAt(i+r.len+n);n++);var a=r.len;if("["===e.charAt(i+r.len+n)&&(r.len+=n+1),'"'===e.charAt(i+r.len)&&"["===e.charAt(i+r.len-1)){var s=t.getBrackettedSubstring(e,i+r.len,5);return[r.len+s[0],r.token,s[1]]}var o=t.getTokenOf(e.substring(i+r.len),"1234567890-,");return 0===o.len||"-"===o.token[0]?[a,r.token]:[r.len+o.len,r.token,o.token]},T={2:3,3:2,4:3,5:2,6:2,7:2,8:3,9:2},A=function(e,i){for(var r={},n=i;"("===e.charAt(i)||t.isWhiteSpace(e.charAt(i));)"("===e.charAt(i)&&(i+1<e.length&&e.charAt(i+1)>="2"&&e.charAt(i+1)<="9"?(void 0!==r.triplet?f("Can't nest triplets",e,i):(r.triplet=e.charAt(i+1)-"0",r.tripletQ=T[r.triplet],r.num_notes=r.triplet,i+2<e.length&&":"===e.charAt(i+2)&&(i+3<e.length&&":"===e.charAt(i+3)?i+4<e.length&&e.charAt(i+4)>="1"&&e.charAt(i+4)<="9"?(r.num_notes=e.charAt(i+4)-"0",i+=3):f("expected number after the two colons after the triplet to mark the duration",e,i):i+3<e.length&&e.charAt(i+3)>="1"&&e.charAt(i+3)<="9"?(r.tripletQ=e.charAt(i+3)-"0",i+4<e.length&&":"===e.charAt(i+4)?i+5<e.length&&e.charAt(i+5)>="1"&&e.charAt(i+5)<="9"&&(r.num_notes=e.charAt(i+5)-"0",i+=4):i+=2):f("expected number after the triplet to mark the duration",e,i))),i++):void 0===r.startSlur?r.startSlur=1:r.startSlur++),i++;return r.consumed=i-n,r},C=function(e,t){switch(e.charAt(t)){case">":return t<e.length-1&&">"===e.charAt(t+1)?[2,1.75,.25]:[1,1.5,.5];case"<":return t<e.length-1&&"<"===e.charAt(t+1)?[2,.25,1.75]:[1,.5,1.5]}return null},N=function(e){return void 0!==e.duration&&e.duration<.25&&(e.end_beam=!0),e},E={A:5,B:6,C:0,D:1,E:2,F:3,G:4,a:12,b:13,c:7,d:8,e:9,f:10,g:11},M={x:"invisible",y:"spacer",z:"rest",Z:"multimeasure"},B=function(i,r,n,a){for(var s=function(e){return"octave"===e||"duration"===e||"Zduration"===e||"broken_rhythm"===e||"end_slur"===e},o="startSlur",l=!1;;){switch(i.charAt(r)){case"(":if("startSlur"!==o)return s(o)?(n.endChar=r,n):null;void 0===n.startSlur?n.startSlur=1:n.startSlur++;break;case")":if(!s(o))return null;void 0===n.endSlur?n.endSlur=1:n.endSlur++;break;case"^":if("startSlur"===o)n.accidental="sharp",o="sharp2";else{if("sharp2"!==o)return s(o)?(n.endChar=r,n):null;n.accidental="dblsharp",o="pitch"}break;case"_":if("startSlur"===o)n.accidental="flat",o="flat2";else{if("flat2"!==o)return s(o)?(n.endChar=r,n):null;n.accidental="dblflat",o="pitch"}break;case"=":if("startSlur"!==o)return s(o)?(n.endChar=r,n):null;n.accidental="natural",o="pitch";break;case"A":case"B":case"C":case"D":case"E":case"F":case"G":case"a":case"b":case"c":case"d":case"e":case"f":case"g":if("startSlur"!==o&&"sharp2"!==o&&"flat2"!==o&&"pitch"!==o)return s(o)?(n.endChar=r,n):null;if(n.pitch=E[i.charAt(r)],c.note(d,n),o="octave",a&&0!==d.next_note_duration?(n.duration=d.default_length*d.next_note_duration,d.next_note_duration=0,l=!0):n.duration=d.default_length,d.clef&&"perc"===d.clef.type||d.currentVoice&&"perc"===d.currentVoice.clef){var h=i.charAt(r);if(n.accidental){h={dblflat:"__",flat:"_",natural:"=",sharp:"^",dblsharp:"^^"}[n.accidental]+h}e.formatting&&e.formatting.midi&&e.formatting.midi.drummap&&(n.midipitch=e.formatting.midi.drummap[h])}break;case",":if("octave"!==o)return s(o)?(n.endChar=r,n):null;n.pitch-=7;break;case"'":if("octave"!==o)return s(o)?(n.endChar=r,n):null;n.pitch+=7;break;case"x":case"y":case"z":case"Z":if("startSlur"!==o)return s(o)?(n.endChar=r,n):null;n.rest={type:M[i.charAt(r)]},delete n.accidental,delete n.startSlur,delete n.startTie,delete n.endSlur,delete n.endTie,delete n.end_beam,delete n.grace_notes,"multimeasure"===n.rest.type?(n.duration=1,o="Zduration"):(a&&0!==d.next_note_duration?(n.duration=d.default_length*d.next_note_duration,d.next_note_duration=0,l=!0):n.duration=d.default_length,o="duration");break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":case"0":case"/":if("octave"===o||"duration"===o){var u=t.getFraction(i,r);for(n.duration=n.duration*u.value,n.endChar=u.index;u.index<i.length&&(t.isWhiteSpace(i.charAt(u.index))||"-"===i.charAt(u.index));)"-"===i.charAt(u.index)?n.startTie={}:n=N(n),u.index++;r=u.index-1,o="broken_rhythm"}else if("sharp2"===o)n.accidental="quartersharp",o="pitch";else{if("flat2"!==o){if("Zduration"===o){var p=t.getNumber(i,r);return n.duration=p.num,n.endChar=p.index,n}return null}n.accidental="quarterflat",o="pitch"}break;case"-":if("startSlur"===o)e.addTieToLastNote(),n.endTie=!0;else{if("octave"!==o&&"duration"!==o&&"end_slur"!==o)return"broken_rhythm"===o?(n.endChar=r,n):null;if(n.startTie={},l||!a)return t.isWhiteSpace(i.charAt(r+1))&&N(n),n.endChar=r+1,n;o="broken_rhythm"}break;case" ":case"\t":if(!s(o))return null;n.end_beam=!0;do{"-"===i.charAt(r)&&(n.startTie={}),r++}while(r<i.length&&(t.isWhiteSpace(i.charAt(r))||"-"===i.charAt(r)));if(n.endChar=r,l||!a||"<"!==i.charAt(r)&&">"!==i.charAt(r))return n;r--,o="broken_rhythm";break;case">":case"<":if(!s(o))return null;if(!a)return n.endChar=r,n;var f=C(i,r);r+=f[0]-1,d.next_note_duration=f[2],n.duration=f[1]*n.duration,o="end_slur";break;default:return s(o)?(n.endChar=r,n):null}if(++r===i.length)return s(o)?(n.endChar=r,n):null}return null};function P(){var t={startChar:-1,endChar:-1};d.partForNextLine.title&&(t.part=d.partForNextLine),t.clef=d.currentVoice&&void 0!==d.staves[d.currentVoice.staffNum].clef?r.clone(d.staves[d.currentVoice.staffNum].clef):r.clone(d.clef);var i=d.currentVoice?d.currentVoice.scoreTranspose:0;if(t.key=s.standardKey(d.key.root+d.key.acc+d.key.mode,d.key.root,d.key.acc,i),t.key.mode=d.key.mode,d.key.impliedNaturals&&(t.key.impliedNaturals=d.key.impliedNaturals),d.key.explicitAccidentals)for(var n=0;n<d.key.explicitAccidentals.length;n++){for(var a=!1,o=0;o<t.key.accidentals.length;o++)t.key.accidentals[o].note===d.key.explicitAccidentals[n].note&&(t.key.accidentals[o].acc=d.key.explicitAccidentals[n].acc,a=!0);a||t.key.accidentals.push(d.key.explicitAccidentals[n])}if(d.targetKey=t.key,t.key.explicitAccidentals&&delete t.key.explicitAccidentals,s.addPosToKey(t.clef,t.key),null!==d.meter?(d.currentVoice?(r.each(d.staves,(function(e){e.meter=d.meter})),t.meter=d.staves[d.currentVoice.staffNum].meter,d.staves[d.currentVoice.staffNum].meter=null):t.meter=d.meter,d.meter=null):d.currentVoice&&d.staves[d.currentVoice.staffNum].meter&&(t.meter=d.staves[d.currentVoice.staffNum].meter,d.staves[d.currentVoice.staffNum].meter=null),d.currentVoice&&d.currentVoice.name&&(t.name=d.currentVoice.name),d.vocalfont&&(t.vocalfont=d.vocalfont),d.tripletfont&&(t.tripletfont=d.tripletfont),d.style&&(t.style=d.style),d.currentVoice){var c=d.staves[d.currentVoice.staffNum];c.brace&&(t.brace=c.brace),c.bracket&&(t.bracket=c.bracket),c.connectBarLines&&(t.connectBarLines=c.connectBarLines),c.name&&(t.name=c.name[d.currentVoice.index]),c.subname&&(t.subname=c.subname[d.currentVoice.index]),d.currentVoice.stem&&(t.stem=d.currentVoice.stem),d.currentVoice.stafflines&&(t.stafflines=d.currentVoice.stafflines),d.currentVoice.staffscale&&(t.staffscale=d.currentVoice.staffscale),d.currentVoice.scale&&(t.scale=d.currentVoice.scale),d.currentVoice.style&&(t.style=d.currentVoice.style),d.currentVoice.transpose&&(t.clef.transpose=d.currentVoice.transpose)}var l=void 0===d.currentVoice||0===d.currentVoice.staffNum&&0===d.currentVoice.index;0===d.barNumbers&&l&&1!==d.currBarNumber&&(t.barNumber=d.currBarNumber),e.startNewLine(t),d.key.impliedNaturals&&delete d.key.impliedNaturals,d.partForNextLine={}}var L=function(e,i){if("{"===e.charAt(i)){var r=t.getBrackettedSubstring(e,i,1,"}");r[2]||f("Missing the closing '}' while parsing grace note",e,i),")"===e[i+r[0]]&&(r[0]++,r[1]+=")");for(var n=[],a=0,s=!1;a<r[1].length;){var o=!1;"/"===r[1].charAt(a)&&(o=!0,a++);var c=B(r[1],a,{},!1);null!==c?(c.duration=c.duration/(8*d.default_length),o&&(c.acciaccatura=!0),n.push(c),s&&(c.endTie=!0,s=!1),c.startTie&&(s=!0),a=c.endChar,delete c.endChar):(" "===r[1].charAt(a)?n.length>0&&(n[n.length-1].end_beam=!0):f("Unknown character '"+r[1].charAt(a)+"' while parsing grace note",e,i),a++)}if(n.length)return[r[0],n]}return[0]};function H(e,t){if("&"===e.charAt(t)){for(var i=t;e.charAt(t)&&":"!==e.charAt(t)&&"|"!==e.charAt(t);)t++;return[t-i,e.substring(i+1,t)]}return[0]}function z(e){var t=e.origMeter;return t&&"specified"===t.type&&t.value&&0!==t.value.length?parseInt(t.value[0].num,10)/parseInt(t.value[0].den,10):1}var I=function i(n){var a=m.parseHeader(n);a.regular&&function(i){m.resolveTempo(),d.is_in_header=!1;for(var n=0,a=d.iChar;t.isWhiteSpace(i.charAt(n))&&n<i.length;)n++;if(n!==i.length&&"%"!==i.charAt(n)){var s=d.start_new_line;void 0===d.continueall?d.start_new_line=!0:d.start_new_line=!1;var o=0,c=m.letter_to_body_header(i,n);c[0]>0&&(n+=c[0],"V"===c[1]&&(s=!0));for(var l={},h=0;n<i.length;){var u=n;if("%"===i.charAt(n))break;var p=m.letter_to_inline_header(i,n);if(p[0]>0)n+=p[0],"V"===p[1]&&(s=!0);else{var v;for(s&&(P(),s=!1);;)if((v=t.eatWhiteSpace(i,n))>0&&(n+=v),n>0&&""===i.charAt(n-1)&&(v=m.letter_to_body_header(i,n))[0]>0&&("V"===v[1]&&P(),n=v[0],d.start_new_line=!1),(v=S(i,n))[0]>0&&(n+=v[0]),(v=g(i,n))[0]>0){l.chord||(l.chord=[]);var b=t.translateString(v[1]);b=b.replace(/;/g,"\n");for(var y=!1,w=0;w<l.chord.length;w++)l.chord[w].position===v[2]&&(y=!0,l.chord[w].name+="\n"+b);!1===y&&(null===v[2]&&v[3]?l.chord.push({name:b,rel_position:v[3]}):l.chord.push({name:b,position:v[2]})),n+=v[0];var k=t.skipWhiteSpace(i.substring(n));k>0&&(l.force_end_beam_last=!0),n+=k}else if((v=-1==="ABCDEFGabcdefgxyzZ[]|^_{".indexOf(i.charAt(n))?x(i,n):[0])[0]>0)null===v[1]?n+1<i.length&&P():v[1].length>0&&(0===v[1].indexOf("style=")?l.style=v[1].substr(6):(void 0===l.decoration&&(l.decoration=[]),l.decoration.push(v[1]))),n+=v[0];else{if(!((v=L(i,n))[0]>0))break;l.gracenotes=v[1],n+=v[0]}if((v=_(i,n))[0]>0){h=0,void 0!==l.gracenotes&&(l.rest={type:"spacer"},l.duration=.125,d.addFormattingOptions(l,e.formatting,"note"),e.appendElement("note",a+n,a+n+v[0],l),d.measureNotEmpty=!0,l={});var T={type:v[1]};if(0===T.type.length)f("Unknown bar type",i,n);else{if(d.inEnding&&"bar_thin"!==T.type&&(T.endEnding=!0,d.inEnding=!1),v[2]&&(T.startEnding=v[2],d.inEnding&&(T.endEnding=!0),d.inEnding=!0),void 0!==l.decoration&&(T.decoration=l.decoration),void 0!==l.chord&&(T.chord=l.chord),T.startEnding&&void 0===d.barFirstEndingNum?d.barFirstEndingNum=d.currBarNumber:T.startEnding&&T.endEnding&&d.barFirstEndingNum?d.currBarNumber=d.barFirstEndingNum:T.endEnding&&(d.barFirstEndingNum=void 0),"bar_invisible"!==T.type&&d.measureNotEmpty)(void 0===d.currentVoice||0===d.currentVoice.staffNum&&0===d.currentVoice.index)&&(d.currBarNumber++,d.barNumbers&&d.currBarNumber%d.barNumbers==0&&(T.barNumber=d.currBarNumber));d.addFormattingOptions(l,e.formatting,"bar"),e.appendElement("bar",a+n,a+n+v[0],T),d.measureNotEmpty=!1,l={}}n+=v[0];d.currentVoice&&(d.currentVoice.staffNum,d.currentVoice.index)}else if("&"===i[n])(v=H(i,n))[0]>0&&(e.appendElement("overlay",a,a+1,{}),n+=1,h++);else{if((v=A(i,n)).consumed>0&&(void 0!==v.startSlur&&(l.startSlur=v.startSlur),void 0!==v.triplet&&(o>0?f("Can't nest triplets",i,n):(l.startTriplet=v.triplet,l.tripletMultiplier=v.tripletQ/v.triplet,o=void 0===v.num_notes?v.triplet:v.num_notes)),n+=v.consumed),"["===i.charAt(n)){var E=n;n++;for(var M=null,I=!1,D=!1;!D;){var O=x(i,n);O[0]>0&&(n+=O[0]);var F=B(i,n,{},!1);if(null!==F)O[0]>0&&0!==O[1].indexOf("style=")&&(void 0===l.decoration&&(l.decoration=[]),l.decoration.push(O[1])),F.end_beam&&(l.end_beam=!0,delete F.end_beam),void 0===l.pitches?(l.duration=F.duration,l.pitches=[F]):l.pitches.push(F),delete F.duration,O[0]>0&&0===O[1].indexOf("style=")&&(l.pitches[l.pitches.length-1].style=O[1].substr(6)),d.inTieChord[l.pitches.length]&&(F.endTie=!0,d.inTieChord[l.pitches.length]=void 0),F.startTie&&(d.inTieChord[l.pitches.length]=!0),n=F.endChar,delete F.endChar;else if(" "===i.charAt(n))f("Spaces are not allowed in chords",i,n),n++;else{if(n<i.length&&"]"===i.charAt(n)){n++,0!==d.next_note_duration&&(l.duration=l.duration*d.next_note_duration,d.next_note_duration=0),d.inTie[h]&&(r.each(l.pitches,(function(e){e.endTie=!0})),d.inTie[h]=!1),o>0&&0===--o&&(l.endTriplet=!0);for(var Y=!1;n<i.length&&!Y;){switch(i.charAt(n)){case" ":case"\t":N(l);break;case")":void 0===l.endSlur?l.endSlur=1:l.endSlur++;break;case"-":r.each(l.pitches,(function(e){e.startTie={}})),d.inTie[h]=!0;break;case">":case"<":var j=C(i,n);n+=j[0]-1,d.next_note_duration=j[2],M?M*=j[1]:M=j[1];break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":case"/":var V=t.getFraction(i,n);M=V.value,n=V.index," "===i.charAt(n)&&(I=!0),"-"===i.charAt(n)||")"===i.charAt(n)||" "===i.charAt(n)||"<"===i.charAt(n)||">"===i.charAt(n)?n--:Y=!0;break;default:Y=!0}Y||n++}}else f("Expected ']' to end the chords",i,n);void 0!==l.pitches&&(null!==M&&(l.duration=l.duration*M,I&&N(l)),d.addFormattingOptions(l,e.formatting,"note"),e.appendElement("note",a+E,a+n,l),d.measureNotEmpty=!0,l={}),D=!0}}}else{var G={},R=B(i,n,G,!0);void 0!==G.endTie&&(d.inTie[h]=!0),null!==R&&(void 0!==R.pitch?(l.pitches=[{}],void 0!==R.accidental&&(l.pitches[0].accidental=R.accidental),l.pitches[0].pitch=R.pitch,R.midipitch&&(l.pitches[0].midipitch=R.midipitch),void 0!==R.endSlur&&(l.pitches[0].endSlur=R.endSlur),void 0!==R.endTie&&(l.pitches[0].endTie=R.endTie),void 0!==R.startSlur&&(l.pitches[0].startSlur=R.startSlur),void 0!==l.startSlur&&(l.pitches[0].startSlur=l.startSlur),void 0!==R.startTie&&(l.pitches[0].startTie=R.startTie),void 0!==l.startTie&&(l.pitches[0].startTie=l.startTie)):(l.rest=R.rest,void 0!==R.endSlur&&(l.endSlur=R.endSlur),void 0!==R.endTie&&(l.rest.endTie=R.endTie),void 0!==R.startSlur&&(l.startSlur=R.startSlur),void 0!==R.startTie&&(l.rest.startTie=R.startTie),void 0!==l.startTie&&(l.rest.startTie=l.startTie)),void 0!==R.chord&&(l.chord=R.chord),void 0!==R.duration&&(l.duration=R.duration),void 0!==R.decoration&&(l.decoration=R.decoration),void 0!==R.graceNotes&&(l.graceNotes=R.graceNotes),delete l.startSlur,d.inTie[h]&&(void 0!==l.pitches?(l.pitches[0].endTie=!0,d.inTie[h]=!1):"spacer"!==l.rest.type&&(l.rest.endTie=!0,d.inTie[h]=!1)),(R.startTie||l.startTie)&&(d.inTie[h]=!0),n=R.endChar,o>0&&0===--o&&(l.endTriplet=!0),R.end_beam&&N(l),l.rest&&"rest"===l.rest.type&&1===l.duration&&z(d)<=1&&(l.rest.type="whole",l.duration=z(d)),d.addFormattingOptions(l,e.formatting,"note"),e.appendElement("note",a+u,a+n,l),d.measureNotEmpty=!0,l={})}n===u&&(" "!==i.charAt(n)&&"`"!==i.charAt(n)&&f("Unknown character ignored",i,n),n++)}}}}}(a.str),a.newline&&P(),a.words&&function(e,i){if(e){"-"!==(i=r.strip(i)).charAt(i.length-1)&&(i+=" ");for(var n=[],a=0,s=!1,o=function(e){var o=r.strip(i.substring(a,e));if(a=e+1,o.length>0){s&&(o=r.gsub(o,"~"," "));var c=i.charAt(e);return"_"!==c&&"-"!==c&&(c=" "),n.push({syllable:t.translateString(o),divider:c}),s=!1,!0}return!1},c=0;c<i.length;c++)switch(i.charAt(c)){case" ":case"":o(c);break;case"-":!o(c)&&n.length>0&&(r.last(n).divider="-",n.push({skip:!0,to:"next"}));break;case"_":o(c),n.push({skip:!0,to:"slur"});break;case"*":o(c),n.push({skip:!0,to:"next"});break;case"|":o(c),n.push({skip:!0,to:"bar"});break;case"~":s=!0}r.each(e,(function(e){if(0!==n.length)if(n[0].skip){switch(n[0].to){case"next":case"slur":"note"===e.el_type&&null!==e.pitches&&n.shift();break;case"bar":"bar"===e.el_type&&n.shift()}"bar"!==e.el_type&&(void 0===e.lyric?e.lyric=[{syllable:"",divider:" "}]:e.lyric.push({syllable:"",divider:" "}))}else if("note"===e.el_type&&void 0===e.rest){var t=n.shift();t.syllable&&(t.syllable=t.syllable.replace(/ +/g," ")),void 0===e.lyric?e.lyric=[t]:e.lyric.push(t)}}))}else f("Can't add words before the first line of music",e,0)}(e.getCurrentVoice(),n.substring(2)),a.symbols&&function(e,i){if(e){"-"!==(i=r.strip(i)).charAt(i.length-1)&&(i+=" ");for(var n=[],a=0,s=!1,o=function(e){var o=r.strip(i.substring(a,e));if(a=e+1,o.length>0){s&&(o=r.gsub(o,"~"," "));var c=i.charAt(e);return"_"!==c&&"-"!==c&&(c=" "),n.push({syllable:t.translateString(o),divider:c}),s=!1,!0}return!1},c=0;c<i.length;c++)switch(i.charAt(c)){case" ":case"":o(c);break;case"-":!o(c)&&n.length>0&&(r.last(n).divider="-",n.push({skip:!0,to:"next"}));break;case"_":o(c),n.push({skip:!0,to:"slur"});break;case"*":o(c),n.push({skip:!0,to:"next"});break;case"|":o(c),n.push({skip:!0,to:"bar"});break;case"~":s=!0}r.each(e,(function(e){if(0!==n.length)if(n[0].skip)switch(n[0].to){case"next":case"slur":"note"===e.el_type&&null!==e.pitches&&n.shift();break;case"bar":"bar"===e.el_type&&n.shift()}else if("note"===e.el_type&&void 0===e.rest){var t=n.shift();void 0===e.lyric?e.lyric=[t]:e.lyric.push(t)}}))}else f("Can't add symbols before the first line of music",e,0)}(e.getCurrentVoice(),n.substring(2)),a.recurse&&i(a.str)};function D(e,t){e.push({el_type:"hint"});for(var i=0;i<t.length;i++){var n=t[i],a=r.clone(n);if(e.push(a),"bar"===n.el_type)return}}function O(e,t){for(var i=0;i<e.length;i++){var r=e[i],n=t[i];if(n)for(var a=0;a<n.voices.length;a++){var s=n.voices[a],o=r.voices[a];o&&D(o,s)}}}this.parse=function(i,a,s){if(a||(a={}),s||(s=0),e.reset(),a.print&&(e.media="print"),d.reset(),d.iChar=s,a.visualTranspose?(d.globalTranspose=parseInt(a.visualTranspose),0===d.globalTranspose&&(d.globalTranspose=void 0)):d.globalTranspose=void 0,a.lineBreaks){d.lineBreaks={};for(var o=0;o<a.lineBreaks.length;o++)d.lineBreaks[""+(a.lineBreaks[o]+1)]=!0}m.reset(t,f,d,e),i=r.gsub(i,"\r\n","\n"),i=r.gsub(i,"\r","\n");var c=(i=(i=(i+="\n").replace(/\n\\.*\n/g,"\n")).replace(/\\([ \t]*)(%.*)*\n/g,(function(e,t,i){return t+" "+(i?"                                                                                                                                                                                                     ".substring(0,i.length):"")}))).split("\n");0===r.last(c).length&&c.pop();try{a.format&&n.globalFormatting(a.format),r.each(c,(function(i){if(a.header_only&&!1===d.is_in_header)throw"normal_abort";if(a.stop_on_warning&&d.warnings)throw"normal_abort";d.is_in_history?":"===i.charAt(1)?(d.is_in_history=!1,I(i)):e.addMetaText("history",t.translateString(t.stripComment(i))):d.inTextBlock?r.startsWith(i,"%%endtext")?(e.addText(d.textBlock),d.inTextBlock=!1):r.startsWith(i,"%%")?d.textBlock+=" "+i.substring(2):d.textBlock+=" "+i:d.inPsBlock?r.startsWith(i,"%%endps")?d.inPsBlock=!1:d.textBlock+=" "+i:I(i),d.iChar+=i.length+1}));var h=792,u=612;switch(d.papersize){case"legal":h=1008,u=612;break;case"A4":h=842.4,u=597.6}if(d.landscape){var p=h;h=u,u=p}d.openSlurs=e.cleanUp(u,h,d.barsperstaff,d.staffnonote,d.openSlurs)}catch(e){if("normal_abort"!==e)throw e}a.hint_measures&&function(){for(var t=0;t<e.lines.length;t++){var i=e.lines[t].staff;if(i){for(var r=t+1;r<e.lines.length&&void 0===e.lines[r].staff;)r++;if(r<e.lines.length)O(i,e.lines[r].staff)}}}(),l.wrapLines(e,d.lineBreaks)}}},function(e,t,i){var r=i(0),n={};!function(){"use strict";var e,t,i,a;n.initialize=function(r,n,s,o){e=r,t=n,a=o,(i=s).annotationfont={face:"Helvetica",size:12,weight:"normal",style:"normal",decoration:"none"},i.gchordfont={face:"Helvetica",size:12,weight:"normal",style:"normal",decoration:"none"},i.historyfont={face:'"Times New Roman"',size:16,weight:"normal",style:"normal",decoration:"none"},i.infofont={face:'"Times New Roman"',size:14,weight:"normal",style:"italic",decoration:"none"},i.measurefont={face:'"Times New Roman"',size:14,weight:"normal",style:"italic",decoration:"none"},i.partsfont={face:'"Times New Roman"',size:15,weight:"normal",style:"normal",decoration:"none"},i.repeatfont={face:'"Times New Roman"',size:13,weight:"normal",style:"normal",decoration:"none"},i.textfont={face:'"Times New Roman"',size:16,weight:"normal",style:"normal",decoration:"none"},i.tripletfont={face:"Times",size:11,weight:"normal",style:"italic",decoration:"none"},i.vocalfont={face:'"Times New Roman"',size:13,weight:"bold",style:"normal",decoration:"none"},i.wordsfont={face:'"Times New Roman"',size:16,weight:"normal",style:"normal",decoration:"none"},a.formatting.composerfont={face:'"Times New Roman"',size:14,weight:"normal",style:"italic",decoration:"none"},a.formatting.subtitlefont={face:'"Times New Roman"',size:16,weight:"normal",style:"normal",decoration:"none"},a.formatting.tempofont={face:'"Times New Roman"',size:15,weight:"bold",style:"normal",decoration:"none"},a.formatting.titlefont={face:'"Times New Roman"',size:20,weight:"normal",style:"normal",decoration:"none"},a.formatting.footerfont={face:'"Times New Roman"',size:12,weight:"normal",style:"normal",decoration:"none"},a.formatting.headerfont={face:'"Times New Roman"',size:12,weight:"normal",style:"normal",decoration:"none"},a.formatting.voicefont={face:'"Times New Roman"',size:13,weight:"bold",style:"normal",decoration:"none"},a.formatting.annotationfont=i.annotationfont,a.formatting.gchordfont=i.gchordfont,a.formatting.historyfont=i.historyfont,a.formatting.infofont=i.infofont,a.formatting.measurefont=i.measurefont,a.formatting.partsfont=i.partsfont,a.formatting.repeatfont=i.repeatfont,a.formatting.textfont=i.textfont,a.formatting.tripletfont=i.tripletfont,a.formatting.vocalfont=i.vocalfont,a.formatting.wordsfont=i.wordsfont};var s={gchordfont:!0,measurefont:!0,partsfont:!0},o=function(e,i,r,n,a){function o(){var o=parseInt(e[0].token);return e.shift(),i?0===e.length?{face:i.face,weight:i.weight,style:i.style,decoration:i.decoration,size:o}:1===e.length&&"box"===e[0].token&&s[a]?{face:i.face,weight:i.weight,style:i.style,decoration:i.decoration,size:o,box:!0}:(t("Extra parameters in font definition.",r,n),{face:i.face,weight:i.weight,style:i.style,decoration:i.decoration,size:o}):(t("Can't set just the size of the font since there is no default value.",r,n),{face:'"Times New Roman"',weight:"normal",style:"normal",decoration:"none",size:o})}if("*"===e[0].token){if(e.shift(),"number"===e[0].type)return o();t("Expected font size number after *.",r,n)}if("number"===e[0].type)return o();for(var c,l=[],h="normal",u="normal",d="none",p=!1,f="face",m=!1;e.length;){var g=e.shift(),v=g.token.toLowerCase();switch(f){case"face":m||"utf"!==v&&"number"!==g.type&&"bold"!==v&&"italic"!==v&&"underline"!==v&&"box"!==v?l.length>0&&"-"===g.token?(m=!0,l[l.length-1]=l[l.length-1]+g.token):m?(m=!1,l[l.length-1]=l[l.length-1]+g.token):l.push(g.token):"number"===g.type?(c?t("Font size specified twice in font definition.",r,n):c=g.token,f="modifier"):"bold"===v?h="bold":"italic"===v?u="italic":"underline"===v?d="underline":"box"===v?(s[a]?p=!0:t('This font style doesn\'t support "box"',r,n),f="finished"):"utf"===v?(g=e.shift(),f="size"):t("Unknown parameter "+g.token+" in font definition.",r,n);break;case"size":"number"===g.type?c?t("Font size specified twice in font definition.",r,n):c=g.token:t("Expected font size in font definition.",r,n),f="modifier";break;case"modifier":"bold"===v?h="bold":"italic"===v?u="italic":"underline"===v?d="underline":"box"===v?(s[a]?p=!0:t('This font style doesn\'t support "box"',r,n),f="finished"):t("Unknown parameter "+g.token+" in font definition.",r,n);break;case"finished":t('Extra characters found after "box" in font definition.',r,n)}}void 0===c?i?c=i.size:(t("Must specify the size of the font since there is no default value.",r,n),c=12):c=parseFloat(c);var b=function(e){switch(e){case"Arial-Italic":return{face:"Arial",weight:"normal",style:"italic",decoration:"none"};case"Arial-Bold":return{face:"Arial",weight:"bold",style:"normal",decoration:"none"};case"Bookman-Demi":return{face:"Bookman,serif",weight:"bold",style:"normal",decoration:"none"};case"Bookman-DemiItalic":return{face:"Bookman,serif",weight:"bold",style:"italic",decoration:"none"};case"Bookman-Light":return{face:"Bookman,serif",weight:"normal",style:"normal",decoration:"none"};case"Bookman-LightItalic":return{face:"Bookman,serif",weight:"normal",style:"italic",decoration:"none"};case"Courier":return{face:'"Courier New"',weight:"normal",style:"normal",decoration:"none"};case"Courier-Oblique":return{face:'"Courier New"',weight:"normal",style:"italic",decoration:"none"};case"Courier-Bold":return{face:'"Courier New"',weight:"bold",style:"normal",decoration:"none"};case"Courier-BoldOblique":return{face:'"Courier New"',weight:"bold",style:"italic",decoration:"none"};case"AvantGarde-Book":return{face:"AvantGarde,Arial",weight:"normal",style:"normal",decoration:"none"};case"AvantGarde-BookOblique":return{face:"AvantGarde,Arial",weight:"normal",style:"italic",decoration:"none"};case"AvantGarde-Demi":case"Avant-Garde-Demi":return{face:"AvantGarde,Arial",weight:"bold",style:"normal",decoration:"none"};case"AvantGarde-DemiOblique":return{face:"AvantGarde,Arial",weight:"bold",style:"italic",decoration:"none"};case"Helvetica-Oblique":return{face:"Helvetica",weight:"normal",style:"italic",decoration:"none"};case"Helvetica-Bold":return{face:"Helvetica",weight:"bold",style:"normal",decoration:"none"};case"Helvetica-BoldOblique":return{face:"Helvetica",weight:"bold",style:"italic",decoration:"none"};case"Helvetica-Narrow":return{face:'"Helvetica Narrow",Helvetica',weight:"normal",style:"normal",decoration:"none"};case"Helvetica-Narrow-Oblique":return{face:'"Helvetica Narrow",Helvetica',weight:"normal",style:"italic",decoration:"none"};case"Helvetica-Narrow-Bold":return{face:'"Helvetica Narrow",Helvetica',weight:"bold",style:"normal",decoration:"none"};case"Helvetica-Narrow-BoldOblique":return{face:'"Helvetica Narrow",Helvetica',weight:"bold",style:"italic",decoration:"none"};case"Palatino-Roman":return{face:"Palatino",weight:"normal",style:"normal",decoration:"none"};case"Palatino-Italic":return{face:"Palatino",weight:"normal",style:"italic",decoration:"none"};case"Palatino-Bold":return{face:"Palatino",weight:"bold",style:"normal",decoration:"none"};case"Palatino-BoldItalic":return{face:"Palatino",weight:"bold",style:"italic",decoration:"none"};case"NewCenturySchlbk-Roman":return{face:'"New Century",serif',weight:"normal",style:"normal",decoration:"none"};case"NewCenturySchlbk-Italic":return{face:'"New Century",serif',weight:"normal",style:"italic",decoration:"none"};case"NewCenturySchlbk-Bold":return{face:'"New Century",serif',weight:"bold",style:"normal",decoration:"none"};case"NewCenturySchlbk-BoldItalic":return{face:'"New Century",serif',weight:"bold",style:"italic",decoration:"none"};case"Times":case"Times-Roman":case"Times-Narrow":case"Times-Courier":case"Times-New-Roman":return{face:'"Times New Roman"',weight:"normal",style:"normal",decoration:"none"};case"Times-Italic":case"Times-Italics":return{face:'"Times New Roman"',weight:"normal",style:"italic",decoration:"none"};case"Times-Bold":return{face:'"Times New Roman"',weight:"bold",style:"normal",decoration:"none"};case"Times-BoldItalic":return{face:'"Times New Roman"',weight:"bold",style:"italic",decoration:"none"};case"ZapfChancery-MediumItalic":return{face:'"Zapf Chancery",cursive,serif',weight:"normal",style:"normal",decoration:"none"};default:return null}}(l=l.join(" ")),y={};return b?(y.face=b.face,y.weight=b.weight,y.style=b.style,y.decoration=b.decoration,y.size=c,p&&(y.box=!0),y):(y.face=l,y.weight=h,y.style=u,y.decoration=d,y.size=c,p&&(y.box=!0),y)},c=function(e,t,r){return 0===t.length?'Directive "'+e+'" requires a font as a parameter.':(i[e]=o(t,i[e],r,0,e),i.is_in_header&&(a.formatting[e]=i[e]),null)},l=function(e,t){var i="";r.each(t,(function(e){i+=e.token}));var n=parseFloat(i);if(isNaN(n)||0===n)return'Directive "'+e+'" requires a number as a parameter.';a.formatting.scale=n},h=function(e,t,r,n,a){if(1!==r.length||"number"!==r[0].type)return'Directive "'+t+'" requires a number as a parameter.';var s=r[0].intt;return void 0!==n&&s<n?'Directive "'+t+'" requires a number greater than or equal to '+n+" as a parameter.":void 0!==a&&s>a?'Directive "'+t+'" requires a number less than or equal to '+a+" as a parameter.":(i[e]=s,null)},u=function(e,t,r){if(1===r.length&&("true"===r[0].token||"false"===r[0].token))return i[e]="true"===r[0].token,null;var n=h(e,t,r,0,1);return null!==n?n:(i[e]=1===i[e],null)},d=function(e,t,r,n){if(1!==r.length)return'Directive "'+t+'" requires one of [ '+n.join(", ")+" ] as a parameter.";for(var a=r[0].token,s=!1,o=0;!s&&o<n.length;o++)n[o]===a&&(s=!0);return s?(i[e]=a,null):'Directive "'+t+'" requires one of [ '+n.join(", ")+" ] as a parameter."},p=["nobarlines","barlines","beataccents","nobeataccents","droneon","droneoff","drumon","drumoff","fermatafixed","fermataproportional","gchordon","gchordoff","controlcombo","temperamentnormal","noportamento"],f=["gchord","ptstress","beatstring"],m=["bassvol","chordvol","c","channel","beatmod","deltaloudness","drumbars","gracedivider","makechordchannels","randomchordattack","chordattack","stressmodel","transpose","rtranspose","volinc"],g=["program"],v=["ratio","snt","bendvelocity","pitchbend","control","temperamentlinear"],b=["beat"],y=["drone"],w=["portamento"],k=["expand","grace","trim"],x=["drum","chordname"];n.parseFontChangeLine=function(e){var t=e.split("$");if(t.length>1&&i.setfont){for(var r=[{text:t[0]}],n=1;n<t.length;n++)"0"===t[n].charAt(0)?r.push({text:t[n].substring(1)}):"1"===t[n].charAt(0)&&i.setfont[1]?r.push({font:i.setfont[1],text:t[n].substring(1)}):"2"===t[n].charAt(0)&&i.setfont[2]?r.push({font:i.setfont[2],text:t[n].substring(1)}):"3"===t[n].charAt(0)&&i.setfont[3]?r.push({font:i.setfont[3],text:t[n].substring(1)}):"4"===t[n].charAt(0)&&i.setfont[4]?r.push({font:i.setfont[4],text:t[n].substring(1)}):r[r.length-1].text+="$"+t[n];if(r.length>1)return r}return e};var S=["auto","above","below","hidden"];n.addDirective=function(s){var _=e.tokenize(s,0,s.length);if(0===_.length||"alpha"!==_[0].type)return null;var T=s.substring(s.indexOf(_[0].token)+_[0].token.length);T=e.stripComment(T);var A=_.shift().token.toLowerCase(),C="";switch(A){case"bagpipes":a.formatting.bagpipes=!0;break;case"flatbeams":a.formatting.flatbeams=!0;break;case"landscape":i.landscape=!0;break;case"papersize":i.papersize=T;break;case"slurgraces":a.formatting.slurgraces=!0;break;case"stretchlast":a.formatting.stretchlast=!0;break;case"titlecaps":i.titlecaps=!0;break;case"titleleft":a.formatting.titleleft=!0;break;case"measurebox":a.formatting.measurebox=!0;break;case"vocal":return d("vocalPosition",A,_,S);case"dynamic":return d("dynamicPosition",A,_,S);case"gchord":return d("chordPosition",A,_,S);case"ornament":return d("ornamentPosition",A,_,S);case"volume":return d("volumePosition",A,_,S);case"botmargin":case"botspace":case"composerspace":case"indent":case"leftmargin":case"linesep":case"musicspace":case"partsspace":case"pageheight":case"pagewidth":case"rightmargin":case"staffsep":case"staffwidth":case"subtitlespace":case"sysstaffsep":case"systemsep":case"textspace":case"titlespace":case"topmargin":case"topspace":case"vocalspace":case"wordsspace":return function(t,i){var r=e.getMeasurement(i);return 0===r.used||0!==i.length?'Directive "'+t+'" requires a measurement as a parameter.':(a.formatting[t]=r.value,null)}(A,_);case"voicescale":if(1!==_.length||"number"!==_[0].type)return"voicescale requires one float as a parameter";var N=_.shift();return i.currentVoice&&(i.currentVoice.scale=N.floatt,a.changeVoiceScale(i.currentVoice.scale)),null;case"vskip":var E=function(t,i){var r=e.getMeasurement(i);return 0===r.used||0!==i.length?{error:'Directive "'+t+'" requires a measurement as a parameter.'}:r.value}(A,_);return E.error?E.error:(a.addSpacing(E),null);case"scale":l(A,_);break;case"sep":if(0===_.length)a.addSeparator();else{var M=e.getMeasurement(_);if(0===M.used)return'Directive "'+A+'" requires 3 numbers: space above, space below, length of line';var B=M.value;if(0===(M=e.getMeasurement(_)).used)return'Directive "'+A+'" requires 3 numbers: space above, space below, length of line';var P=M.value;if(0===(M=e.getMeasurement(_)).used||0!==_.length)return'Directive "'+A+'" requires 3 numbers: space above, space below, length of line';var L=M.value;a.addSeparator(B,P,L)}break;case"barsperstaff":if(null!==(C=h("barsperstaff",A,_)))return C;break;case"staffnonote":if(1!==_.length)return"Directive staffnonote requires one parameter: 0 or 1";if("0"===_[0].token)i.staffnonote=!0;else{if("1"!==_[0].token)return"Directive staffnonote requires one parameter: 0 or 1 (received "+_[0].token+")";i.staffnonote=!1}break;case"printtempo":if(null!==(C=u("printTempo",A,_)))return C;break;case"partsbox":if(null!==(C=u("partsBox",A,_)))return C;i.partsfont.box=i.partsBox;break;case"freegchord":if(null!==(C=u("freegchord",A,_)))return C;break;case"measurenb":case"barnumbers":if(null!==(C=h("barNumbers",A,_)))return C;break;case"setbarnb":if(1!==_.length||"number"!==_[0].type)return"Directive setbarnb requires a number as a parameter.";i.currBarNumber=a.setBarNumberImmediate(_[0].intt);break;case"begintext":i.inTextBlock=!0;break;case"continueall":i.continueall=!0;break;case"beginps":i.inPsBlock=!0,t("Postscript ignored",s,0);break;case"deco":T.length>0&&i.ignoredDecorations.push(T.substring(0,T.indexOf(" "))),t("Decoration redefinition ignored",s,0);break;case"text":var H=e.translateString(T);a.addText(n.parseFontChangeLine(H));break;case"center":var z=e.translateString(T);a.addCentered(n.parseFontChangeLine(z));break;case"font":break;case"setfont":var I=e.tokenize(T,0,T.length);if(I.length>=4&&"-"===I[0].token&&"number"===I[1].type){var D=parseInt(I[1].token);D>=1&&D<=4&&(i.setfont||(i.setfont=[]),I.shift(),I.shift(),i.setfont[D]=o(I,i.setfont[D],s,0,"setfont"))}break;case"gchordfont":case"partsfont":case"tripletfont":case"vocalfont":case"textfont":case"annotationfont":case"historyfont":case"infofont":case"measurefont":case"repeatfont":case"wordsfont":return c(A,_,s);case"composerfont":case"subtitlefont":case"tempofont":case"titlefont":case"voicefont":case"footerfont":case"headerfont":return function(e,t,i){return 0===t.length?'Directive "'+e+'" requires a font as a parameter.':(a.formatting[e]=o(t,a.formatting[e],i,0,e),null)}(A,_,s);case"barlabelfont":case"barnumberfont":case"barnumfont":return c("measurefont",_,s);case"staves":case"score":i.score_is_present=!0;for(var O,F=function(e,t,n,a,s){(t||0===i.staves.length)&&i.staves.push({index:i.staves.length,numVoices:0});var o=r.last(i.staves);void 0!==n&&(o.bracket=n),void 0!==a&&(o.brace=a),s&&(o.connectBarLines="end"),void 0===i.voices[e]&&(i.voices[e]={staffNum:o.index,index:o.numVoices},o.numVoices++)},Y=!1,j=!1,V=!1,G=!1,R=!1,W=!1,q=!1,U=function(){if(q=!0,O){var e="start";O.staffNum>0&&("start"!==i.staves[O.staffNum-1].connectBarLines&&"continue"!==i.staves[O.staffNum-1].connectBarLines||(e="continue")),i.staves[O.staffNum].connectBarLines=e}};_.length;){var X=_.shift();switch(X.token){case"(":Y?t("Can't nest parenthesis in %%score",s,X.start):(Y=!0,G=!0);break;case")":!Y||G?t("Unexpected close parenthesis in %%score",s,X.start):Y=!1;break;case"[":j?t("Can't nest brackets in %%score",s,X.start):(j=!0,R=!0);break;case"]":!j||R?t("Unexpected close bracket in %%score",s,X.start):(j=!1,i.staves[O.staffNum].bracket="end");break;case"{":V?t("Can't nest braces in %%score",s,X.start):(V=!0,W=!0);break;case"}":!V||W?t("Unexpected close brace in %%score",s,X.start):(V=!1,i.staves[O.staffNum].brace="end");break;case"|":U();break;default:for(var K="";("alpha"===X.type||"number"===X.type)&&(K+=X.token,X.continueId);)X=_.shift();F(K,!Y||G,R?"start":j?"continue":void 0,W?"start":V?"continue":void 0,q),G=!1,R=!1,W=!1,q=!1,O=i.voices[K],"staves"===A&&U()}}break;case"newpage":var Q=e.getInt(T);a.addNewPage(0===Q.digits?-1:Q.value);break;case"abc":var Z=T.split(" ");switch(Z[0]){case"-copyright":case"-creator":case"-edited-by":case"-version":case"-charset":var $=Z.shift();a.addMetaText(A+$,Z.join(" "));break;default:return"Unknown directive: "+A+Z[0]}break;case"header":case"footer":var J=e.getMeat(T,0,T.length);'"'===(J=T.substring(J.start,J.end)).charAt(0)&&'"'===J.charAt(J.length-1)&&(J=J.substring(1,J.length-1));var ee=J.split("\t"),te={};te=1===ee.length?{left:"",center:ee[0],right:""}:2===ee.length?{left:ee[0],center:ee[1],right:""}:{left:ee[0],center:ee[1],right:ee[2]},ee.length>3&&t("Too many tabs in "+A+": "+ee.length+" found.",T,0),a.addMetaTextObj(A,te);break;case"midi":var ie=e.tokenize(T,0,T.length,!0);ie.length>0&&"="===ie[0].token&&ie.shift(),0===ie.length?t("Expected midi command",T,0):function(e,i,r){var n=e.shift().token,a=[];if(p.indexOf(n)>=0)0!==e.length&&t("Unexpected parameter in MIDI "+n,r,0);else if(f.indexOf(n)>=0)1!==e.length?t("Expected one parameter in MIDI "+n,r,0):a.push(e[0].token);else if(m.indexOf(n)>=0)1!==e.length?t("Expected one parameter in MIDI "+n,r,0):"number"!==e[0].type?t("Expected one integer parameter in MIDI "+n,r,0):a.push(e[0].intt);else if(g.indexOf(n)>=0)1!==e.length&&2!==e.length?t("Expected one or two parameters in MIDI "+n,r,0):"number"!==e[0].type?t("Expected integer parameter in MIDI "+n,r,0):2===e.length&&"number"!==e[1].type?t("Expected integer parameter in MIDI "+n,r,0):(a.push(e[0].intt),2===e.length&&a.push(e[1].intt));else if(v.indexOf(n)>=0)2!==e.length?t("Expected two parameters in MIDI "+n,r,0):"number"!==e[0].type||"number"!==e[1].type?t("Expected two integer parameters in MIDI "+n,r,0):(a.push(e[0].intt),a.push(e[1].intt));else if(w.indexOf(n)>=0)2!==e.length?t("Expected two parameters in MIDI "+n,r,0):"alpha"!==e[0].type||"number"!==e[1].type?t("Expected one string and one integer parameters in MIDI "+n,r,0):(a.push(e[0].token),a.push(e[1].intt));else if("drummap"===n)2===e.length&&"alpha"===e[0].type&&"number"===e[1].type?(i.formatting||(i.formatting={}),i.formatting.midi||(i.formatting.midi={}),i.formatting.midi.drummap||(i.formatting.midi.drummap={}),i.formatting.midi.drummap[e[0].token]=e[1].intt,a=i.formatting.midi.drummap):3===e.length&&"punct"===e[0].type&&"alpha"===e[1].type&&"number"===e[2].type?(i.formatting||(i.formatting={}),i.formatting.midi||(i.formatting.midi={}),i.formatting.midi.drummap||(i.formatting.midi.drummap={}),i.formatting.midi.drummap[e[0].token+e[1].token]=e[2].intt,a=i.formatting.midi.drummap):t("Expected one note name and one integer parameter in MIDI "+n,r,0);else if(k.indexOf(n)>=0)3!==e.length?t("Expected fraction parameter in MIDI "+n,r,0):"number"!==e[0].type||"/"!==e[1].token||"number"!==e[2].type?t("Expected fraction parameter in MIDI "+n,r,0):(a.push(e[0].intt),a.push(e[2].intt));else if(b.indexOf(n)>=0)4!==e.length?t("Expected four parameters in MIDI "+n,r,0):"number"!==e[0].type||"number"!==e[1].type||"number"!==e[2].type||"number"!==e[3].type?t("Expected four integer parameters in MIDI "+n,r,0):(a.push(e[0].intt),a.push(e[1].intt),a.push(e[2].intt),a.push(e[3].intt));else if(y.indexOf(n)>=0)5!==e.length?t("Expected five parameters in MIDI "+n,r,0):"number"!==e[0].type||"number"!==e[1].type||"number"!==e[2].type||"number"!==e[3].type||"number"!==e[4].type?t("Expected five integer parameters in MIDI "+n,r,0):(a.push(e[0].intt),a.push(e[1].intt),a.push(e[2].intt),a.push(e[3].intt),a.push(e[4].intt));else if(g.indexOf(n)>=0)1!==e.length||4!==e.length?t("Expected one or two parameters in MIDI "+n,r,0):"number"!==e[0].type?t("Expected integer parameter in MIDI "+n,r,0):4===e.length?("octave"!==e[1].token&&t("Expected octave parameter in MIDI "+n,r,0),"="!==e[2].token&&t("Expected octave parameter in MIDI "+n,r,0),"number"!==e[3].type&&t("Expected integer parameter for octave in MIDI "+n,r,0)):(a.push(e[0].intt),4===e.length&&a.push(e[3].intt));else if(x.indexOf(n)>=0)if(e.length<2)t("Expected string parameter and at least one integer parameter in MIDI "+n,r,0);else if("alpha"!==e[0].type)t("Expected string parameter and at least one integer parameter in MIDI "+n,r,0);else{var s=e.shift();for(a.push(s.token);e.length>0;)"number"!==(s=e.shift()).type&&t("Expected integer parameter in MIDI "+n,r,0),a.push(s.intt)}i.hasBeginMusic()?i.appendElement("midi",-1,-1,{cmd:n,params:a}):(void 0===i.formatting.midi&&(i.formatting.midi={}),i.formatting.midi[n]=a)}(ie,a,T);break;case"map":case"percmap":case"playtempo":case"auquality":case"continuous":case"nobarcheck":a.formatting[A]=T;break;default:return"Unknown directive: "+A}return null},n.globalFormatting=function(r){for(var n in r)if(r.hasOwnProperty(n)){var a,s=""+r[n],o=e.tokenize(s,0,s.length);switch(n){case"titlefont":case"gchordfont":case"composerfont":case"footerfont":case"headerfont":case"historyfont":case"infofont":case"measurefont":case"partsfont":case"repeatfont":case"subtitlefont":case"tempofont":case"textfont":case"voicefont":case"tripletfont":case"vocalfont":case"wordsfont":case"annotationfont":c(n,o,s);break;case"scale":l(n,o);break;case"partsbox":null!==(a=u("partsBox",n,o))&&t(a),i.partsfont.box=i.partsBox;break;case"freegchord":null!==(a=u("freegchord",n,o))&&t(a);default:t("Formatting directive unrecognized: ",n,0)}}}}(),e.exports=n},function(e,t,i){var r=i(0),n=i(11),a=i(16),s={};!function(){var e,t,i,o;s.initialize=function(r,n,a,s){e=r,t=n,i=a,o=s},s.standardKey=function(e,t,r,n){var s={acc:"sharp",note:"f"},o={acc:"sharp",note:"c"},c={acc:"sharp",note:"g"},l={acc:"sharp",note:"d"},h={acc:"sharp",note:"A"},u={acc:"sharp",note:"e"},d={acc:"sharp",note:"B"},p={acc:"flat",note:"B"},f={acc:"flat",note:"e"},m={acc:"flat",note:"A"},g={acc:"flat",note:"d"},v={acc:"flat",note:"G"},b={acc:"flat",note:"c"},y={acc:"flat",note:"F"},w={"C#":[s,o,c,l,h,u,d],"A#m":[s,o,c,l,h,u,d],"G#Mix":[s,o,c,l,h,u,d],"D#Dor":[s,o,c,l,h,u,d],"E#Phr":[s,o,c,l,h,u,d],"F#Lyd":[s,o,c,l,h,u,d],"B#Loc":[s,o,c,l,h,u,d],"F#":[s,o,c,l,h,u],"D#m":[s,o,c,l,h,u],"C#Mix":[s,o,c,l,h,u],"G#Dor":[s,o,c,l,h,u],"A#Phr":[s,o,c,l,h,u],BLyd:[s,o,c,l,h,u],"E#Loc":[s,o,c,l,h,u],B:[s,o,c,l,h],"G#m":[s,o,c,l,h],"F#Mix":[s,o,c,l,h],"C#Dor":[s,o,c,l,h],"D#Phr":[s,o,c,l,h],ELyd:[s,o,c,l,h],"A#Loc":[s,o,c,l,h],E:[s,o,c,l],"C#m":[s,o,c,l],BMix:[s,o,c,l],"F#Dor":[s,o,c,l],"G#Phr":[s,o,c,l],ALyd:[s,o,c,l],"D#Loc":[s,o,c,l],A:[s,o,c],"F#m":[s,o,c],EMix:[s,o,c],BDor:[s,o,c],"C#Phr":[s,o,c],DLyd:[s,o,c],"G#Loc":[s,o,c],D:[s,o],Bm:[s,o],AMix:[s,o],EDor:[s,o],"F#Phr":[s,o],GLyd:[s,o],"C#Loc":[s,o],G:[s],Em:[s],DMix:[s],ADor:[s],BPhr:[s],CLyd:[s],"F#Loc":[s],C:[],Am:[],GMix:[],DDor:[],EPhr:[],FLyd:[],BLoc:[],F:[p],Dm:[p],CMix:[p],GDor:[p],APhr:[p],BbLyd:[p],ELoc:[p],Bb:[p,f],Gm:[p,f],FMix:[p,f],CDor:[p,f],DPhr:[p,f],EbLyd:[p,f],ALoc:[p,f],Eb:[p,f,m],Cm:[p,f,m],BbMix:[p,f,m],FDor:[p,f,m],GPhr:[p,f,m],AbLyd:[p,f,m],DLoc:[p,f,m],Ab:[p,f,m,g],Fm:[p,f,m,g],EbMix:[p,f,m,g],BbDor:[p,f,m,g],CPhr:[p,f,m,g],DbLyd:[p,f,m,g],GLoc:[p,f,m,g],Db:[p,f,m,g,v],Bbm:[p,f,m,g,v],AbMix:[p,f,m,g,v],EbDor:[p,f,m,g,v],FPhr:[p,f,m,g,v],GbLyd:[p,f,m,g,v],CLoc:[p,f,m,g,v],Gb:[p,f,m,g,v,b],Ebm:[p,f,m,g,v,b],DbMix:[p,f,m,g,v,b],AbDor:[p,f,m,g,v,b],BbPhr:[p,f,m,g,v,b],CbLyd:[p,f,m,g,v,b],FLoc:[p,f,m,g,v,b],Cb:[p,f,m,g,v,b,y],Abm:[p,f,m,g,v,b,y],GbMix:[p,f,m,g,v,b,y],DbDor:[p,f,m,g,v,b,y],EbPhr:[p,f,m,g,v,b,y],FbLyd:[p,f,m,g,v,b,y],BbLoc:[p,f,m,g,v,b,y],"A#":[p,f],"B#":[],"D#":[p,f,m],"E#":[p],"G#":[p,f,m,g],Gbm:[s,o,c,l,h,u,d]};return a.keySignature(i,w,e,t,r,n)};var c={treble:{clef:"treble",pitch:4,mid:0},"treble+8":{clef:"treble+8",pitch:4,mid:0},"treble-8":{clef:"treble-8",pitch:4,mid:0},"treble^8":{clef:"treble+8",pitch:4,mid:0},treble_8:{clef:"treble-8",pitch:4,mid:0},treble1:{clef:"treble",pitch:2,mid:2},treble2:{clef:"treble",pitch:4,mid:0},treble3:{clef:"treble",pitch:6,mid:-2},treble4:{clef:"treble",pitch:8,mid:-4},treble5:{clef:"treble",pitch:10,mid:-6},perc:{clef:"perc",pitch:6,mid:0},none:{clef:"none",mid:0},bass:{clef:"bass",pitch:8,mid:-12},"bass+8":{clef:"bass+8",pitch:8,mid:-12},"bass-8":{clef:"bass-8",pitch:8,mid:-12},"bass^8":{clef:"bass+8",pitch:8,mid:-12},bass_8:{clef:"bass-8",pitch:8,mid:-12},"bass+16":{clef:"bass",pitch:8,mid:-12},"bass-16":{clef:"bass",pitch:8,mid:-12},"bass^16":{clef:"bass",pitch:8,mid:-12},bass_16:{clef:"bass",pitch:8,mid:-12},bass1:{clef:"bass",pitch:2,mid:-6},bass2:{clef:"bass",pitch:4,mid:-8},bass3:{clef:"bass",pitch:6,mid:-10},bass4:{clef:"bass",pitch:8,mid:-12},bass5:{clef:"bass",pitch:10,mid:-14},tenor:{clef:"alto",pitch:8,mid:-8},tenor1:{clef:"alto",pitch:2,mid:-2},tenor2:{clef:"alto",pitch:4,mid:-4},tenor3:{clef:"alto",pitch:6,mid:-6},tenor4:{clef:"alto",pitch:8,mid:-8},tenor5:{clef:"alto",pitch:10,mid:-10},alto:{clef:"alto",pitch:6,mid:-6},alto1:{clef:"alto",pitch:2,mid:-2},alto2:{clef:"alto",pitch:4,mid:-4},alto3:{clef:"alto",pitch:6,mid:-6},alto4:{clef:"alto",pitch:8,mid:-8},alto5:{clef:"alto",pitch:10,mid:-10},"alto+8":{clef:"alto+8",pitch:6,mid:-6},"alto-8":{clef:"alto-8",pitch:6,mid:-6},"alto^8":{clef:"alto+8",pitch:6,mid:-6},alto_8:{clef:"alto-8",pitch:6,mid:-6}},l=function(e,t){var i=c[e];return(i?i.mid:0)+t};s.fixClef=function(e){var t=c[e.type];t&&(e.clefPos=t.pitch,e.type=t.clef)},s.deepCopyKey=function(e){var t={accidentals:[],root:e.root,acc:e.acc,mode:e.mode};return r.each(e.accidentals,(function(e){t.accidentals.push(r.clone(e))})),t};var h={A:5,B:6,C:0,D:1,E:2,F:3,G:4,a:12,b:13,c:7,d:8,e:9,f:10,g:11};s.addPosToKey=function(e,t){var i=e.verticalPos;r.each(t.accidentals,(function(e){var t=h[e.note];t-=i,e.verticalPos=t})),t.impliedNaturals&&r.each(t.impliedNaturals,(function(e){var t=h[e.note];t-=i,e.verticalPos=t})),i<-10?(r.each(t.accidentals,(function(e){e.verticalPos-=7,(e.verticalPos>=11||10===e.verticalPos&&"flat"===e.acc)&&(e.verticalPos-=7),"A"===e.note&&"sharp"===e.acc&&(e.verticalPos-=7),"G"!==e.note&&"F"!==e.note||"flat"!==e.acc||(e.verticalPos-=7)})),t.impliedNaturals&&r.each(t.impliedNaturals,(function(e){e.verticalPos-=7,(e.verticalPos>=11||10===e.verticalPos&&"flat"===e.acc)&&(e.verticalPos-=7),"A"===e.note&&"sharp"===e.acc&&(e.verticalPos-=7),"G"!==e.note&&"F"!==e.note||"flat"!==e.acc||(e.verticalPos-=7)}))):i<-4?(r.each(t.accidentals,(function(e){e.verticalPos-=7,-8!==i||"f"!==e.note&&"g"!==e.note||"sharp"!==e.acc||(e.verticalPos-=7)})),t.impliedNaturals&&r.each(t.impliedNaturals,(function(e){e.verticalPos-=7,-8!==i||"f"!==e.note&&"g"!==e.note||"sharp"!==e.acc||(e.verticalPos-=7)}))):i>=7&&(r.each(t.accidentals,(function(e){e.verticalPos+=7})),t.impliedNaturals&&r.each(t.impliedNaturals,(function(e){e.verticalPos+=7})))},s.fixKey=function(e,t){var i=r.clone(t);return s.addPosToKey(e,i),i};var u=function(e){var t=0,i=e.charAt(t++);"^"!==i&&"_"!==i||(i=e.charAt(t++));var r=h[i];for(void 0===r&&(r=6);t<e.length;t++)if(","===e.charAt(t))r-=7;else{if("'"!==e.charAt(t))break;r+=7}return{mid:r-6,str:e.substring(t)}};s.parseKey=function(r){0===r.length&&(r="none");var a=e.tokenize(r,0,r.length),o={};switch(a[0].token){case"HP":n.addDirective("bagpipes"),i.key={root:"HP",accidentals:[],acc:"",mode:""},o.foundKey=!0,a.shift();break;case"Hp":n.addDirective("bagpipes"),i.key={root:"Hp",accidentals:[{acc:"natural",note:"g"},{acc:"sharp",note:"f"},{acc:"sharp",note:"c"}],acc:"",mode:""},o.foundKey=!0,a.shift();break;case"none":i.key={root:"none",accidentals:[],acc:"",mode:""},o.foundKey=!0,a.shift();break;default:var c=e.getKeyPitch(a[0].token);if(c.len>0){o.foundKey=!0;var h="",u="";a[0].token.length>1?a[0].token=a[0].token.substring(1):a.shift();var d=c.token;if(a.length>0){var p=e.getSharpFlat(a[0].token);if(p.len>0&&(a[0].token.length>1?a[0].token=a[0].token.substring(1):a.shift(),d+=p.token,h=p.token),a.length>0){var f=e.getMode(a[0].token);f.len>0&&(a.shift(),d+=f.token,u=f.token)}if(void 0===s.standardKey(d,c.token,h,0))return t("Unsupported key signature: "+d,r,0),o}var m=s.deepCopyKey(i.key),g=i.globalTranspose?-i.globalTranspose:0;if(i.key=s.deepCopyKey(s.standardKey(d,c.token,h,g)),i.key.mode=u,m){for(var v,b=0;b<i.key.accidentals.length;b++)for(v=0;v<m.accidentals.length;v++)m.accidentals[v].note&&i.key.accidentals[b].note.toLowerCase()===m.accidentals[v].note.toLowerCase()&&(m.accidentals[v].note=null);for(v=0;v<m.accidentals.length;v++)m.accidentals[v].note&&(i.key.impliedNaturals||(i.key.impliedNaturals=[]),i.key.impliedNaturals.push({acc:"natural",note:m.accidentals[v].note}))}}}if(0===a.length)return o;if("exp"===a[0].token&&a.shift(),0===a.length)return o;if("oct"===a[0].token&&a.shift(),0===a.length)return o;var y,w=e.getKeyAccidentals2(a);if(w.warn&&t(w.warn,r,0),w.accs){o.foundKey||(o.foundKey=!0,i.key={root:"none",acc:"",mode:"",accidentals:[]}),function(e){for(var t=0;t<e.length;t++)"b"===e[t].note?e[t].note="B":"a"===e[t].note?e[t].note="A":"F"===e[t].note?e[t].note="f":"E"===e[t].note?e[t].note="e":"D"===e[t].note?e[t].note="d":"C"===e[t].note?e[t].note="c":"G"===e[t].note&&"sharp"===e[t].acc?e[t].note="g":"g"===e[t].note&&"flat"===e[t].acc&&(e[t].note="G")}(w.accs);for(var k=0;k<w.accs.length;k++){for(var x=!1,S=0;S<i.key.accidentals.length&&!x;S++)i.key.accidentals[S].note===w.accs[k].note&&(x=!0,i.key.accidentals[S].acc!==w.accs[k].acc&&(i.key.accidentals[S].acc=w.accs[k].acc,i.key.explicitAccidentals||(i.key.explicitAccidentals=[]),i.key.explicitAccidentals.push(w.accs[k])));if(!x&&(i.key.explicitAccidentals||(i.key.explicitAccidentals=[]),i.key.explicitAccidentals.push(w.accs[k]),i.key.accidentals.push(w.accs[k]),i.key.impliedNaturals))for(var _=0;_<i.key.impliedNaturals.length;_++)i.key.impliedNaturals[_].note===w.accs[k].note&&i.key.impliedNaturals.splice(_,1)}}for(;a.length>0;)switch(a[0].token){case"m":case"middle":if(a.shift(),0===a.length)return t("Expected = after middle",r,0),o;if("="!==(y=a.shift()).token){t("Expected = after middle",r,y.start);break}if(0===a.length)return t("Expected parameter after middle=",r,0),o;var T=e.getPitchFromTokens(a);T.warn&&t(T.warn,r,0),T.position&&(i.clef.verticalPos=T.position-6);break;case"transpose":if(a.shift(),0===a.length)return t("Expected = after transpose",r,0),o;if("="!==(y=a.shift()).token){t("Expected = after transpose",r,y.start);break}if(0===a.length)return t("Expected parameter after transpose=",r,0),o;if("number"!==a[0].type){t("Expected number after transpose",r,a[0].start);break}i.clef.transpose=a[0].intt,a.shift();break;case"stafflines":if(a.shift(),0===a.length)return t("Expected = after stafflines",r,0),o;if("="!==(y=a.shift()).token){t("Expected = after stafflines",r,y.start);break}if(0===a.length)return t("Expected parameter after stafflines=",r,0),o;if("number"!==a[0].type){t("Expected number after stafflines",r,a[0].start);break}i.clef.stafflines=a[0].intt,a.shift();break;case"staffscale":if(a.shift(),0===a.length)return t("Expected = after staffscale",r,0),o;if("="!==(y=a.shift()).token){t("Expected = after staffscale",r,y.start);break}if(0===a.length)return t("Expected parameter after staffscale=",r,0),o;if("number"!==a[0].type){t("Expected number after staffscale",r,a[0].start);break}i.clef.staffscale=a[0].floatt,a.shift();break;case"style":if(a.shift(),0===a.length)return t("Expected = after style",r,0),o;if("="!==(y=a.shift()).token){t("Expected = after style",r,y.start);break}if(0===a.length)return t("Expected parameter after style=",r,0),o;switch(a[0].token){case"normal":case"harmonic":case"rhythm":case"x":i.style=a[0].token,a.shift();break;default:t("error parsing style element: "+a[0].token,r,a[0].start)}break;case"clef":if(a.shift(),0===a.length)return t("Expected = after clef",r,0),o;if("="!==(y=a.shift()).token){t("Expected = after clef",r,y.start);break}if(0===a.length)return t("Expected parameter after clef=",r,0),o;case"treble":case"bass":case"alto":case"tenor":case"perc":var A=a.shift();switch(A.token){case"treble":case"tenor":case"alto":case"bass":case"perc":case"none":break;case"C":A.token="alto";break;case"F":A.token="bass";break;case"G":A.token="treble";break;case"c":A.token="alto";break;case"f":A.token="bass";break;case"g":A.token="treble";break;default:t("Expected clef name. Found "+A.token,r,A.start)}a.length>0&&"number"===a[0].type&&(A.token+=a[0].token,a.shift()),a.length>1&&("-"===a[0].token||"+"===a[0].token||"^"===a[0].token||"_"===a[0].token)&&"8"===a[1].token&&(A.token+=a[0].token+a[1].token,a.shift(),a.shift()),i.clef={type:A.token,verticalPos:l(A.token,0)},i.currentVoice&&void 0!==i.currentVoice.transpose&&(i.clef.transpose=i.currentVoice.transpose),o.foundClef=!0;break;default:t("Unknown parameter: "+a[0].token,r,a[0].start),a.shift()}return o};s.parseVoice=function(r,n,a){var s=e.getMeat(r,n,a),c=s.start,h=s.end,d=e.getToken(r,c,h);if(0!==d.length){var p=!1;void 0===i.voices[d]&&(i.voices[d]={},p=!0,i.score_is_present&&t("Can't have an unknown V: id when the %score directive is present",r,c)),c+=d.length,c+=e.eatWhiteSpace(r,c);for(var f={startStaff:p},m=function(i){var n=e.getVoiceToken(r,c,h);void 0!==n.warn?t("Expected value for "+i+" in voice: "+n.warn,r,c):0===n.token.length&&'"'!==r.charAt(c)?t("Expected value for "+i+" in voice",r,c):f[i]=n.token,c+=n.len},g=function(n,a,s){var o=e.getVoiceToken(r,c,h);void 0!==o.warn?t("Expected value for "+a+" in voice: "+o.warn,r,c):0===o.token.length&&'"'!==r.charAt(c)?t("Expected value for "+a+" in voice",r,c):("number"===s&&(o.token=parseFloat(o.token)),i.voices[n][a]=o.token),c+=o.len},v=function(i,n){var a=e.getVoiceToken(r,c,h);if(void 0!==a.warn)t("Expected value for "+i+" in voice: "+a.warn,r,c);else{if(0!==a.token.length||'"'===r.charAt(c))return"number"===n&&(a.token=parseFloat(a.token)),a.token;t("Expected value for "+i+" in voice",r,c)}c+=a.len},b=function(n,a){var s=e.getVoiceToken(r,c,h);if(void 0!==s.warn)t("Expected one of (_B, _E, _b, _e) for "+a+" in voice: "+s.warn,r,c);else if(0===s.token.length&&'"'!==r.charAt(c))t("Expected one of (_B, _E, _b, _e) for "+a+" in voice",r,c);else{var o={_B:2,_E:9,_b:-10,_e:-3}[s.token];o?i.voices[n][a]=o:t("Expected one of (_B, _E, _b, _e) for "+a+" in voice",r,c)}c+=s.len};c<h;){var y=e.getVoiceToken(r,c,h);if(c+=y.len,y.warn)t("Error parsing voice: "+y.warn,r,c);else{var w=null;switch(y.token){case"clef":case"cl":m("clef");var k=0;void 0!==f.clef&&(f.clef=f.clef.replace(/[',]/g,""),-1!==f.clef.indexOf("+16")&&(k+=14,f.clef=f.clef.replace("+16","")),f.verticalPos=l(f.clef,k));break;case"treble":case"bass":case"tenor":case"alto":case"perc":case"none":case"treble'":case"bass'":case"tenor'":case"alto'":case"none'":case"treble''":case"bass''":case"tenor''":case"alto''":case"none''":case"treble,":case"bass,":case"tenor,":case"alto,":case"none,":case"treble,,":case"bass,,":case"tenor,,":case"alto,,":case"none,,":f.clef=y.token.replace(/[',]/g,""),f.verticalPos=l(f.clef,0),i.voices[d].clef=y.token;break;case"staves":case"stave":case"stv":m("staves");break;case"brace":case"brc":m("brace");break;case"bracket":case"brk":m("bracket");break;case"name":case"nm":m("name");break;case"subname":case"sname":case"snm":m("subname");break;case"merge":f.startStaff=!1;break;case"stem":case"stems":void 0!==(w=e.getVoiceToken(r,c,h)).warn?t("Expected value for stems in voice: "+w.warn,r,c):"up"===w.token||"down"===w.token?i.voices[d].stem=w.token:t("Expected up or down for voice stem",r,c),c+=w.len;break;case"up":case"down":i.voices[d].stem=y.token;break;case"middle":case"m":m("verticalPos"),f.verticalPos=u(f.verticalPos).mid;break;case"gchords":case"gch":i.voices[d].suppressChords=!0,"0"===(w=e.getVoiceToken(r,c,h)).token&&(c+=w.len);break;case"space":case"spc":m("spacing");break;case"scale":g(d,"scale","number");break;case"score":b(d,"scoreTranspose");break;case"transpose":g(d,"transpose","number");break;case"stafflines":g(d,"stafflines","number");break;case"staffscale":g(d,"staffscale","number");break;case"octave":g(d,"octave","number");break;case"volume":g(d,"volume","number");break;case"cue":var x=v("cue","string");i.voices[d].scale="on"===x?.6:1;break;case"style":void 0!==(w=e.getVoiceToken(r,c,h)).warn?t("Expected value for style in voice: "+w.warn,r,c):"normal"===w.token||"harmonic"===w.token||"rhythm"===w.token||"x"===w.token?i.voices[d].style=w.token:t("Expected one of [normal, harmonic, rhythm, x] for voice style",r,c),c+=w.len}}c+=e.eatWhiteSpace(r,c)}if((f.startStaff||0===i.staves.length)&&(i.staves.push({index:i.staves.length,meter:i.origMeter}),i.score_is_present||(i.staves[i.staves.length-1].numVoices=0)),void 0===i.voices[d].staffNum){i.voices[d].staffNum=i.staves.length-1;var S=0;for(var _ in i.voices)i.voices.hasOwnProperty(_)&&i.voices[_].staffNum===i.voices[d].staffNum&&S++;i.voices[d].index=S-1}var T=i.staves[i.voices[d].staffNum];i.score_is_present||T.numVoices++,f.clef&&(T.clef={type:f.clef,verticalPos:f.verticalPos}),f.spacing&&(T.spacing_below_offset=f.spacing),f.verticalPos&&(T.verticalPos=f.verticalPos),f.name&&(T.name?T.name.push(f.name):T.name=[f.name]),f.subname&&(T.subname?T.subname.push(f.subname):T.subname=[f.subname]),function(e){i.currentVoice=i.voices[e],o.setCurrentVoice(i.currentVoice.staffNum,i.currentVoice.index)}(d)}else t("Expected a voice id",r,c)}}(),e.exports=s},function(e,t,i){var r=i(50),n=i(21),a=i(51),s=i(15),o=i(6),c=i(7),l=i(22),h=i(14),u=i(52),d=i(53),p=i(54),f="MIDI is not supported in this browser.",m="https://paulrosen.github.io/midi-js-soundfonts/FluidR3_GM/";e.exports=function(){var e=this;e.audioBufferPossible=void 0,e.directSource=[],e.startTimeSec=void 0,e.pausedTimeSec=void 0,e.audioBuffers=[],e.duration=void 0,e.isRunning=!1,e.init=function(t){t||(t={}),s(t.audioContext);var i=o().currentTime;if(e.debugCallback=t.debugCallback,e.debugCallback&&e.debugCallback("init called"),e.audioBufferPossible=e._deviceCapable(),!e.audioBufferPossible)return Promise.reject({status:"NotSupported",message:f});e.soundFontUrl=t.soundFontUrl?t.soundFontUrl:m,e.millisecondsPerMeasure=t.millisecondsPerMeasure?t.millisecondsPerMeasure:t.visualObj?t.visualObj.millisecondsPerMeasure():1e3;var r=t.options?t.options:{};if(e.meterSize=1,t.visualObj){var n=d(t.visualObj,r);e.flattened=p(n,r),e.meterSize=t.visualObj.getMeterFraction().num/t.visualObj.getMeterFraction().den}else{if(!t.sequence)return Promise.reject(new Error("Must pass in either a visualObj or a sequence"));e.flattened=t.sequence}e.sequenceCallback=r.sequenceCallback;var a={},c=h[0];e.flattened.tracks.forEach((function(e){e.forEach((function(e){"program"===e.cmd&&h[e.instrument]&&(c=h[e.instrument]);var t=e.pitch+60;void 0!==e.pitch&&(l[t]?(a[c]||(a[c]={}),a[c][l[t]]=!0):console.log("Can't find note: ",t))}))})),e.debugCallback&&e.debugCallback("note gathering time = "+Math.floor(1e3*(o().currentTime-i))+"ms"),i=o().currentTime;var u=[];Object.keys(a).forEach((function(e){Object.keys(a[e]).forEach((function(t){u.push({instrument:e,note:t})}))}));for(var g=[],v=0;v<u.length;v+=256)g.push(u.slice(v,v+256));return new Promise((function(t,r){var n=[],a=0;!function s(){a<g.length?e._loadBatch(g[a],e.soundFontUrl,i).then((function(e){i=o().currentTime,n.push(e),a++,s()}),r):t(n)}()}))},e._loadBatch=function(t,i,n){var a=[];return t.forEach((function(e){a.push(r(i,e.instrument,e.note,o()))})),Promise.all(a).then((function(t){return e.debugCallback&&e.debugCallback("mp3 load time = "+Math.floor(1e3*(o().currentTime-n))+"ms"),Promise.resolve(t)}))},e.prime=function(){return e.isRunning=!1,e.audioBufferPossible?(e.debugCallback&&e.debugCallback("prime called"),new Promise((function(t){var i=o().currentTime,r=e.millisecondsPerMeasure/1e3/e.meterSize;e.duration=e.flattened.totalDuration*r;var s=Math.floor(o().sampleRate*e.duration);e.stop();var c=a(e.flattened);e.sequenceCallback&&e.sequenceCallback(c),e.audioBuffers=[],c.forEach((function(t){var i=o().createBuffer(1,s,o().sampleRate),a=i.getChannelData(0);t.forEach((function(t){e._placeNote(a,t,r,n)})),e.audioBuffers.push(i)})),e.debugCallback&&(e.debugCallback("sampleRate = "+o().sampleRate),e.debugCallback("totalSamples = "+s),e.debugCallback("creationTime = "+Math.floor(1e3*(o().currentTime-i))+"ms")),t({status:"ok",seconds:0})}))):Promise.reject(new Error(f))},e.start=function(){if(e.pausedTimeSec)e.resume();else{if(!e.audioBufferPossible)throw new Error(f);e.debugCallback&&e.debugCallback("start called"),e._kickOffSound(0),e.startTimeSec=o().currentTime,e.pausedTimeSec=void 0,e.debugCallback&&e.debugCallback("MIDI STARTED",e.startTimeSec)}},e.pause=function(){if(!e.audioBufferPossible)throw new Error(f);e.debugCallback&&e.debugCallback("pause called"),e.pausedTimeSec||(e.stop(),e.pausedTimeSec=o().currentTime)},e.resume=function(){if(!e.audioBufferPossible)throw new Error(f);e.debugCallback&&e.debugCallback("resume called");var t=e.pausedTimeSec-e.startTimeSec;e.startTimeSec=o().currentTime-t,e.pausedTimeSec=void 0,e._kickOffSound(t)},e.seek=function(t){var i=e.duration*t;if(!e.audioBufferPossible)throw new Error(f);e.debugCallback&&e.debugCallback("seek called sec="+i),e.isRunning&&(e.stop(),e._kickOffSound(i));var r=e.pausedTimeSec?e.pausedTimeSec-e.startTimeSec:void 0;e.startTimeSec=o().currentTime-i,e.pausedTimeSec&&(e.pausedTimeSec=e.startTimeSec+r)},e.stop=function(){e.isRunning=!1,e.pausedTimeSec=void 0,e.directSource.forEach((function(e){try{e.stop()}catch(e){console.log("direct source didn't stop:",e)}})),e.directSource=[]},e.download=function(){return u(e)},e._deviceCapable=function(){return!!c()||(console.warn(f),e.debugCallback&&e.debugCallback(f),!1)},e._kickOffSound=function(t){e.isRunning=!0,e.directSource=[],e.audioBuffers.forEach((function(t,i){e.directSource[i]=o().createBufferSource(),e.directSource[i].buffer=t,e.directSource[i].connect(o().destination)})),e.directSource.forEach((function(e){e.start(0,t)}))},e._placeNote=function(e,t,i,r){var n=Math.floor(t.start*o().sampleRate*i),a=(t.end-t.start)*i,s=l[t.pitch+60];if(s)for(var c=r[t.instrument][s].getChannelData(0),h=Math.min(c.length,Math.floor(a*o().sampleRate)),u=0;u<h;u++){var d=c[u]*t.volume/128;e[n+u]?e[n+u]=.75*(e[n+u]+d):e[n+u]=d}}}},function(e,t){e.exports=["acoustic_grand_piano","bright_acoustic_piano","electric_grand_piano","honkytonk_piano","electric_piano_1","electric_piano_2","harpsichord","clavinet","celesta","glockenspiel","music_box","vibraphone","marimba","xylophone","tubular_bells","dulcimer","drawbar_organ","percussive_organ","rock_organ","church_organ","reed_organ","accordion","harmonica","tango_accordion","acoustic_guitar_nylon","acoustic_guitar_steel","electric_guitar_jazz","electric_guitar_clean","electric_guitar_muted","overdriven_guitar","distortion_guitar","guitar_harmonics","acoustic_bass","electric_bass_finger","electric_bass_pick","fretless_bass","slap_bass_1","slap_bass_2","synth_bass_1","synth_bass_2","violin","viola","cello","contrabass","tremolo_strings","pizzicato_strings","orchestral_harp","timpani","string_ensemble_1","string_ensemble_2","synth_strings_1","synth_strings_2","choir_aahs","voice_oohs","synth_choir","orchestra_hit","trumpet","trombone","tuba","muted_trumpet","french_horn","brass_section","synth_brass_1","synth_brass_2","soprano_sax","alto_sax","tenor_sax","baritone_sax","oboe","english_horn","bassoon","clarinet","piccolo","flute","recorder","pan_flute","blown_bottle","shakuhachi","whistle","ocarina","lead_1_square","lead_2_sawtooth","lead_3_calliope","lead_4_chiff","lead_5_charang","lead_6_voice","lead_7_fifths","lead_8_bass__lead","pad_1_new_age","pad_2_warm","pad_3_polysynth","pad_4_choir","pad_5_bowed","pad_6_metallic","pad_7_halo","pad_8_sweep","fx_1_rain","fx_2_soundtrack","fx_3_crystal","fx_4_atmosphere","fx_5_brightness","fx_6_goblins","fx_7_echoes","fx_8_scifi","sitar","banjo","shamisen","koto","kalimba","bagpipe","fiddle","shanai","tinkle_bell","agogo","steel_drums","woodblock","taiko_drum","melodic_tom","synth_drum","reverse_cymbal","guitar_fret_noise","breath_noise","seashore","bird_tweet","telephone_ring","helicopter","applause","gunshot","percussion"]},function(e,t){e.exports=function(e){return window.abcjsAudioContext||(e||(e=new(e=window.AudioContext||window.webkitAudioContext||navigator.mozAudioContext||navigator.msAudioContext)),window.abcjsAudioContext=e),"suspended"!==window.abcjsAudioContext.state}},function(e,t){var i={},r={C:0,"C#":1,Db:1,D:2,"D#":3,Eb:3,E:4,F:5,"F#":6,Gb:6,G:7,"G#":8,Ab:8,A:9,"A#":10,Bb:10,B:11},n=["C","Db","D","Eb","E","F","F#","G","Ab","A","Bb","B"],a=["C","C#","D","D#","E","F","F#","G","G#","A","Bb","B"];i.keySignature=function(e,t,i,s,o,c){c||(c=0),e.localTransposeVerticalMovement=0,e.localTransposePreferFlats=!1;var l=t[i];if(!l)return e.key;if(e.localTranspose=(e.globalTranspose?e.globalTranspose:0)+c,!e.localTranspose)return{accidentals:l,root:s,acc:o};if(e.globalTransposeOrigKeySig=l,e.localTranspose%12==0)return e.localTransposeVerticalMovement=e.localTranspose/12*7,{accidentals:l,root:s,acc:o};var h=i[0];"b"===i[1]||"#"===i[1]?(h+=i[1],i=i.substr(2)):i=i.substr(1);for(var u=r[h]+e.localTranspose;u<0;)u+=12;u>11&&(u%=12);var d="m"===i[0]?a[u]:n[u],p=d+i,f=t[p];f.length>0&&"flat"===f[0].acc&&(e.localTransposePreferFlats=!0);var m=p.charCodeAt(0)-h.charCodeAt(0);return e.localTranspose>0?m<0?m+=7:0===m&&("#"!==h[1]&&"b"!==p[1]||(m+=7)):e.localTranspose<0&&(m>0?m-=7:0===m&&("b"!==h[1]&&"#"!==p[1]||(m-=7))),e.localTranspose>0?e.localTransposeVerticalMovement=m+7*Math.floor(e.localTranspose/12):e.localTransposeVerticalMovement=m+7*Math.ceil(e.localTranspose/12),{accidentals:f,root:d[0],acc:d.length>1?d[1]:""}};var s=["C","C♯","D","D♯","E","F","F♯","G","G♯","A","A♯","B"],o=["C","D♭","D","E♭","E","F","G♭","G","A♭","A","B♭","B"],c=["C","C#","D","D#","E","F","F#","G","G#","A","A#","B"],l=["C","Db","D","Eb","E","F","Gb","G","Ab","A","Bb","B"];i.chordName=function(e,t){if(e.localTranspose&&e.localTranspose%12!=0){for(var i=e.localTranspose;i<0;)i+=12;i>11&&(i%=12);for(var r=(t=(t=(t=(t=(t=(t=(t=(t=e.freegchord?(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace(/Cb/g,"`~11`")).replace(/Db/g,"`~1`")).replace(/Eb/g,"`~3`")).replace(/Fb/g,"`~4`")).replace(/Gb/g,"`~6`")).replace(/Ab/g,"`~8`")).replace(/Bb/g,"`~10`")).replace(/C#/g,"`~1`")).replace(/D#/g,"`~3`")).replace(/E#/g,"`~5`")).replace(/F#/g,"`~6`")).replace(/G#/g,"`~8`")).replace(/A#/g,"`~10`")).replace(/B#/g,"`~0`"):(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace(/C♭/g,"`~11`")).replace(/D♭/g,"`~1`")).replace(/E♭/g,"`~3`")).replace(/F♭/g,"`~4`")).replace(/G♭/g,"`~6`")).replace(/A♭/g,"`~8`")).replace(/B♭/g,"`~10`")).replace(/C♯/g,"`~1`")).replace(/D♯/g,"`~3`")).replace(/E♯/g,"`~5`")).replace(/F♯/g,"`~6`")).replace(/G♯/g,"`~8`")).replace(/A♯/g,"`~10`")).replace(/B♯/g,"`~0`")).replace(/C/g,"`~0`")).replace(/D/g,"`~2`")).replace(/E/g,"`~4`")).replace(/F/g,"`~5`")).replace(/G/g,"`~7`")).replace(/A/g,"`~9`")).replace(/B/g,"`~11`")).split("`"),n=0;n<r.length;n++)if("~"===r[n][0]){var a=parseInt(r[n].substr(1),10);(a+=i)>11&&(a-=12),e.freegchord?r[n]=e.localTransposePreferFlats?l[a]:c[a]:r[n]=e.localTransposePreferFlats?o[a]:s[a]}t=r.join("")}return t};var h=["c","d","e","f","g","a","b"];var u={dblflat:-2,flat:-1,natural:0,sharp:1,dblsharp:2},d={"-2":"dblflat","-1":"flat",0:"natural",1:"sharp",2:"dblsharp"};i.note=function(e,t){if(e.localTranspose){var i=t.pitch;if(t.pitch=t.pitch+e.localTransposeVerticalMovement,t.accidental){var r=function(e,t,i,r,n){for(var a=h[(e+49)%7],s=0,o=0;o<r.length;o++)r[o].note.toLowerCase()===a&&(s=u[r[o].acc]);for(var c=u[i]-s,l=h[(t+49)%7],d=0,p=0;p<n.accidentals.length;p++)n.accidentals[p].note.toLowerCase()===l&&(d=u[n.accidentals[p].acc]);var f=c+d;return f<-2&&(t--,f+="c"===l||"f"===l?1:2),f>2&&(t++,f-="b"===l||"e"===l?1:2),[t,f]}(i,t.pitch,t.accidental,e.globalTransposeOrigKeySig,e.targetKey);t.pitch=r[0],t.accidental=d[r[1]]}}},e.exports=i},function(e,t){function i(e){for(var t=[],i=0;i<e.length;i++)t.push(e[i]);return t}function r(e,t,r,n,a,s,o,c,l,h,u){for(var d=h;d<e.length;d++){var p=e[d];r+=p,n+=p;var f=Math.abs(r-t[c]);if(Math.abs(f-s)<t[0]/10)if(f<s){var m=i(a),g=i(l);g.push(d-1),m.push(n-p),u.push({accumulator:r,lineAccumulator:p,lineWidths:m,lastVariance:Math.abs(r-t[c+1]),highestVariance:Math.max(o,s),currLine:c+1,lineBreaks:g,startIndex:d+1})}else f>s&&d<e.length-1&&(m=i(a),g=i(l),u.push({accumulator:r,lineAccumulator:n,lineWidths:m,lastVariance:f,highestVariance:Math.max(o,f),currLine:c,lineBreaks:g,startIndex:d+1}));f>s?(l.push(d-1),c++,o=Math.max(o,s),s=Math.abs(r-t[c]),a.push(n-p),n=p):s=f}a.push(n)}function n(e,t,i,r,n){var a=new n,s={lineBreaks:e,staffwidth:t};for(var o in r)r.hasOwnProperty(o)&&"wrap"!==o&&"staffwidth"!==o&&(s[o]=r[o]);return a.parse(i,s),{tune:a.getTune(),revisedParams:s}}e.exports={wrapLines:function(e,t){if(t&&0!==e.lines.length){for(var i=[],r=[],n=[],a=[],s=[],o="",c={},l=0;l<e.lines.length;l++){var h=e.lines[l];if(h.staff)for(var u=h.staff,d=0;d<u.length;d++){void 0===r[d]&&(r[d]=[],n[d]=[],a[d]=[],s[d]=[]);for(var p=u[d],f=p.voices,m=0;m<f.length;m++){void 0===r[d][m]&&(r[d][m]=!0,n[d][m]=0,a[d][m]=0,s[d][m]=0);for(var g=f[m],v=0;v<g.length;v++){if(r[d][m]){if(i[n[d][m]]||(i[n[d][m]]={staff:[]}),!i[n[d][m]].staff[d])for(var b in i[n[d][m]].staff[d]={voices:[]},p)p.hasOwnProperty(b)&&("meter"===b?1!==i.length&&o===JSON.stringify(p[b])||(o=JSON.stringify(p[b]),i[n[d][m]].staff[d][b]=p[b]):"voices"!==b&&(i[n[d][m]].staff[d][b]=p[b]));s[d][m]&&(i[n[d][m]].staff[d].barNumber=s[d][m]),r[d][m]=!1}var y=g[v];if(!i[n[d][m]].staff[d].voices[m])for(var w in i[n[d][m]].staff[d].voices[m]=[],c)c.hasOwnProperty(w)&&i[n[d][m]].staff[d].voices[m].push(c[w]);i[n[d][m]].staff[d].voices[m].push(y),"stem"===y.el_type&&(c[y.el_type]=y),"bar"===y.el_type&&(a[d][m]++,t[a[d][m]]&&(r[d][m]=!0,n[d][m]++,s[d][m]=y.barNumber,delete y.barNumber))}}}else i.push(h),n++}e.lines=i}},calcLineWraps:function(e,t,i,a,s,o){if(a.staffwidth<t.left)return{explanation:"Staffwidth is narrower than the margin",tune:e,revisedParams:a};var c=a.scale?Math.max(a.scale,.1):1,l=a.wrap.minSpacing?Math.max(parseFloat(a.wrap.minSpacing),1):1,h=a.wrap.minSpacingLimit?Math.max(parseFloat(a.wrap.minSpacingLimit),1):l-.1,u=a.wrap.maxSpacing?Math.max(parseFloat(a.wrap.maxSpacing),1):void 0;a.wrap.lastLineLimit&&!u&&(u=Math.max(parseFloat(a.wrap.lastLineLimit),1)),a.wrap.targetHeight&&Math.max(parseInt(a.wrap.targetHeight,10),100);var d=a.wrap.preferredMeasuresPerLine?Math.max(parseInt(a.wrap.preferredMeasuresPerLine,10),1):void 0,p=(a.staffwidth-t.left)/l/c,f=(a.staffwidth-t.left)/u/c,m=(a.staffwidth-t.left)/h/c,g={widths:t,lineBreakPoint:p,minLineSize:f,attempts:[],staffWidth:a.staffwidth,minWidth:Math.round(m)},v=null;if(d){var b=function(e,t,i){for(var r=[],n=[],a=0,s=!1,o=0;o<e.length;o++)(a+=e[o])>t&&(s=!0),o%i==i-1&&(o!==e.length-1&&r.push(o),n.push(Math.round(a)),a=0);return{failed:s,totals:n,lineBreaks:r}}(t.measureWidths,p,d);g.attempts.push({type:"Fixed Measures Per Line",preferredMeasuresPerLine:d,lineBreaks:b.lineBreaks,failed:b.failed,totals:b.totals}),b.failed||(v=b.lineBreaks)}if(!v){var y=function(e,t){for(var i=[],r=[],n=0,a=0;a<e.length;a++){var s=e[a],o=n+s;if(o<t)n=o;else t-n<o-t&&n>0?(i.push(a-1),r.push(Math.round(n-s)),n=s):a<e.length-1&&(i.push(a),r.push(Math.round(n)),n=0)}return r.push(Math.round(n)),{lineBreaks:i,totals:r}}(t.measureWidths,p);g.attempts.push({type:"Free Form",lineBreaks:y.lineBreaks,totals:y.totals}),v=y.lineBreaks,y=function(e,t,i,n){for(var a=Math.ceil(e.total/t)+1,s=Math.floor(e.total/a),o=[],c=0;c<a;c++)o.push(s*(c+1));var l=[];l.push({accumulator:0,lineAccumulator:0,lineWidths:[],lastVariance:999999,highestVariance:0,currLine:0,lineBreaks:[],startIndex:0});for(var h=0;h<l.length;)r(e.measureWidths,o,l[h].accumulator,l[h].lineAccumulator,l[h].lineWidths,l[h].lastVariance,l[h].highestVariance,l[h].currLine,l[h].lineBreaks,l[h].startIndex,l),h++;for(c=0;c<l.length;c++){var u=l[c];u.variances=[],u.aveVariance=0;for(var d=0;d<u.lineWidths.length;d++){var p=u.lineWidths[d];u.variances.push(p-o[0]),u.aveVariance+=Math.abs(p-o[0])}u.aveVariance=u.aveVariance/u.lineWidths.length,n.attempts.push({type:"optimizeLineWidths",lineBreaks:u.lineBreaks,variances:u.variances,aveVariance:u.aveVariance,widths:e.measureWidths})}var f=9999999,m=-1;for(c=0;c<l.length;c++)(u=l[c]).aveVariance<f&&(f=u.aveVariance,m=c);return{failed:!1,lineBreaks:l[m].lineBreaks,variance:l[m].highestVariance}}(t,p,0,g),g.attempts.push({type:"Optimize",failed:y.failed,reason:y.reason,lineBreaks:y.lineBreaks,totals:y.totals}),y.failed||(v=y.lineBreaks)}var w=a.staffwidth,k=n(v,w,i,a,s),x=o.getMeasureWidths(k.tune),S=!0;return g.attempts.push({type:"heightCheck",height:x.height}),0===v.length&&f>t.total&&(w=t.total*u*c+t.left,g.attempts.push({type:"too sparse",newWidth:Math.round(w)}),S=!1),S||(k=n(v,w,i,a,s)),k.explanation=g,k}}},function(e,t,i){var r=i(0),n=i(12),a=i(1);e.exports=function(){this.getBeatLength=function(){for(var e=0;e<this.lines.length;e++)if(this.lines[e].staff)for(var t=0;t<this.lines[e].staff.length;t++)if(this.lines[e].staff[t].meter){var i=this.lines[e].staff[t].meter;if("specified"===i.type){if(i.value.length>0){var r=parseInt(i.value[0].num,10),n=parseInt(i.value[0].den,10);return 3===r&&8===n?3/8:6===r&&8===n?3/8:6===r&&4===n?.75:9===r&&8===n?3/8:12===r&&8===n?3/8:1/n}return.25}return"cut_time"===i.type?.5:.25}return.25},this.getPickupLength=function(){for(var e=0,t=this.getBarLength(),i=0;i<this.lines.length;i++)if(this.lines[i].staff)for(var r=0;r<this.lines[i].staff.length;r++)for(var n=0;n<this.lines[i].staff[r].voices.length;n++)for(var a=this.lines[i].staff[r].voices[n],s=1,o=0;o<a.length;o++){var c=a[o].rest&&"spacer"===a[o].rest.type;if(a[o].startTriplet&&(s=a[o].tripletMultiplier),a[o].duration&&!c&&(e+=a[o].duration*s),a[o].endTriplet&&(s=1),e>=t&&(e-=t),"bar"===a[o].el_type)return e}return e},this.getBarLength=function(){var e=this.getMeterFraction();return e.num/e.den},this.millisecondsPerMeasure=function(e){var t;if(e)t=e;else{var i=this.metaText?this.metaText.tempo:null;t=this.getBpm(i)}return t<=0&&(t=1),6e4*(this.getBeatsPerMeasure()/t)},this.getBeatsPerMeasure=function(){var e,t=this.getMeterFraction();return(e=8===t.den?t.num/3:t.num)<=0&&(e=1),e},this.reset=function(){this.version="1.0.1",this.media="screen",this.metaText={},this.formatting={},this.lines=[],this.staffNum=0,this.voiceNum=0,this.lineNum=0},this.resolveOverlays=function(){for(var e=!1,t=0;t<this.lines.length;t++){var i=this.lines[t];if(i.staff)for(var n=0;n<i.staff.length;n++){for(var a=i.staff[n],s=[],o=0;o<a.voices.length;o++){var c=a.voices[o];s.push({hasOverlay:!1,voice:[],snip:[]});for(var l=0,h=!1,u=-1,d=0;d<c.length;d++){var p=c[d];"overlay"!==p.el_type||h?"bar"===p.el_type?(h?(h=!1,s[o].snip.push({start:u,len:d-u}),s[o].voice.push(p)):(l>0&&s[o].voice.push({el_type:"note",duration:l,rest:{type:"invisible"},startChar:p.startChar,endChar:p.endChar}),s[o].voice.push(p)),l=0):"note"===p.el_type?h?s[o].voice.push(p):l+=p.duration:"scale"!==p.el_type&&"stem"!==p.el_type&&"overlay"!==p.el_type&&"style"!==p.el_type&&"transpose"!==p.el_type||s[o].voice.push(p):(e=!0,h=!0,u=d,s[o].hasOverlay=!0)}s[o].hasOverlay&&0===s[o].snip.length&&s[o].snip.push({start:u,len:c.length-u})}for(o=0;o<s.length;o++){var f=s[o];if(f.hasOverlay){a.voices.push(f.voice);for(var m=f.snip.length-1;m>=0;m--){var g=f.snip[m];a.voices[o].splice(g.start,g.len)}for(m=0;m<a.voices[a.voices.length-1].length;m++){a.voices[a.voices.length-1][m]=r.clone(a.voices[a.voices.length-1][m]);var v=a.voices[a.voices.length-1][m];"bar"===v.el_type&&v.startEnding&&delete v.startEnding,"bar"===v.el_type&&v.endEnding&&delete v.endEnding}}}}}return e},this.cleanUp=function(e,t,i,a,s){this.closeLine(),this.metaText.tempo&&this.metaText.tempo.bpm&&!this.metaText.tempo.duration&&(this.metaText.tempo.duration=[this.getBeatLength()]);var o,c,l,h=!1;for(o=0;o<this.lines.length;o++)if(void 0!==this.lines[o].staff){var u=!1;for(c=0;c<this.lines[o].staff.length;c++)if(void 0===this.lines[o].staff[c])h=!0,this.lines[o].staff[c]=null;else for(l=0;l<this.lines[o].staff[c].voices.length;l++)void 0===this.lines[o].staff[c].voices[l]?this.lines[o].staff[c].voices[l]=[]:this.containsNotes(this.lines[o].staff[c].voices[l])&&(u=!0);u||(this.lines[o]=null,h=!0)}if(h&&(this.lines=r.compact(this.lines),r.each(this.lines,(function(e){e.staff&&(e.staff=r.compact(e.staff))}))),i)for(;m(this.lines,i););if(a){for(h=!1,o=0;o<this.lines.length;o++)if(void 0!==this.lines[o].staff)for(c=0;c<this.lines[o].staff.length;c++){var d=!1;for(l=0;l<this.lines[o].staff[c].voices.length;l++)this.containsNotesStrict(this.lines[o].staff[c].voices[l])&&(d=!0);d||(h=!0,this.lines[o].staff[c]=null)}h&&r.each(this.lines,(function(e){e.staff&&(e.staff=r.compact(e.staff))}))}for(function(e){for(var t=!0,i=0;i<e.length;i++){var r=e[i];if(r.staff){for(var n=0;n<r.staff.length;n++){var a=r.staff[n];if(a.title){for(var s=!1,o=0;o<a.title.length;o++)a.title[o]?(a.title[o]=t?a.title[o].name:a.title[o].subname,a.title[o]?s=!0:a.title[o]=""):a.title[o]="";s||delete a.title}}t=!1}}}(this.lines),o=0;o<this.lines.length;o++)if(this.lines[o].staff)for(c=0;c<this.lines[o].staff.length;c++)delete this.lines[o].staff[c].workingClef;for(;this.resolveOverlays(););function p(e){for(var t,i=function(e,i,n){if(void 0===s[n]){for(t=0;t<s.length;t++)if(void 0!==s[t]){n=t;break}if(void 0===s[n]){var a=100*n+1;r.each(e.endSlur,(function(e){a===e&&--a})),s[n]=[a]}}for(var o,c=0;c<i;c++)o=s[n].pop(),e.endSlur.push(o);return 0===s[n].length&&delete s[n],o},n=function(e,t,i,n){e.startSlur=[],void 0===s[i]&&(s[i]=[]);for(var a=100*i+1,o=0;o<t;o++)n&&(r.each(n,(function(e){a===e&&++a})),r.each(n,(function(e){a===e&&++a})),r.each(n,(function(e){a===e&&++a}))),r.each(s[i],(function(e){a===e&&++a})),r.each(s[i],(function(e){a===e&&++a})),s[i].push(a),e.startSlur.push({label:a}),a++},a=0;a<e.length;a++){var o=e[a];if("note"===o.el_type){if(o.gracenotes)for(var c=0;c<o.gracenotes.length;c++){if(o.gracenotes[c].endSlur){var l=o.gracenotes[c].endSlur;o.gracenotes[c].endSlur=[];for(var h=0;h<l;h++)i(o.gracenotes[c],1,20)}o.gracenotes[c].startSlur&&(t=o.gracenotes[c].startSlur,n(o.gracenotes[c],t,20))}if(o.endSlur&&(t=o.endSlur,o.endSlur=[],i(o,t,0)),o.startSlur&&n(o,t=o.startSlur,0),o.pitches){for(var u=[],d=0;d<o.pitches.length;d++)if(o.pitches[d].endSlur){var p=o.pitches[d].endSlur;o.pitches[d].endSlur=[];for(var f=0;f<p;f++){var m=i(o.pitches[d],1,d+1);u.push(m)}}for(d=0;d<o.pitches.length;d++)o.pitches[d].startSlur&&(t=o.pitches[d].startSlur,n(o.pitches[d],t,d+1,u));o.gracenotes&&o.pitches[0].endSlur&&100===o.pitches[0].endSlur[0]&&o.pitches[0].startSlur&&(o.gracenotes[0].endSlur?o.gracenotes[0].endSlur.push(o.pitches[0].startSlur[0].label):o.gracenotes[0].endSlur=[o.pitches[0].startSlur[0].label],1===o.pitches[0].endSlur.length?delete o.pitches[0].endSlur:100===o.pitches[0].endSlur[0]?o.pitches[0].endSlur.shift():100===o.pitches[0].endSlur[o.pitches[0].endSlur.length-1]&&o.pitches[0].endSlur.pop(),1===s[1].length?delete s[1]:s[1].pop())}}}}function f(e){n.fixClef(e)}function m(e,t){for(o=0;o<e.length;o++)if(void 0!==e[o].staff)for(c=0;c<e[o].staff.length;c++){var i=[];for(l=0;l<e[o].staff[c].voices.length;l++)for(var n=e[o].staff[c].voices[l],a=0,s=0;s<n.length;s++)if("bar"===n[s].el_type){if(++a>=t&&s<n.length-1){var h=g(e,o);if(!h){var u=JSON.parse(JSON.stringify(e[o]));e.push(r.clone(u)),h=e[e.length-1];for(var d=0;d<h.staff.length;d++)for(var p=0;p<h.staff[d].voices.length;p++)h.staff[d].voices[p]=[]}var f=s+1,m=e[o].staff[c].voices[l].slice(f);return e[o].staff[c].voices[l]=e[o].staff[c].voices[l].slice(0,f),h.staff[c].voices[l]=i.concat(m.concat(h.staff[c].voices[l])),!0}}else n[s].duration||i.push(n[s])}return!1}function g(e,t){for(t++;e.length>t;){if(e[t].staff)return e[t];t++}return null}for(this.lineNum=0;this.lineNum<this.lines.length;this.lineNum++){var v=this.lines[this.lineNum].staff;if(v)for(this.staffNum=0;this.staffNum<v.length;this.staffNum++)for(v[this.staffNum].clef&&f(v[this.staffNum].clef),this.voiceNum=0;this.voiceNum<v[this.staffNum].voices.length;this.voiceNum++){var b=v[this.staffNum].voices[this.voiceNum];p(b);for(var y=0;y<b.length;y++)"clef"===b[y].el_type&&f(b[y]);if(b.length>0&&b[b.length-1].barNumber){var w=g(this.lines,this.lineNum);w&&(w.staff[0].barNumber=b[b.length-1].barNumber),delete b[b.length-1].barNumber}}}return this.formatting.pagewidth||(this.formatting.pagewidth=e),this.formatting.pageheight||(this.formatting.pageheight=t),delete this.staffNum,delete this.voiceNum,delete this.lineNum,delete this.potentialStartBeam,delete this.potentialEndBeam,delete this.vskipPending,s},this.reset(),this.getLastNote=function(){if(this.lines[this.lineNum]&&this.lines[this.lineNum].staff&&this.lines[this.lineNum].staff[this.staffNum]&&this.lines[this.lineNum].staff[this.staffNum].voices[this.voiceNum])for(var e=this.lines[this.lineNum].staff[this.staffNum].voices[this.voiceNum].length-1;e>=0;e--){var t=this.lines[this.lineNum].staff[this.staffNum].voices[this.voiceNum][e];if("note"===t.el_type)return t}return null},this.addTieToLastNote=function(){var e=this.getLastNote();return!!(e&&e.pitches&&e.pitches.length>0)&&(e.pitches[0].startTie={},!0)},this.getDuration=function(e){return e.duration?e.duration:0},this.closeLine=function(){this.potentialStartBeam&&this.potentialEndBeam&&(this.potentialStartBeam.startBeam=!0,this.potentialEndBeam.endBeam=!0),delete this.potentialStartBeam,delete this.potentialEndBeam},this.appendElement=function(e,t,i,n){var a=this;n.el_type=e,null!==t&&(n.startChar=t),null!==i&&(n.endChar=i);var s=function(){void 0!==a.potentialStartBeam&&void 0!==a.potentialEndBeam&&(a.potentialStartBeam.startBeam=!0,a.potentialEndBeam.endBeam=!0),delete a.potentialStartBeam,delete a.potentialEndBeam};"note"===e?a.getDuration(n)>=.25?s():n.force_end_beam_last&&void 0!==a.potentialStartBeam?s():n.end_beam&&void 0!==a.potentialStartBeam?void 0===n.rest?(a.potentialStartBeam.startBeam=!0,n.endBeam=!0,delete a.potentialStartBeam,delete a.potentialEndBeam):s():void 0===n.rest&&(void 0===a.potentialStartBeam?n.end_beam||(a.potentialStartBeam=n,delete a.potentialEndBeam):a.potentialEndBeam=n):s();delete n.end_beam,delete n.force_end_beam_last,function(e){var t=a.lines[a.lineNum].staff[a.staffNum];if(t){if(void 0!==e.pitches){var i=t.workingClef.verticalPos;r.each(e.pitches,(function(e){e.verticalPos=e.pitch-i}))}if(void 0!==e.gracenotes){var n=t.workingClef.verticalPos;r.each(e.gracenotes,(function(e){e.verticalPos=e.pitch-n}))}t.voices[a.voiceNum].push(e)}}(n)},this.appendStartingElement=function(e,t,i,n){var a;this.closeLine(),"key"===e&&(a=n.impliedNaturals,delete n.impliedNaturals,delete n.explicitAccidentals);var s=r.clone(n);if(this.lines[this.lineNum].staff){this.lines[this.lineNum].staff.length<=this.staffNum&&(this.lines[this.lineNum].staff[this.staffNum]={},this.lines[this.lineNum].staff[this.staffNum].clef=r.clone(this.lines[this.lineNum].staff[0].clef),this.lines[this.lineNum].staff[this.staffNum].key=r.clone(this.lines[this.lineNum].staff[0].key),this.lines[this.lineNum].staff[0].meter&&(this.lines[this.lineNum].staff[this.staffNum].meter=r.clone(this.lines[this.lineNum].staff[0].meter)),this.lines[this.lineNum].staff[this.staffNum].workingClef=r.clone(this.lines[this.lineNum].staff[0].workingClef),this.lines[this.lineNum].staff[this.staffNum].voices=[[]]),"clef"===e&&(this.lines[this.lineNum].staff[this.staffNum].workingClef=s);for(var o=this.lines[this.lineNum].staff[this.staffNum].voices[this.voiceNum],c=0;c<o.length;c++){if("note"===o[c].el_type||"bar"===o[c].el_type)return s.el_type=e,s.startChar=t,s.endChar=i,a&&(s.accidentals=a.concat(s.accidentals)),void o.push(s);if(o[c].el_type===e)return s.el_type=e,s.startChar=t,s.endChar=i,a&&(s.accidentals=a.concat(s.accidentals)),void(o[c]=s)}this.lines[this.lineNum].staff[this.staffNum][e]=n}},this.getNumLines=function(){return this.lines.length},this.pushLine=function(e){this.vskipPending&&(e.vskip=this.vskipPending,delete this.vskipPending),this.lines.push(e)},this.addSubtitle=function(e){this.pushLine({subtitle:e})},this.addSpacing=function(e){this.vskipPending=e},this.addNewPage=function(e){this.pushLine({newpage:e})},this.addSeparator=function(e,t,i){this.pushLine({separator:{spaceAbove:e,spaceBelow:t,lineLength:i}})},this.addText=function(e){this.pushLine({text:e})},this.addCentered=function(e){this.pushLine({text:[{text:e,center:!0}]})},this.containsNotes=function(e){for(var t=0;t<e.length;t++)if("note"===e[t].el_type||"bar"===e[t].el_type)return!0;return!1},this.containsNotesStrict=function(e){for(var t=0;t<e.length;t++)if("note"===e[t].el_type&&void 0===e[t].rest)return!0;return!1},this.changeVoiceScale=function(e){this.appendElement("scale",null,null,{size:e})},this.startNewLine=function(e){var t=this;this.closeLine();var i=function(e){var i=t.lines[t.lineNum].staff[t.staffNum];if(i.voices[t.voiceNum]=[],i.title||(i.title=[]),i.title[t.voiceNum]={name:e.name,subname:e.subname},e.style&&t.appendElement("style",null,null,{head:e.style}),e.stem)t.appendElement("stem",null,null,{direction:e.stem});else if(t.voiceNum>0){if(void 0!==i.voices[0]){for(var r=!1,n=0;n<i.voices[0].length;n++)"stem"===i.voices[0].el_type&&(r=!0);if(!r){i.voices[0].splice(0,0,{el_type:"stem",direction:"up"})}}t.appendElement("stem",null,null,{direction:"down"})}e.scale&&t.appendElement("scale",null,null,{size:e.scale})},r=function(e){e.key&&e.key.impliedNaturals&&(e.key.accidentals=e.key.accidentals.concat(e.key.impliedNaturals),delete e.key.impliedNaturals),t.lines[t.lineNum].staff[t.staffNum]={voices:[],clef:e.clef,key:e.key,workingClef:e.clef},void 0!==e.stafflines&&(t.lines[t.lineNum].staff[t.staffNum].clef.stafflines=e.stafflines,t.lines[t.lineNum].staff[t.staffNum].workingClef.stafflines=e.stafflines),e.staffscale&&(t.lines[t.lineNum].staff[t.staffNum].staffscale=e.staffscale),e.tripletfont&&(t.lines[t.lineNum].staff[t.staffNum].tripletfont=e.tripletfont),e.vocalfont&&(t.lines[t.lineNum].staff[t.staffNum].vocalfont=e.vocalfont),e.bracket&&(t.lines[t.lineNum].staff[t.staffNum].bracket=e.bracket),e.brace&&(t.lines[t.lineNum].staff[t.staffNum].brace=e.brace),e.connectBarLines&&(t.lines[t.lineNum].staff[t.staffNum].connectBarLines=e.connectBarLines),e.barNumber&&(t.lines[t.lineNum].staff[t.staffNum].barNumber=e.barNumber),i(e),e.part&&t.appendElement("part",e.part.startChar,e.part.endChar,{title:e.part.title}),void 0!==e.meter&&(t.lines[t.lineNum].staff[t.staffNum].meter=e.meter)};if(void 0===this.lines[this.lineNum])!function(e){t.lines[t.lineNum]={staff:[]},r(e)}(e);else if(void 0===this.lines[this.lineNum].staff)this.lineNum++,this.startNewLine(e);else if(void 0===this.lines[this.lineNum].staff[this.staffNum])r(e);else if(void 0===this.lines[this.lineNum].staff[this.staffNum].voices[this.voiceNum])i(e);else{if(!this.containsNotes(this.lines[this.lineNum].staff[this.staffNum].voices[this.voiceNum]))return;this.lineNum++,this.startNewLine(e)}},this.setBarNumberImmediate=function(e){var t=this.getCurrentVoice();if(t&&t.length>0){var i=t[t.length-1];if("bar"!==i.el_type)return e-1;void 0!==i.barNumber&&(i.barNumber=e)}return e},this.hasBeginMusic=function(){for(var e=0;e<this.lines.length;e++)if(this.lines[e].staff)return!0;return!1},this.isFirstLine=function(e){for(var t=e-1;t>=0;t--)if(void 0!==this.lines[t].staff)return!1;return!0},this.getMeter=function(){for(var e=0;e<this.lines.length;e++){var t=this.lines[e];if(t.staff)for(var i=0;i<t.staff.length;i++){var r=t.staff[i].meter;if(r)return r}}return{type:"common_time"}},this.getMeterFraction=function(){var e=this.getMeter(),t=4,i=4;return e&&("specified"===e.type?(t=parseInt(e.value[0].num,10),i=parseInt(e.value[0].den,10)):"cut_time"===e.type?(t=2,i=2):"common_time"===e.type&&(t=4,i=4)),this.meter={num:t,den:i},this.meter},this.getKeySignature=function(){for(var e=0;e<this.lines.length;e++){var t=this.lines[e];if(t.staff)for(var i=0;i<t.staff.length;i++)if(t.staff[i].key)return t.staff[i].key}return{}},this.getCurrentVoice=function(){return void 0!==this.lines[this.lineNum]&&void 0!==this.lines[this.lineNum].staff[this.staffNum]&&void 0!==this.lines[this.lineNum].staff[this.staffNum].voices[this.voiceNum]?this.lines[this.lineNum].staff[this.staffNum].voices[this.voiceNum]:null},this.setCurrentVoice=function(e,t){this.staffNum=e,this.voiceNum=t;for(var i=0;i<this.lines.length;i++)if(this.lines[i].staff&&(void 0===this.lines[i].staff[e]||void 0===this.lines[i].staff[e].voices[t]||!this.containsNotes(this.lines[i].staff[e].voices[t])))return void(this.lineNum=i);this.lineNum=i},this.addMetaText=function(e,t){void 0===this.metaText[e]?this.metaText[e]=t:this.metaText[e]+="\n"+t},this.addMetaTextArray=function(e,t){void 0===this.metaText[e]?this.metaText[e]=[t]:this.metaText[e].push(t)},this.addMetaTextObj=function(e,t){this.metaText[e]=t},this.addElementToEvents=function(e,t,i,n,a,s,o,c,l,h){if(t.hint)return{isTiedState:void 0,duration:0};var u=t.durationClass?t.durationClass:t.duration;if(t.abcelem.rest&&"spacer"===t.abcelem.rest.type&&(u=0),u>0){for(var d=[],p=0;p<t.elemset.length;p++)null!==t.elemset[p]&&d.push(t.elemset[p]);var f=t.startTie;if(void 0!==l)e["event"+l].elements.push(d),h&&(e["event"+i]||(e["event"+i]={type:"event",milliseconds:i,line:s,measureNumber:o,top:n,height:a,left:null,width:0,elements:[],startChar:null,endChar:null,startCharArray:[],endCharArray:[]}),e["event"+i].measureStart=!0,h=!1),f||(l=void 0);else{if(e["event"+i]){if(e["event"+i].left?e["event"+i].left=Math.min(e["event"+i].left,t.x):e["event"+i].left=t.x,e["event"+i].elements.push(d),e["event"+i].startCharArray.push(t.abcelem.startChar),e["event"+i].endCharArray.push(t.abcelem.endChar),null===e["event"+i].startChar&&(e["event"+i].startChar=t.abcelem.startChar),null===e["event"+i].endChar&&(e["event"+i].endChar=t.abcelem.endChar),t.abcelem.midiPitches&&t.abcelem.midiPitches.length){e["event"+i].midiPitches||(e["event"+i].midiPitches=[]);for(p=0;p<t.abcelem.midiPitches.length;p++)e["event"+i].midiPitches.push(t.abcelem.midiPitches[p])}if(t.abcelem.midiGraceNotePitches&&t.abcelem.midiGraceNotePitches.length){e["event"+i].midiGraceNotePitches||(e["event"+i].midiGraceNotePitches=[]);for(var m=0;m<t.abcelem.midiGraceNotePitches.length;m++)e["event"+i].midiGraceNotePitches.push(t.abcelem.midiGraceNotePitches[m])}}else e["event"+i]={type:"event",milliseconds:i,line:s,measureNumber:o,top:n,height:a,left:t.x,width:t.w,elements:[d],startChar:t.abcelem.startChar,endChar:t.abcelem.endChar,startCharArray:[t.abcelem.startChar],endCharArray:[t.abcelem.endChar],midiPitches:t.abcelem.midiPitches?r.cloneArray(t.abcelem.midiPitches):[]},t.abcelem.midiGraceNotePitches&&(e["event"+i].midiGraceNotePitches=r.cloneArray(t.abcelem.midiGraceNotePitches));h&&(e["event"+i].measureStart=!0,h=!1),f&&(l=i)}}return{isTiedState:l,duration:u/c,nextIsBar:h||"bar"===t.type}},this.makeVoicesArray=function(){for(var e=[],t=0;t<this.engraver.staffgroups.length;t++)for(var i=this.engraver.staffgroups[t],r=i.staffs[0],n=r.absoluteY,s=n-r.top*a.STEP,o=i.staffs[i.staffs.length-1],c=(n=o.absoluteY)-o.bottom*a.STEP-s,l=i.voices,h=0;h<l.length;h++){var u=0,d=!1;e[h]||(e[h]=[]);for(var p=l[h].children,f=0;f<p.length;f++)e[h].push({top:s,height:c,line:t,measureNumber:u,elem:p[f]}),"bar"===p[f].type&&d&&u++,"note"!==p[f].type&&"rest"!==p[f].type||(d=!0)}return e},this.setupEvents=function(e,t,i){for(var r,n=[],a={},s=e,o=!0,c=this.makeVoicesArray(),l=0;l<c.length;l++)for(var h=s,u=Math.round(1e3*h),d=0,p=-1,f=c[l],m=0;m<f.length;m++){var g=f[m].elem;if("tempo"===g.abcelem.el_type){i=this.getBpm(g.abcelem);t=this.getBeatLength()*(i/60)}var v=this.addElementToEvents(a,g,u,f[m].top,f[m].height,f[m].line,f[m].measureNumber,t,r,o);if(r=v.isTiedState,o=v.nextIsBar,h+=v.duration,u=Math.round(1e3*h),"bar"===g.type){var b=g.abcelem.type,y="bar_right_repeat"===b||"bar_dbl_repeat"===b,w="1"===g.abcelem.startEnding,k="bar_left_repeat"===b||"bar_dbl_repeat"===b||"bar_right_repeat"===b;if(y){-1===p&&(p=m);for(var x=d;x<p;x++){var S=f[x].elem;r=(v=this.addElementToEvents(a,S,u,f[x].top,f[x].height,f[x].line,f[x].measureNumber,t,r,o)).isTiedState,o=v.nextIsBar,h+=v.duration,u=Math.round(1e3*h)}o=!0,p=-1}w&&(p=m),k&&(d=m)}}return function(e){for(var t,i,r,n,a=e.length-1;a>=0;a--){var s=e[a];"bar"===s.type?(s.top=r,s.nextTop=t,t=r,s.bottom=n,s.nextBottom=i,i=n):"event"===s.type&&(r=s.top,n=s.top+s.height)}}(n=function(e){var t=[];for(var i in e)e.hasOwnProperty(i)&&t.push(e[i]);return t=t.sort((function(e,t){var i=e.milliseconds-t.milliseconds;return 0!==i?i:"bar"===e.type?-1:1}))}(a)),n.push({type:"end",milliseconds:u}),this.addUsefulCallbackInfo(n,i),n},this.addUsefulCallbackInfo=function(e,t){for(var i=this.millisecondsPerMeasure(t),r=0;r<e.length;r++){e[r].millisecondsPerMeasure=i}},this.getBpm=function(e){var t;if(e){t=e.bpm;var i=this.getBeatLength();t=t*(e.duration&&e.duration.length>0?e.duration[0]:i)/i}if(!t){t=180;var r=this.getMeterFraction();r&&8===r.den&&(t=120)}return t},this.setTiming=function(e,t){if(!e){var i=this.metaText?this.metaText.tempo:null;e=this.getBpm(i)}var r=this.getBeatLength(),n=e/60,a=this.getBarLength()/r*t/n;a&&(a-=this.getPickupLength()/r/n);var s=r*n;this.noteTimings=this.setupEvents(a,s,e)}}},function(e,t,i){var r=i(1),n=i(34),a=i(48),s=function(e,t){t=t||{},this.responsive=t.responsive,this.space=3*r.SPACE,this.scale=t.scale?parseFloat(t.scale):0,this.scale>.1||(this.scale=void 0),t.staffwidth?(this.staffwidthScreen=t.staffwidth,this.staffwidthPrint=t.staffwidth):(this.staffwidthScreen=740,this.staffwidthPrint=680),this.editable=t.editable||!1,this.listeners=[],t.clickListener&&this.addSelectListener(t.clickListener),this.renderer=new a(e,t.regression,t.add_classes),this.renderer.setPaddingOverride(t),this.renderer.controller=this,this.reset()};function o(e,t,i,r,n,a,s){return e&&r/i<.66&&!t?null:Math.abs(i-r)<2?null:a>0?((n=(i-(r-a*n))/a)*s>50&&(n=50/s),n):null}s.prototype.reset=function(){this.selected=[],this.ingroup=!1,this.staffgroups=[],this.lastStaffGroupIndex=-1,this.engraver&&this.engraver.reset(),this.engraver=null,this.renderer.reset()},s.prototype.engraveABC=function(e,t){void 0===e[0]&&(e=[e]),this.reset();for(var i=0;i<e.length;i++)void 0===t&&(t=i),this.engraveTune(e[i],t);if(this.renderer.doRegression)return this.renderer.regressionLines.join("\n")},s.prototype.adjustNonScaledItems=function(e){this.width/=e,this.renderer.adjustNonScaledItems(e)},s.prototype.getMeasureWidths=function(e){this.reset(),this.renderer.lineNumber=null,this.renderer.newTune(e),this.engraver=new n(this.renderer,0,{bagpipes:e.formatting.bagpipes,flatbeams:e.formatting.flatbeams}),this.engraver.setStemHeight(this.renderer.spacing.stemHeight),e.formatting.staffwidth?this.width=1.33*e.formatting.staffwidth:this.width=this.renderer.isPrint?this.staffwidthPrint:this.staffwidthScreen;var t=e.formatting.scale?e.formatting.scale:this.scale;"resize"===this.responsive&&(t=void 0),void 0===t&&(t=this.renderer.isPrint?.75:1),this.adjustNonScaledItems(t);var i={left:0,measureWidths:[],height:0,total:0};i.height=this.renderer.padding.top+this.renderer.spacing.music+this.renderer.padding.bottom+24;for(var a=!1,s=0;s<e.lines.length;s++){var o=e.lines[s];if(o.staff){if(o.staffGroup=this.engraver.createABCLine(o.staff,a?null:e.metaText.tempo),o.staffGroup.layout(0,this.renderer,!1),o.staffGroup.voices.length>0)for(var c=o.staffGroup.voices[0],l=!1,h=0,u=0;u<c.children.length;u++){var d=c.children[u];l||d.isClef||d.isKeySig||(l=!0,i.left=d.x,h=d.x),"bar"===d.type&&(i.measureWidths.push(d.x-h),i.total+=d.x-h,h=d.x)}a=!0,i.height+=o.staffGroup.calcHeight()*r.STEP}}return i},s.prototype.engraveTune=function(e,t){this.renderer.lineNumber=null,this.renderer.newTune(e),this.engraver=new n(this.renderer,t,{bagpipes:e.formatting.bagpipes,flatbeams:e.formatting.flatbeams}),this.engraver.setStemHeight(this.renderer.spacing.stemHeight),this.engraver.measureLength=e.getMeterFraction().num/e.getMeterFraction().den,e.formatting.staffwidth?this.width=1.33*e.formatting.staffwidth:this.width=this.renderer.isPrint?this.staffwidthPrint:this.staffwidthScreen;var i,r,a=e.formatting.scale?e.formatting.scale:this.scale;"resize"===this.responsive&&(a=void 0),void 0===a&&(a=this.renderer.isPrint?.75:1),this.adjustNonScaledItems(a);var s=!1;for(i=0;i<e.lines.length;i++)(r=e.lines[i]).staff&&(r.staffGroup=this.engraver.createABCLine(r.staff,s?null:e.metaText.tempo),s=!0);var o=this.width;for(i=0;i<e.lines.length;i++)(r=e.lines[i]).staff&&(this.setXSpacing(r.staffGroup,e.formatting,i===e.lines.length-1,!1),r.staffGroup.w>o&&(o=r.staffGroup.w));for(i=0;i<e.lines.length;i++)if((r=e.lines[i]).staffGroup&&r.staffGroup.voices){for(var c=0;c<r.staffGroup.voices.length;c++)r.staffGroup.voices[c].layoutBeams();r.staffGroup.setUpperAndLowerElements(this.renderer)}for(i=0;i<e.lines.length;i++)(r=e.lines[i]).staffGroup&&(r.staffGroup.height=r.staffGroup.calcHeight());this.renderer.topMargin(e),this.renderer.engraveTopText(this.width,e),this.renderer.addMusicPadding(),this.staffgroups=[],this.lastStaffGroupIndex=-1;for(var l=0;l<e.lines.length;l++)this.renderer.lineNumber=l,(r=e.lines[l]).staff?this.engraveStaffLine(r.staffGroup):r.subtitle&&0!==l?this.renderer.outputSubtitle(this.width,r.subtitle):void 0!==r.text?this.renderer.outputFreeText(r.text,r.vskip):void 0!==r.separator&&this.renderer.outputSeparator(r.separator);this.renderer.moveY(24),this.renderer.engraveExtraText(this.width,e),this.renderer.setPaperSize(o,a,this.responsive)},s.prototype.setXSpacing=function(e,t,i,r){for(var n=this.space,a=0;a<8;a++){var s=e.layout(n,this.renderer,r);if(n=o(i,!!t.stretchlast&&t.stretchlast,this.width+this.renderer.padding.left,e.w,n,s.spacingUnits,s.minSpace),r&&console.log("setXSpace",a,e.w,n,e.minspace),null===n)break}!function(e){for(var t=0;t<e.length;t++)for(var i=e[t],r=1;r<i.children.length-1;r++){var n=i.children[r];if(n.abcelem.rest&&("whole"===n.abcelem.rest.type||"multimeasure"===n.abcelem.rest.type)){var a=i.children[r-1],s=(i.children[r+1].x-a.x)/2+a.x;n.x=s-n.w/2;for(var o=0;o<n.children.length;o++)n.children[o].x=n.x}}}(e.voices)},s.prototype.engraveStaffLine=function(e){this.lastStaffGroupIndex>-1&&this.renderer.addStaffPadding(this.staffgroups[this.lastStaffGroupIndex],e),this.renderer.voiceNumber=null,e.draw(this.renderer);var t=e.height*r.STEP;this.staffgroups[this.staffgroups.length]=e,this.lastStaffGroupIndex=this.staffgroups.length-1,this.renderer.y+=t},s.prototype.notifySelect=function(e,t,i){this.clearSelection(),e.highlight&&(this.selected=[e],e.highlight());for(var r=e.abcelem||{},n=0;n<this.listeners.length;n++)this.listeners[n](r,t,i)},s.prototype.clearSelection=function(){for(var e=0;e<this.selected.length;e++)this.selected[e].unhighlight();this.selected=[]},s.prototype.addSelectListener=function(e){this.listeners[this.listeners.length]=e},s.prototype.rangeHighlight=function(e,t){this.clearSelection();for(var i=0;i<this.staffgroups.length;i++)for(var r=this.staffgroups[i].voices,n=0;n<r.length;n++)for(var a=r[n].children,s=0;s<a.length;s++){var o=a[s].abcelem.startChar,c=a[s].abcelem.endChar;(t>o&&e<c||t===e&&t===c)&&(this.selected[this.selected.length]=a[s],a[s].highlight())}},e.exports=s},function(e,t){var i=function(e){this.anchor1=e.anchor1,this.anchor2=e.anchor2,e.isGrace&&(this.isGrace=!0),e.fixedY&&(this.fixedY=!0),e.stemDir&&(this.stemDir=e.stemDir),void 0!==e.voiceNumber&&(this.voiceNumber=e.voiceNumber),this.internalNotes=[]};i.prototype.addInternalNote=function(e){this.internalNotes.push(e)},i.prototype.setEndAnchor=function(e){this.anchor2=e},i.prototype.setStartX=function(e){this.startLimitX=e},i.prototype.setEndX=function(e){this.endLimitX=e},i.prototype.setHint=function(){this.hint=!0},i.prototype.setUpperAndLowerElements=function(e){},i.prototype.calcTieDirection=function(){if(this.isGrace)this.above=!1;else if(0===this.voiceNumber)this.above=!0;else if(this.voiceNumber>0)this.above=!1;else{var e;e=this.anchor1?this.anchor1.pitch:this.anchor2?this.anchor2.pitch:14,this.anchor1&&"down"===this.anchor1.stemDir&&this.anchor2&&"down"===this.anchor2.stemDir?this.above=!0:this.anchor1&&"up"===this.anchor1.stemDir&&this.anchor2&&"up"===this.anchor2.stemDir?this.above=!1:this.anchor1&&this.anchor2?this.above=e>=6:this.anchor1?this.above="down"===this.anchor1.stemDir:this.anchor2?this.above="down"===this.anchor2.stemDir:this.above=e>=6}},i.prototype.calcSlurDirection=function(){if(this.isGrace)this.above=!1;else if(0===this.voiceNumber)this.above=!0;else if(this.voiceNumber>0)this.above=!1;else{var e=!1;this.anchor1&&"down"===this.anchor1.stemDir&&(e=!0),this.anchor2&&"down"===this.anchor2.stemDir&&(e=!0);for(var t=0;t<this.internalNotes.length;t++){"down"===this.internalNotes[t].stemDir&&(e=!0)}this.above=e}},i.prototype.calcX=function(e,t){this.anchor1?(this.startX=this.anchor1.x,this.anchor1.scalex<1&&(this.startX-=3)):this.startLimitX?this.startX=this.startLimitX.x+this.startLimitX.w:this.startX=e,this.anchor2?this.endX=this.anchor2.x:this.endLimitX?this.endX=this.endLimitX.x:this.endX=t},i.prototype.calcTieY=function(){this.anchor1?this.startY=this.anchor1.pitch:this.anchor2?this.startY=this.anchor2.pitch:this.startY=this.above?14:0,this.anchor2?this.endY=this.anchor2.pitch:this.anchor1?this.endY=this.anchor1.pitch:this.endY=this.above?14:0},i.prototype.calcSlurY=function(){if(this.anchor1&&this.anchor2){this.above&&"up"===this.anchor1.stemDir&&!this.fixedY?(this.startY=(this.anchor1.highestVert+this.anchor1.pitch)/2,this.startX+=this.anchor1.w/2):this.startY=this.anchor1.pitch;var e=this.anchor2.parent.beam&&this.anchor2.parent.beam.stemsUp&&this.anchor2.parent.beam.elems[0]!==this.anchor2.parent,t=(this.anchor2.highestVert+this.anchor2.pitch)/2;this.above&&"up"===this.anchor2.stemDir&&!this.fixedY&&!e&&t<this.startY?(this.endY=t,this.endX+=this.anchor2.w/2):this.endY=this.above&&e?this.anchor2.highestVert:this.anchor2.pitch}else this.anchor1?this.startY=this.endY=this.anchor1.pitch:this.anchor2?this.startY=this.endY=this.anchor2.pitch:(this.startY=this.above?14:0,this.endY=this.above?14:0)},i.prototype.avoidCollisionAbove=function(){if(this.above){for(var e=-50,t=0;t<this.internalNotes.length;t++)this.internalNotes[t].highestVert>e&&(e=this.internalNotes[t].highestVert);e>this.startY&&e>this.endY&&(this.startY=this.endY=e-1)}},i.prototype.layout=function(e,t){this.anchor1&&this.anchor2?this.anchor1.pitch===this.anchor2.pitch&&0===this.internalNotes.length?this.isTie=!0:this.isTie=!1:this.isTie=!0,this.isTie?(this.calcTieDirection(),this.calcX(e,t),this.calcTieY()):(this.calcSlurDirection(),this.calcX(e,t),this.calcSlurY()),this.avoidCollisionAbove()},i.prototype.draw=function(e,t,i){var r;this.layout(t,i),this.hint&&(r="abcjs-hint");var n=this.fixedY?1.5:0;e.drawArc(this.startX,this.endX,this.startY+n,this.endY+n,this.above,r,this.isTie)},e.exports=i},function(e,t){e.exports={}},function(e,t){e.exports={21:"A0",22:"Bb0",23:"B0",24:"C1",25:"Db1",26:"D1",27:"Eb1",28:"E1",29:"F1",30:"Gb1",31:"G1",32:"Ab1",33:"A1",34:"Bb1",35:"B1",36:"C2",37:"Db2",38:"D2",39:"Eb2",40:"E2",41:"F2",42:"Gb2",43:"G2",44:"Ab2",45:"A2",46:"Bb2",47:"B2",48:"C3",49:"Db3",50:"D3",51:"Eb3",52:"E3",53:"F3",54:"Gb3",55:"G3",56:"Ab3",57:"A3",58:"Bb3",59:"B3",60:"C4",61:"Db4",62:"D4",63:"Eb4",64:"E4",65:"F4",66:"Gb4",67:"G4",68:"Ab4",69:"A4",70:"Bb4",71:"B4",72:"C5",73:"Db5",74:"D5",75:"Eb5",76:"E5",77:"F5",78:"Gb5",79:"G5",80:"Ab5",81:"A5",82:"Bb5",83:"B5",84:"C6",85:"Db6",86:"D6",87:"Eb6",88:"E6",89:"F6",90:"Gb6",91:"G6",92:"Ab6",93:"A6",94:"Bb6",95:"B6",96:"C7",97:"Db7",98:"D7",99:"Eb7",100:"E7",101:"F7",102:"Gb7",103:"G7",104:"Ab7",105:"A7",106:"Bb7",107:"B7",108:"C8",109:"Db8",110:"D8",111:"Eb8",112:"E8",113:"F8",114:"Gb8",115:"G8",116:"Ab8",117:"A8",118:"Bb8",119:"B8",120:"C9",121:"Db9"}},function(e,t){e.exports=function(){var e=this;e.tracks=[],e.totalDuration=0,e.addTrack=function(){return e.tracks.push([]),e.tracks.length-1},e.setInstrument=function(t,i){e.tracks[t].push({channel:0,cmd:"program",instrument:i})},e.appendNote=function(t,i,r,n){e.tracks[t].push({cmd:"start",pitch:i-60,volume:n}),e.tracks[t].push({cmd:"move",duration:r}),e.tracks[t].push({cmd:"stop",pitch:i-60});var a=0;e.tracks[t].forEach((function(e){e.duration&&(a+=e.duration)})),e.totalDuration=Math.max(e.totalDuration,a)}}},function(e,t,i){var r=i(7),n=i(15),a=i(6),s=i(0),o='<svg version="1.0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 700 700" preserveAspectRatio="xMidYMid meet">\n<g transform="translate(0,700) scale(0.1,-0.1)" >\n<path d="M3111 6981 c-20 -37 -90 -55 -364 -96 -120 -18 -190 -33 -244 -55 -42 -17 -124 -42 -182 -56 -78 -18 -119 -34 -157 -60 -28 -19 -86 -46 -128 -60 -43 -13 -107 -42 -144 -64 -37 -23 -84 -46 -106 -52 -21 -7 -56 -29 -79 -50 -22 -22 -61 -50 -86 -63 -26 -13 -67 -40 -91 -60 -24 -20 -65 -47 -90 -60 -25 -13 -53 -31 -61 -41 -8 -9 -32 -30 -54 -46 -75 -54 -486 -460 -512 -507 -15 -25 -48 -69 -75 -98 -26 -28 -48 -57 -48 -63 0 -6 -18 -29 -39 -53 -21 -23 -56 -71 -77 -107 -20 -36 -50 -80 -65 -97 -16 -18 -33 -52 -40 -75 -12 -47 -47 -115 -84 -166 -13 -18 -30 -56 -38 -83 -8 -27 -34 -80 -56 -118 -33 -53 -46 -91 -62 -167 -12 -63 -34 -127 -59 -179 -42 -84 -60 -166 -60 -270 0 -90 26 -122 125 -154 54 -17 96 -19 430 -20 305 -1 381 2 430 14 82 22 140 51 153 78 6 12 22 47 37 77 14 30 38 77 54 103 15 27 34 73 40 103 7 30 28 78 48 107 19 28 44 74 55 101 10 28 34 67 53 87 18 20 49 61 68 90 19 30 44 63 57 74 13 11 36 40 52 65 59 94 232 270 306 313 20 11 57 37 82 58 25 20 70 52 100 72 30 19 66 47 79 61 13 14 49 35 80 46 30 12 80 37 111 56 31 19 95 45 143 58 48 12 110 37 139 55 63 40 127 55 323 76 83 9 208 28 279 41 156 29 165 29 330 4 453 -71 514 -84 606 -130 31 -16 83 -36 116 -45 32 -9 84 -34 115 -56 31 -21 82 -48 113 -60 32 -11 72 -33 89 -48 18 -16 59 -45 92 -65 33 -21 74 -51 90 -66 17 -15 49 -40 73 -54 52 -32 65 -61 50 -113 -8 -31 -61 -90 -277 -308 -300 -303 -361 -382 -369 -481 -2 -29 0 -66 6 -81 13 -40 88 -138 115 -151 12 -6 54 -26 92 -44 l70 -33 945 -2 c520 -1 975 2 1012 7 64 8 191 50 231 76 11 7 33 34 50 60 22 34 42 51 65 58 l32 9 0 1101 0 1102 -32 9 c-21 7 -44 26 -64 55 -60 84 -77 97 -140 110 -44 9 -76 10 -127 2 -59 -9 -77 -17 -134 -62 -37 -28 -172 -155 -301 -281 -129 -127 -249 -237 -267 -245 -25 -10 -41 -11 -71 -2 -58 15 -112 45 -124 69 -6 11 -35 35 -64 54 -28 18 -58 41 -66 50 -8 9 -41 35 -75 58 -33 22 -77 56 -99 75 -21 18 -64 46 -95 61 -31 14 -73 39 -93 55 -20 15 -70 40 -110 55 -40 15 -97 44 -127 64 -29 21 -78 44 -107 53 -30 8 -77 31 -105 51 -42 28 -73 39 -173 60 -68 14 -154 39 -196 58 -95 43 -131 51 -343 76 -209 24 -242 32 -279 70 l-30 29 -328 0 c-312 0 -330 -1 -339 -19z"></path>\n<path d="M254 2875 c-89 -16 -107 -26 -145 -78 -32 -44 -62 -66 -91 -67 -17 0 -18 -61 -18 -1140 l0 -1140 24 0 c16 0 41 -17 72 -50 40 -42 61 -55 117 -72 l69 -21 82 23 c44 12 96 30 114 39 18 9 148 132 290 272 141 141 267 261 279 268 51 26 86 14 176 -61 32 -26 62 -48 66 -48 5 0 36 -25 70 -55 34 -30 74 -61 89 -69 15 -8 37 -28 50 -45 12 -17 50 -45 84 -62 34 -17 78 -44 98 -60 19 -16 61 -37 93 -48 32 -11 81 -37 107 -56 27 -20 76 -45 109 -56 33 -12 75 -31 93 -44 62 -45 93 -58 191 -82 54 -12 130 -37 168 -54 68 -29 180 -58 226 -59 62 0 183 -64 183 -96 0 -12 88 -14 639 -14 l639 0 12 30 c18 44 76 66 233 89 89 14 160 30 200 47 34 15 106 42 159 60 54 18 112 44 130 57 47 35 85 52 146 67 29 7 76 28 105 48 29 20 77 48 107 63 30 15 66 39 80 54 14 15 50 40 81 56 31 15 78 46 104 69 26 22 61 46 79 54 17 7 43 26 56 42 14 16 41 41 60 56 64 48 380 362 408 405 15 23 40 51 55 63 15 12 36 38 46 58 11 21 37 57 58 82 22 25 49 62 62 83 13 20 38 56 57 78 19 23 50 74 69 113 19 39 46 86 59 104 14 18 34 62 46 98 12 36 32 77 45 92 31 38 60 97 80 167 9 33 26 76 37 95 29 50 47 103 68 206 10 52 32 117 51 155 29 56 33 74 34 140 0 94 -10 108 -101 138 -61 20 -83 21 -463 21 -226 0 -421 -4 -451 -10 -63 -12 -86 -30 -110 -85 -10 -22 -33 -63 -52 -92 -21 -31 -42 -80 -53 -123 -11 -44 -32 -93 -56 -128 -20 -32 -47 -83 -59 -115 -12 -32 -37 -77 -56 -100 -19 -23 -50 -65 -69 -94 -19 -29 -44 -57 -54 -63 -11 -5 -29 -27 -42 -47 -52 -85 -234 -277 -300 -315 -25 -15 -53 -38 -62 -51 -9 -14 -42 -39 -74 -57 -32 -18 -75 -48 -95 -66 -21 -18 -59 -44 -85 -58 -26 -13 -72 -40 -100 -59 -35 -24 -78 -41 -128 -52 -47 -11 -99 -31 -139 -56 -69 -42 -94 -49 -391 -110 -245 -51 -425 -66 -595 -50 -168 16 -230 27 -330 61 -47 16 -123 35 -170 44 -98 17 -123 25 -172 58 -20 14 -71 37 -114 53 -44 15 -95 40 -115 56 -20 16 -70 42 -110 59 -40 16 -88 45 -108 63 -20 19 -55 46 -78 61 -24 14 -49 35 -55 47 -7 11 -34 33 -60 49 -50 31 -65 61 -53 102 4 13 130 147 281 298 236 238 277 283 299 335 15 32 35 71 46 86 12 18 19 44 19 76 0 42 -8 63 -53 138 -92 151 11 139 -1207 141 -798 2 -1030 0 -1086 -11z"></path>\n</g>\n</svg>\n',c='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25" class="abcjs-play-svg">\n    <g>\n    <polygon points="4 0 23 12.5 4 25"/>\n    </g>\n</svg>',l='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25" class="abcjs-pause-svg">\n  <g>\n    <rect width="8.23" height="25"/>\n    <rect width="8.23" height="25" x="17"/>\n  </g>\n</svg>',h='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" class="abcjs-loading-svg">\n    <circle cx="50" cy="50" fill="none" stroke-width="20" r="35" stroke-dasharray="160 55"></circle>\n</svg>',u='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25">\n  <g>\n    <polygon points="5 12.5 24 0 24 25"/>\n    <rect width="3" height="25" x="0" y="0"/>\n  </g>\n</svg>';function d(e,t,i,s,o){var c=!0;if(a()?c="suspended"===a().state:n(),!r())throw{status:"NotSupported",message:"This browser does not support audio."};(c||o)&&i&&i.classList.add("abcjs-loading"),c?a().resume().then((function(){s?s().then((function(r){p(e,t,i,o)})):p(e,t,i,o)})):p(e,t,i,o)}function p(e,t,i,r){r?e(t).then((function(){i&&i.classList.remove("abcjs-loading")})):(e(t),i&&i.classList.remove("abcjs-loading"))}e.exports=function(e,t){var i=this;if("string"==typeof e){var r=e;if(!(e=document.querySelector(r)))throw new Error('Cannot find element "'+r+'" in the DOM.')}else if(!(e instanceof HTMLElement))throw new Error("The first parameter must be a valid element or selector in the DOM.");if(i.parent=e,i.options={},t&&(i.options=s.clone(t)),i.options.ac&&n(i.options.ac),function(e,t){var i=!!t.loopHandler,r=!!t.restartHandler,n=!!t.playHandler||!!t.playPromiseHandler,a=!!t.progressHandler,s=!!t.warpHandler,d=!1!==t.hasClock,p='<div class="abcjs-inline-audio">\n';if(i){var f=t.repeatTitle?t.repeatTitle:"Click to toggle play once/repeat.",m=t.repeatAria?t.repeatAria:f;p+='<button type="button" class="abcjs-midi-loop abcjs-btn" title="'+f+'" aria-label="'+m+'">'+o+"</button>\n"}if(r){var g=t.restartTitle?t.restartTitle:"Click to go to beginning.",v=t.restartAria?t.restartAria:g;p+='<button type="button" class="abcjs-midi-reset abcjs-btn" title="'+g+'" aria-label="'+v+'">'+u+"</button>\n"}if(n){var b=t.playTitle?t.playTitle:"Click to play/pause.",y=t.playAria?t.playAria:b;p+='<button type="button" class="abcjs-midi-start abcjs-btn" title="'+b+'" aria-label="'+y+'">'+c+l+h+"</button>\n"}if(a){var w=t.randomTitle?t.randomTitle:"Click to change the playback position.",k=t.randomAria?t.randomAria:w;p+='<button type="button" class="abcjs-midi-progress-background" title="'+w+'" aria-label="'+k+'"><span class="abcjs-midi-progress-indicator"></span></button>\n'}d&&(p+='<span class="abcjs-midi-clock"></span>\n');if(s){var x=t.warpTitle?t.warpTitle:"Change the playback speed.",S=t.warpAria?t.warpAria:x,_=t.bpm?t.bpm:"BPM";p+='<span class="abcjs-tempo-wrapper"><label><input class="abcjs-midi-tempo" type="number" min="1" max="300" value="100" title="'+x+'" aria-label="'+S+'">%</label><span>&nbsp;(<span class="abcjs-midi-current-tempo"></span> '+_+")</span></span>\n"}p+="</div>\n",e.innerHTML=p}(i.parent,i.options),function(e){var t=!!e.options.loopHandler,i=!!e.options.restartHandler,r=!!e.options.playHandler||!!e.options.playPromiseHandler,n=!!e.options.progressHandler,a=!!e.options.warpHandler,s=e.parent.querySelector(".abcjs-midi-start");t&&e.parent.querySelector(".abcjs-midi-loop").addEventListener("click",(function(t){d(e.options.loopHandler,t,s,e.options.afterResume)}));i&&e.parent.querySelector(".abcjs-midi-reset").addEventListener("click",(function(t){d(e.options.restartHandler,t,s,e.options.afterResume)}));r&&s.addEventListener("click",(function(t){d(e.options.playPromiseHandler||e.options.playHandler,t,s,e.options.afterResume,!!e.options.playPromiseHandler)}));n&&e.parent.querySelector(".abcjs-midi-progress-background").addEventListener("click",(function(t){d(e.options.progressHandler,t,s,e.options.afterResume)}));a&&e.parent.querySelector(".abcjs-midi-tempo").addEventListener("change",(function(t){d(e.options.warpHandler,t,s,e.options.afterResume)}))}(i),i.setTempo=function(e){var t=i.parent.querySelector(".abcjs-midi-current-tempo");t&&(t.innerHTML=e)},i.resetAll=function(){for(var e=i.parent.querySelectorAll(".abcjs-pushed"),t=0;t<e.length;t++){e[t].classList.remove("abcjs-pushed")}},i.pushPlay=function(e){var t=i.parent.querySelector(".abcjs-midi-start");t&&(e?t.classList.add("abcjs-pushed"):t.classList.remove("abcjs-pushed"))},i.pushLoop=function(e){var t=i.parent.querySelector(".abcjs-midi-loop");t&&(e?t.classList.add("abcjs-pushed"):t.classList.remove("abcjs-pushed"))},i.setProgress=function(e,t){var r=i.parent.querySelector(".abcjs-midi-progress-background"),n=i.parent.querySelector(".abcjs-midi-progress-indicator");if(r&&n){var a=r.clientWidth*e;n.style.left=a+"px";var s=i.parent.querySelector(".abcjs-midi-clock");if(s){var o=t*e/1e3,c=Math.floor(o/60),l=Math.floor(o%60),h=l<10?"0"+l:l;s.innerHTML=c+":"+h}}},i.options.afterResume){var p=!1;i.options.ac?p="suspended"!==i.options.ac.state:a()&&(p="suspended"!==a().state),p&&i.options.afterResume()}}},function(e,t,i){var r=i(24),n=i(13),a=i(8);e.exports=function(){var e=this;e.warp=100,e.cursorControl=null,e.visualObj=null,e.timer=null,e.midiBuffer=null,e.options=null,e.currentTempo=null,e.control=null,e.isLooping=!1,e.isStarted=!1,e.isLoaded=!1,e.load=function(t,i,n){n||(n={}),e.control=new r(t,{loopHandler:n.displayLoop?e.toggleLoop:void 0,restartHandler:n.displayRestart?e.restart:void 0,playPromiseHandler:n.displayPlay?e.play:void 0,progressHandler:n.displayProgress?e.randomAccess:void 0,warpHandler:n.displayWarp?e.onWarp:void 0,afterResume:e.init}),e.cursorControl=i},e.setTune=function(t,i,r){return e.isLoaded=!1,e.visualObj=t,e.options=r,e.control&&(e.pause(),e.setProgress(0,1),e.control.resetAll(),e.restart(),e.isStarted=!1),e.isLooping=!1,i?e.go():Promise.resolve({status:"no-audio-context"})},e.go=function(){var t=100*e.visualObj.millisecondsPerMeasure()/e.warp;return e.currentTempo=Math.round(e.visualObj.getBeatsPerMeasure()/t*6e4),e.control.setTempo(e.currentTempo),e.percent=0,e.midiBuffer||(e.midiBuffer=new n),e.midiBuffer.init({visualObj:e.visualObj,options:e.options,millisecondsPerMeasure:t}).then((function(){return e.midiBuffer.prime()})).then((function(){var t=16;return e.cursorControl&&void 0!==e.cursorControl.beatSubdivisions&&parseInt(e.cursorControl.beatSubdivisions,10)>=1&&parseInt(e.cursorControl.beatSubdivisions,10)<=64&&(t=parseInt(e.cursorControl.beatSubdivisions,10)),e.timer=new a(e.visualObj,{beatCallback:e.beatCallback,eventCallback:e.eventCallback,lineEndCallback:e.lineEndCallback,qpm:e.currentTempo,extraMeasuresAtBeginning:e.cursorControl?e.cursorControl.extraMeasuresAtBeginning:void 0,lineEndAnticipation:e.cursorControl?e.cursorControl.lineEndAnticipation:void 0,beatSubdivisions:t}),e.cursorControl&&e.cursorControl.onReady&&"function"==typeof e.cursorControl.onReady&&e.cursorControl.onReady(e),e.isLoaded=!0,Promise.resolve({status:"created"})}))},e.destroy=function(){e.timer&&(e.timer.reset(),e.timer.stop(),e.timer=null),e.midiBuffer&&(e.midiBuffer.stop(),e.midiBuffer=null),e.setProgress(0,1),e.control.resetAll()},e.play=function(){return e.isLoaded?e._play():e.go().then((function(){return e._play()}))},e._play=function(){return e.isStarted=!e.isStarted,e.isStarted?(e.cursorControl&&e.cursorControl.onStart&&"function"==typeof e.cursorControl.onStart&&e.cursorControl.onStart(),e.midiBuffer.start(),e.timer.start(),e.control.pushPlay(!0)):e.pause(),Promise.resolve({status:"ok"})},e.pause=function(){e.timer&&(e.timer.pause(),e.midiBuffer.pause(),e.control.pushPlay(!1))},e.toggleLoop=function(){e.isLooping=!e.isLooping,e.control.pushLoop(e.isLooping)},e.restart=function(){e.timer&&(e.timer.setProgress(0),e.midiBuffer.seek(0))},e.randomAccess=function(t){return e.isLoaded?e._randomAccess(t):e.go().then((function(){return e._randomAccess(t)}))},e._randomAccess=function(t){var i=t.target.classList.contains("abcjs-midi-progress-indicator")?t.target.parentNode:t.target,r=(t.x-i.offsetLeft)/i.offsetWidth;r<0&&(r=0),r>100&&(r=100),e.timer.setProgress(r),e.midiBuffer.seek(r)},e.onWarp=function(t){var i=t.target.value;if(parseInt(i,10)>0){e.warp=parseInt(i,10);var r=e.isStarted,n=e.percent;e.destroy(),e.isStarted=!1,e.go().then((function(){e.setProgress(n,1e3*e.midiBuffer.duration),r&&e.play(),e.timer.setProgress(n),e.midiBuffer.seek(n)}))}},e.setProgress=function(t,i){e.percent=t,e.control.setProgress(t,i)},e.finished=function(){e.timer.reset(),e.isLooping?(e.timer.start(),e.midiBuffer.start()):(e.timer.stop(),e.isStarted&&(e.control.pushPlay(!1),e.isStarted=!1,e.cursorControl&&e.cursorControl.onFinished&&"function"==typeof e.cursorControl.onFinished&&e.cursorControl.onFinished(),e.setProgress(0,1)))},e.beatCallback=function(t,i,r){var n=t/i;e.setProgress(n,r),e.cursorControl&&e.cursorControl.onBeat&&"function"==typeof e.cursorControl.onBeat&&e.cursorControl.onBeat(t,i,r)},e.eventCallback=function(t){t?e.cursorControl&&e.cursorControl.onEvent&&"function"==typeof e.cursorControl.onEvent&&e.cursorControl.onEvent(t):e.finished()},e.lineEndCallback=function(t){e.cursorControl&&e.cursorControl.onLineEnd&&"function"==typeof e.cursorControl.onLineEnd&&e.cursorControl.onLineEnd(t)},e.getUrl=function(){return e.midiBuffer.download()},e.download=function(t){var i=e.getUrl(),r=document.createElement("a");document.body.appendChild(r),r.setAttribute("style","display: none;"),r.href=i,r.download=t||"output.wav",r.click(),window.URL.revokeObjectURL(i),document.body.removeChild(r)}}},function(e,t,i){i(27),window.ABCJS=i(28)},function(e,t){},function(e,t,i){var r=i(29),n=i(30),a=i(9),s={};s.signature="abcjs-basic v"+r,Object.keys(n).forEach((function(e){s[e]=n[e]})),Object.keys(a).forEach((function(e){s[e]=a[e]})),s.renderAbc=i(33),s.TimingCallbacks=i(8);var o=i(13),c=i(14),l=i(22),h=i(23),u=i(24),d=i(15),p=i(6),f=i(7),m=i(55),g=i(25);s.synth={CreateSynth:o,instrumentIndexToName:c,pitchToNoteName:l,SynthController:g,SynthSequence:h,CreateSynthControl:u,registerAudioContext:d,activeAudioContext:p,supportsAudio:f,playEvent:m};var v=i(56);s.Editor=v,e.exports=s},function(e,t){e.exports="5.10.3"},function(e,t,i){i(1),i(0);var r=i(8),n={};!function(){"use strict";var e,t;n.startAnimation=function(i,n,a){function s(e){for(var t=0;t<e.length;t++){var i=e[t];i.classList.contains("abcjs-bar")||(i.style.display="none")}}var o;function c(e){a.hideCurrentMeasure?function(e){s(i.querySelectorAll(e))}(e):a.hideFinishedMeasures&&function(e){o&&s(i.querySelectorAll(o)),o=e}(e)}e&&(e.stop(),e=void 0),a.showCursor&&((t=i.querySelector(".abcjs-cursor"))||((t=document.createElement("DIV")).className="abcjs-cursor cursor",t.style.position="absolute",i.appendChild(t),i.style.position="relative")),(e=new r(n,{qpm:a.bpm,eventCallback:function(i){if(i){if(i.measureStart){var r=".abcjs-l"+(n=i).line+".abcjs-m"+n.measureNumber;r&&c(r)}t&&(t.style.left=i.left+"px",t.style.top=i.top+"px",t.style.width=i.width+"px",t.style.height=i.height+"px")}else e.stop(),e=void 0;var n}})).start()},n.pauseAnimation=function(t){e&&(t?e.pause():e.start())},n.stopAnimation=function(){e&&(e.stop(),e=void 0)}}(),e.exports=n},function(e,t,i){var r=i(0),n=i(11),a=i(12);e.exports=function(e,t,i,s){this.reset=function(e,t,i,r){a.initialize(e,t,i,r),n.initialize(e,t,i,r)},this.reset(e,t,i,s),this.setTitle=function(t){if(i.hasMainTitle)s.addSubtitle(e.translateString(e.stripComment(t)));else{var r=e.translateString(e.theReverser(e.stripComment(t)));i.titlecaps&&(r=r.toUpperCase()),s.addMetaText("title",r),i.hasMainTitle=!0}},this.setMeter=function(r){if("C"===(r=e.stripComment(r)))return!0===i.havent_set_length&&(i.default_length=.125,i.havent_set_length=!1),{type:"common_time"};if("C|"===r)return!0===i.havent_set_length&&(i.default_length=.125,i.havent_set_length=!1),{type:"cut_time"};if("o"===r)return!0===i.havent_set_length&&(i.default_length=.125,i.havent_set_length=!1),{type:"tempus_perfectum"};if("c"===r)return!0===i.havent_set_length&&(i.default_length=.125,i.havent_set_length=!1),{type:"tempus_imperfectum"};if("o."===r)return!0===i.havent_set_length&&(i.default_length=.125,i.havent_set_length=!1),{type:"tempus_perfectum_prolatio"};if("c."===r)return!0===i.havent_set_length&&(i.default_length=.125,i.havent_set_length=!1),{type:"tempus_imperfectum_prolatio"};if(0===r.length||"none"===r.toLowerCase())return!0===i.havent_set_length&&(i.default_length=.125,i.havent_set_length=!1),null;var n=e.tokenize(r,0,r.length);try{var a=function(){var e=function(){var e={value:0,num:""},t=n.shift();for("("===t.token&&(t=n.shift());;){if("number"!==t.type)throw"Expected top number of meter";if(e.value+=parseInt(t.token),e.num+=t.token,0===n.length||"/"===n[0].token)return e;if(")"===(t=n.shift()).token){if(0===n.length||"/"===n[0].token)return e;throw"Unexpected paren in meter"}if("."!==t.token&&"+"!==t.token)throw"Expected top number of meter";if(e.num+=t.token,0===n.length)throw"Expected top number of meter";t=n.shift()}return e}();if(0===n.length)return e;var t=n.shift();if("/"!==t.token)throw"Expected slash in meter";if("number"!==(t=n.shift()).type)throw"Expected bottom number of meter";return e.den=t.token,e.value=e.value/parseInt(e.den),e};if(0===n.length)throw"Expected meter definition in M: line";for(var s={type:"specified",value:[]},o=0;;){var c=a();o+=c.value;var l={num:c.num};if(void 0!==c.den&&(l.den=c.den),s.value.push(l),0===n.length)break}return!0===i.havent_set_length&&(i.default_length=o<.75?.0625:.125,i.havent_set_length=!1),s}catch(e){t(e,r,0)}return null},this.calcTempo=function(e){var t=.25;i.meter&&"specified"===i.meter.type?t=1/parseInt(i.meter.value[0].den):i.origMeter&&"specified"===i.origMeter.type&&(t=1/parseInt(i.origMeter.value[0].den));for(var r=0;r<e.duration;r++)e.duration[r]=t*e.duration[r];return e},this.resolveTempo=function(){i.tempo&&(this.calcTempo(i.tempo),s.metaText.tempo=i.tempo,delete i.tempo)},this.addUserDefinition=function(e,n,a){var s=e.indexOf("=",n);if(-1!==s){var o=r.strip(e.substring(n,s)),c=r.strip(e.substring(s+1));if(1===o.length){-1!=="HIJKLMNOPQRSTUVWXYhijklmnopqrstuvw~".indexOf(o)?0!==c.length?(void 0===i.macros&&(i.macros={}),i.macros[o]=c):t("Missing macro definition",e,n):t("Macro definitions must be H-Y, h-w, or tilde",e,n)}else t("Macro definitions can only be one character",e,n)}else t("Need an = in a macro definition",e,n)},this.setDefaultLength=function(e,t,n){var a=r.gsub(e.substring(t,n)," ","").split("/");if(2===a.length){var s=parseInt(a[0]),o=parseInt(a[1]);o>0&&(i.default_length=s/o,i.havent_set_length=!1)}else 1===a.length&&"1"===a[0]&&(i.default_length=1,i.havent_set_length=!1)};var o={larghissimo:20,adagissimo:24,sostenuto:28,grave:32,largo:40,lento:50,larghetto:60,adagio:68,adagietto:74,andante:80,andantino:88,"marcia moderato":84,"andante moderato":100,moderato:112,allegretto:116,"allegro moderato":120,allegro:126,animato:132,agitato:140,veloce:148,"mosso vivo":156,vivace:164,vivacissimo:172,allegrissimo:176,presto:184,prestissimo:210};this.setTempo=function(r,n,a){try{var s=e.tokenize(r,n,a);if(0===s.length)throw"Missing parameter in Q: field";var c={},l=!0,h=s.shift();if("quote"===h.type&&(c.preString=h.token,h=s.shift(),0===s.length))return o[c.preString.toLowerCase()]&&(c.bpm=o[c.preString.toLowerCase()],c.suppressBpm=!0),{type:"immediate",tempo:c};if("alpha"===h.type&&"C"===h.token){if(0===s.length)throw"Missing tempo after C in Q: field";if("punct"===(h=s.shift()).type&&"="===h.token){if(0===s.length)throw"Missing tempo after = in Q: field";if("number"!==(h=s.shift()).type)throw"Expected number after = in Q: field";c.duration=[1],c.bpm=parseInt(h.token)}else{if("number"!==h.type)throw"Expected number or equal after C in Q: field";if(c.duration=[parseInt(h.token)],0===s.length)throw"Missing = after duration in Q: field";if("punct"!==(h=s.shift()).type||"="!==h.token)throw"Expected = after duration in Q: field";if(0===s.length)throw"Missing tempo after = in Q: field";if("number"!==(h=s.shift()).type)throw"Expected number after = in Q: field";c.bpm=parseInt(h.token)}}else{if("number"!==h.type)throw"Unknown value in Q: field";var u=parseInt(h.token);if(0===s.length||"quote"===s[0].type)c.duration=[1],c.bpm=u;else{if(l=!1,"punct"!==(h=s.shift()).type&&"/"!==h.token)throw"Expected fraction in Q: field";if("number"!==(h=s.shift()).type)throw"Expected fraction in Q: field";var d=parseInt(h.token);for(c.duration=[u/d];s.length>0&&"="!==s[0].token&&"quote"!==s[0].type;){if("number"!==(h=s.shift()).type)throw"Expected fraction in Q: field";if(u=parseInt(h.token),"punct"!==(h=s.shift()).type&&"/"!==h.token)throw"Expected fraction in Q: field";if("number"!==(h=s.shift()).type)throw"Expected fraction in Q: field";d=parseInt(h.token),c.duration.push(u/d)}if("punct"!==(h=s.shift()).type&&"="!==h.token)throw"Expected = in Q: field";if("number"!==(h=s.shift()).type)throw"Expected tempo in Q: field";c.bpm=parseInt(h.token)}}if(0!==s.length&&("quote"===(h=s.shift()).type&&(c.postString=h.token,h=s.shift()),0!==s.length))throw"Unexpected string at end of Q: field";return!1===i.printTempo&&(c.suppress=!0),{type:l?"delaySet":"immediate",tempo:c}}catch(e){return t(e,r,n),{type:"none"}}},this.letter_to_inline_header=function(r,o){var c=e.eatWhiteSpace(r,o);if(o+=c,r.length>=o+5&&"["===r.charAt(o)&&":"===r.charAt(o+2)){var l=r.indexOf("]",o),h=i.iChar+o,u=i.iChar+l+1;switch(r.substring(o,o+3)){case"[I:":var d=n.addDirective(r.substring(o+3,l));return d&&t(d,r,o),[l-o+1+c];case"[M:":var p=this.setMeter(r.substring(o+3,l));return s.hasBeginMusic()&&p?s.appendStartingElement("meter",h,u,p):i.meter=p,[l-o+1+c];case"[K:":var f=a.parseKey(r.substring(o+3,l));return f.foundClef&&s.hasBeginMusic()&&s.appendStartingElement("clef",h,u,i.clef),f.foundKey&&s.hasBeginMusic()&&s.appendStartingElement("key",h,u,a.fixKey(i.clef,i.key)),[l-o+1+c];case"[P:":return s.lines.length<=s.lineNum?i.partForNextLine={title:r.substring(o+3,l),startChar:h,endChar:u}:s.appendElement("part",h,u,{title:r.substring(o+3,l)}),[l-o+1+c];case"[L:":return this.setDefaultLength(r,o+3,l),[l-o+1+c];case"[Q:":if(l>0){var m=this.setTempo(r,o+3,l);return"delaySet"===m.type?s.appendElement("tempo",h,u,this.calcTempo(m.tempo)):"immediate"===m.type&&s.appendElement("tempo",h,u,m.tempo),[l-o+1+c,r.charAt(o+1),r.substring(o+3,l)]}break;case"[V:":if(l>0)return a.parseVoice(r,o+3,l),[l-o+1+c,r.charAt(o+1),r.substring(o+3,l)]}}return[0]},this.letter_to_body_header=function(e,o){if(e.length>=o+3)switch(e.substring(o,o+2)){case"I:":var c=n.addDirective(e.substring(o+2));return c&&t(c,e,o),[e.length];case"M:":var l=this.setMeter(e.substring(o+2));return s.hasBeginMusic()&&l&&s.appendStartingElement("meter",i.iChar+o,i.iChar+e.length,l),[e.length];case"K:":var h=a.parseKey(e.substring(o+2));return h.foundClef&&s.hasBeginMusic()&&s.appendStartingElement("clef",i.iChar+o,i.iChar+e.length,i.clef),h.foundKey&&s.hasBeginMusic()&&s.appendStartingElement("key",i.iChar+o,i.iChar+e.length,a.fixKey(i.clef,i.key)),[e.length];case"P:":return s.hasBeginMusic()&&s.appendElement("part",i.iChar+o,i.iChar+e.length,{title:e.substring(o+2)}),[e.length];case"L:":return this.setDefaultLength(e,o+2,e.length),[e.length];case"Q:":var u=e.indexOf("",o+2);-1===u&&(u=e.length);var d=this.setTempo(e,o+2,u);return"delaySet"===d.type?s.appendElement("tempo",i.iChar+o,i.iChar+e.length,this.calcTempo(d.tempo)):"immediate"===d.type&&s.appendElement("tempo",i.iChar+o,i.iChar+e.length,d.tempo),[u,e.charAt(o),r.strip(e.substring(o+2))];case"V:":return a.parseVoice(e,o+2,e.length),[e.length,e.charAt(o),r.strip(e.substring(o+2))]}return[0]};var c={A:"author",B:"book",C:"composer",D:"discography",F:"url",G:"group",I:"instruction",N:"notes",O:"origin",R:"rhythm",S:"source",W:"unalignedWords",Z:"transcription"};this.parseHeader=function(o){if(r.startsWith(o,"%%")){var l=n.addDirective(o.substring(2));return l&&t(l,o,2),{}}var h=o.indexOf("%");if(h>=0&&(o=o.substring(0,h)),0===(o=o.replace(/\s+$/,"")).length)return{};if(o.length>=2&&":"===o.charAt(1)){var u="";o.indexOf("")>=0&&"w"!==o.charAt(0)&&(u=o.substring(o.indexOf("")+1),o=o.substring(0,o.indexOf("")));var d=c[o.charAt(0)];if(void 0!==d)return"unalignedWords"===d?s.addMetaTextArray(d,n.parseFontChangeLine(e.translateString(e.stripComment(o.substring(2))))):s.addMetaText(d,e.translateString(e.stripComment(o.substring(2)))),{};var p=i.iChar,f=p+o.length;switch(o.charAt(0)){case"H":s.addMetaText("history",e.translateString(e.stripComment(o.substring(2)))),i.is_in_history=!0;break;case"K":this.resolveTempo();var m=a.parseKey(o.substring(2));!i.is_in_header&&s.hasBeginMusic()&&(m.foundClef&&s.appendStartingElement("clef",p,f,i.clef),m.foundKey&&s.appendStartingElement("key",p,f,a.fixKey(i.clef,i.key))),i.is_in_header=!1;break;case"L":this.setDefaultLength(o,2,o.length);break;case"M":i.origMeter=i.meter=this.setMeter(o.substring(2));break;case"P":i.is_in_header?s.addMetaText("partOrder",e.translateString(e.stripComment(o.substring(2)))):i.partForNextLine={title:e.translateString(e.stripComment(o.substring(2))),startChar:p,endChar:f};break;case"Q":var g=this.setTempo(o,2,o.length);"delaySet"===g.type?i.tempo=g.tempo:"immediate"===g.type&&(s.metaText.tempo=g.tempo);break;case"T":this.setTitle(o.substring(2));break;case"U":this.addUserDefinition(o,2,o.length);break;case"V":if(a.parseVoice(o,2,o.length),!i.is_in_header)return{newline:!0};break;case"s":return{symbols:!0};case"w":return{words:!0};case"X":break;case"E":case"m":t("Ignored header",o,0);break;default:return u.length&&(u=""+u),{regular:!0,str:o+u}}return u.length>0?{recurse:!0,str:u}:{}}return{regular:!0,str:o}}}},function(e,t,i){var r=i(0);e.exports=function(){this.skipWhiteSpace=function(e){for(var t=0;t<e.length;t++)if(!this.isWhiteSpace(e.charAt(t)))return t;return e.length};var e=function(e,t){return t>=e.length};this.eatWhiteSpace=function(e,t){for(var i=t;i<e.length;i++)if(!this.isWhiteSpace(e.charAt(i)))return i-t;return i-t},this.getKeyPitch=function(t){var i=this.skipWhiteSpace(t);if(e(t,i))return{len:0};switch(t.charAt(i)){case"A":return{len:i+1,token:"A"};case"B":return{len:i+1,token:"B"};case"C":return{len:i+1,token:"C"};case"D":return{len:i+1,token:"D"};case"E":return{len:i+1,token:"E"};case"F":return{len:i+1,token:"F"};case"G":return{len:i+1,token:"G"}}return{len:0}},this.getSharpFlat=function(e){if("bass"===e)return{len:0};switch(e.charAt(0)){case"#":return{len:1,token:"#"};case"b":return{len:1,token:"b"}}return{len:0}},this.getMode=function(t){var i=function(e,t){for(;t<e.length&&(e.charAt(t)>="a"&&e.charAt(t)<="z"||e.charAt(t)>="A"&&e.charAt(t)<="Z");)t++;return t},r=this.skipWhiteSpace(t);if(e(t,r))return{len:0};var n=t.substring(r,r+3).toLowerCase();switch((n.length>1&&" "===n.charAt(1)||"^"===n.charAt(1)||"_"===n.charAt(1)||"="===n.charAt(1))&&(n=n.charAt(0)),n){case"mix":return{len:i(t,r),token:"Mix"};case"dor":return{len:i(t,r),token:"Dor"};case"phr":return{len:i(t,r),token:"Phr"};case"lyd":return{len:i(t,r),token:"Lyd"};case"loc":return{len:i(t,r),token:"Loc"};case"aeo":return{len:i(t,r),token:"m"};case"maj":case"ion":return{len:i(t,r),token:""};case"min":case"m":return{len:i(t,r),token:"m"}}return{len:0}},this.getClef=function(t,i){var n=t,a=this.skipWhiteSpace(t);if(e(t,a))return{len:0};var s=!1,o=t.substring(a);if(r.startsWith(o,"clef=")&&(s=!0,o=o.substring(5),a+=5),0===o.length&&s)return{len:a+5,warn:"No clef specified: "+n};var c=this.skipWhiteSpace(o);if(e(o,c))return{len:0};c>0&&(a+=c,o=o.substring(c));var l=null;if(r.startsWith(o,"treble"))l="treble";else if(r.startsWith(o,"bass3"))l="bass3";else if(r.startsWith(o,"bass"))l="bass";else if(r.startsWith(o,"tenor"))l="tenor";else if(r.startsWith(o,"alto2"))l="alto2";else if(r.startsWith(o,"alto1"))l="alto1";else if(r.startsWith(o,"alto"))l="alto";else if(!i&&s&&r.startsWith(o,"none"))l="none";else if(r.startsWith(o,"perc"))l="perc";else if(!i&&s&&r.startsWith(o,"C"))l="tenor";else if(!i&&s&&r.startsWith(o,"F"))l="bass";else{if(i||!s||!r.startsWith(o,"G"))return{len:a+5,warn:"Unknown clef specified: "+n};l="treble"}return o=o.substring(l.length),(c=this.isMatch(o,"+8"))>0?l+="+8":(c=this.isMatch(o,"-8"))>0&&(l+="-8"),{len:a+l.length,token:l,explicit:s}},this.getBarLine=function(e,t){switch(e.charAt(t)){case"]":switch(++t,e.charAt(t)){case"|":return{len:2,token:"bar_thick_thin"};case"[":return++t,e.charAt(t)>="1"&&e.charAt(t)<="9"||'"'===e.charAt(t)?{len:2,token:"bar_invisible"}:{len:1,warn:"Unknown bar symbol"};default:return{len:1,token:"bar_invisible"}}break;case":":switch(++t,e.charAt(t)){case":":return{len:2,token:"bar_dbl_repeat"};case"|":switch(++t,e.charAt(t)){case"]":switch(++t,e.charAt(t)){case"|":return++t,":"===e.charAt(t)?{len:5,token:"bar_dbl_repeat"}:{len:3,token:"bar_right_repeat"};default:return{len:3,token:"bar_right_repeat"}}break;case"|":return++t,":"===e.charAt(t)?{len:4,token:"bar_dbl_repeat"}:{len:3,token:"bar_right_repeat"};default:return{len:2,token:"bar_right_repeat"}}break;default:return{len:1,warn:"Unknown bar symbol"}}break;case"[":if(++t,"|"!==e.charAt(t))return e.charAt(t)>="1"&&e.charAt(t)<="9"||'"'===e.charAt(t)?{len:1,token:"bar_invisible"}:{len:0};switch(++t,e.charAt(t)){case":":return{len:3,token:"bar_left_repeat"};case"]":return{len:3,token:"bar_invisible"};default:return{len:2,token:"bar_thick_thin"}}break;case"|":switch(++t,e.charAt(t)){case"]":return{len:2,token:"bar_thin_thick"};case"|":return++t,":"===e.charAt(t)?{len:3,token:"bar_left_repeat"}:{len:2,token:"bar_thin_thin"};case":":for(var i=0;":"===e.charAt(t+i);)i++;return{len:1+i,token:"bar_left_repeat"};default:return{len:1,token:"bar_thin"}}}return{len:0}},this.getTokenOf=function(e,t){for(var i=0;i<e.length;i++)if(t.indexOf(e.charAt(i))<0)return{len:i,token:e.substring(0,i)};return{len:i,token:e}},this.getToken=function(e,t,i){for(var r=t;r<i&&!this.isWhiteSpace(e.charAt(r));)r++;return e.substring(t,r)},this.isMatch=function(t,i){var n=this.skipWhiteSpace(t);return e(t,n)?0:r.startsWith(t.substring(n),i)?n+i.length:0},this.getPitchFromTokens=function(e){var t={};if(t.position={A:5,B:6,C:0,D:1,E:2,F:3,G:4,a:12,b:13,c:7,d:8,e:9,f:10,g:11}[e[0].token],void 0===t.position)return{warn:"Pitch expected. Found: "+e[0].token};for(e.shift();e.length;)switch(e[0].token){case",":t.position-=7,e.shift();break;case"'":t.position+=7,e.shift();break;default:return t}return t},this.getKeyAccidentals2=function(e){for(var t;e.length>0;){var i;if("^"===e[0].token){if(i="sharp",e.shift(),0===e.length)return{accs:t,warn:"Expected note name after "+i};switch(e[0].token){case"^":i="dblsharp",e.shift();break;case"/":i="quartersharp",e.shift()}}else if("="===e[0].token)i="natural",e.shift();else{if("_"!==e[0].token)return{accs:t};if(i="flat",e.shift(),0===e.length)return{accs:t,warn:"Expected note name after "+i};switch(e[0].token){case"_":i="dblflat",e.shift();break;case"/":i="quarterflat",e.shift()}}if(0===e.length)return{accs:t,warn:"Expected note name after "+i};switch(e[0].token.charAt(0)){case"a":case"b":case"c":case"d":case"e":case"f":case"g":case"A":case"B":case"C":case"D":case"E":case"F":case"G":void 0===t&&(t=[]),t.push({acc:i,note:e[0].token.charAt(0)}),1===e[0].token.length?e.shift():e[0].token=e[0].token.substring(1);break;default:return{accs:t,warn:"Expected note name after "+i+" Found: "+e[0].token}}}return{accs:t}},this.getKeyAccidental=function(t){var i={"^":"sharp","^^":"dblsharp","=":"natural",_:"flat",__:"dblflat","_/":"quarterflat","^/":"quartersharp"},r=this.skipWhiteSpace(t);if(e(t,r))return{len:0};var n=null;switch(t.charAt(r)){case"^":case"_":case"=":n=t.charAt(r);break;default:return{len:0}}if(e(t,++r))return{len:1,warn:"Expected note name after accidental"};switch(t.charAt(r)){case"a":case"b":case"c":case"d":case"e":case"f":case"g":case"A":case"B":case"C":case"D":case"E":case"F":case"G":return{len:r+1,token:{acc:i[n],note:t.charAt(r)}};case"^":case"_":case"/":if(n+=t.charAt(r),e(t,++r))return{len:2,warn:"Expected note name after accidental"};switch(t.charAt(r)){case"a":case"b":case"c":case"d":case"e":case"f":case"g":case"A":case"B":case"C":case"D":case"E":case"F":case"G":return{len:r+1,token:{acc:i[n],note:t.charAt(r)}};default:return{len:2,warn:"Expected note name after accidental"}}break;default:return{len:1,warn:"Expected note name after accidental"}}},this.isWhiteSpace=function(e){return" "===e||"\t"===e||""===e},this.getMeat=function(e,t,i){var r=e.indexOf("%",t);for(r>=0&&r<i&&(i=r);t<i&&(" "===e.charAt(t)||"\t"===e.charAt(t)||""===e.charAt(t));)t++;for(;t<i&&(" "===e.charAt(i-1)||"\t"===e.charAt(i-1)||""===e.charAt(i-1));)i--;return{start:t,end:i}};var t=function(e){return e>="A"&&e<="Z"||e>="a"&&e<="z"},i=function(e){return e>="0"&&e<="9"};this.tokenize=function(e,r,n,a){var s=this.getMeat(e,r,n);r=s.start,n=s.end;for(var o,c=[];r<n;){if('"'===e.charAt(r)){for(o=r+1;o<n&&'"'!==e.charAt(o);)o++;c.push({type:"quote",token:e.substring(r+1,o),start:r+1,end:o}),o++}else if(t(e.charAt(r))){if(o=r+1,a)for(;o<n&&!this.isWhiteSpace(e.charAt(o));)o++;else for(;o<n&&t(e.charAt(o));)o++;c.push({type:"alpha",token:e.substring(r,o),continueId:i(e.charAt(o)),start:r,end:o}),r=o+1}else if("."===e.charAt(r)&&i(e.charAt(o+1))){o=r+1;for(var l;o<n&&i(e.charAt(o));)o++;l=parseFloat(e.substring(r,o)),c.push({type:"number",token:e.substring(r,o),intt:null,floatt:l,continueId:t(e.charAt(o)),start:r,end:o}),r=o+1}else if(i(e.charAt(r))||"-"===e.charAt(r)&&i(e.charAt(o+1))){o=r+1;for(var h,u=null;o<n&&i(e.charAt(o));)o++;if("."===e.charAt(o)&&i(e.charAt(o+1)))for(o++;o<n&&i(e.charAt(o));)o++;else u=parseInt(e.substring(r,o));h=parseFloat(e.substring(r,o)),c.push({type:"number",token:e.substring(r,o),intt:u,floatt:h,continueId:t(e.charAt(o)),start:r,end:o}),r=o+1}else" "===e.charAt(r)||"\t"===e.charAt(r)?o=r+1:(c.push({type:"punct",token:e.charAt(r),start:r,end:r+1}),o=r+1);r=o}return c},this.getVoiceToken=function(e,t,i){for(var r=t;r<i&&this.isWhiteSpace(e.charAt(r))||"="===e.charAt(r);)r++;if('"'===e.charAt(r)){var n=e.indexOf('"',r+1);return-1===n||n>=i?{len:1,err:"Missing close quote"}:{len:n-t+1,token:this.translateString(e.substring(r+1,n))}}for(var a=r;a<i&&!this.isWhiteSpace(e.charAt(a))&&"="!==e.charAt(a);)a++;return{len:a-t+1,token:e.substring(r,a)}};var n={"`a":"à","'a":"á","^a":"â","~a":"ã",'"a':"ä",oa:"å",aa:"å","=a":"ā",ua:"ă",";a":"ą","`e":"è","'e":"é","^e":"ê",'"e':"ë","=e":"ē",ue:"ĕ",";e":"ę",".e":"ė","`i":"ì","'i":"í","^i":"î",'"i':"ï","=i":"ī",ui:"ĭ",";i":"į","`o":"ò","'o":"ó","^o":"ô","~o":"õ",'"o':"ö","=o":"ō",uo:"ŏ","/o":"ø","`u":"ù","'u":"ú","^u":"û","~u":"ũ",'"u':"ü",ou:"ů","=u":"ū",uu:"ŭ",";u":"ų","`A":"À","'A":"Á","^A":"Â","~A":"Ã",'"A':"Ä",oA:"Å",AA:"Å","=A":"Ā",uA:"Ă",";A":"Ą","`E":"È","'E":"É","^E":"Ê",'"E':"Ë","=E":"Ē",uE:"Ĕ",";E":"Ę",".E":"Ė","`I":"Ì","'I":"Í","^I":"Î","~I":"Ĩ",'"I':"Ï","=I":"Ī",uI:"Ĭ",";I":"Į",".I":"İ","`O":"Ò","'O":"Ó","^O":"Ô","~O":"Õ",'"O':"Ö","=O":"Ō",uO:"Ŏ","/O":"Ø","`U":"Ù","'U":"Ú","^U":"Û","~U":"Ũ",'"U':"Ü",oU:"Ů","=U":"Ū",uU:"Ŭ",";U":"Ų",ae:"æ",AE:"Æ",oe:"œ",OE:"Œ",ss:"ß","'c":"ć","^c":"ĉ",uc:"č",cc:"ç",".c":"ċ",cC:"Ç","'C":"Ć","^C":"Ĉ",uC:"Č",".C":"Ċ","~N":"Ñ","~n":"ñ","=s":"š",vs:"š",DH:"Ð",dh:"ð",HO:"Ő",Ho:"ő",HU:"Ű",Hu:"ű","'Y":"Ý","'y":"ý","^Y":"Ŷ","^y":"ŷ",'"Y':"Ÿ",'"y':"ÿ",vS:"Š",vZ:"Ž",vz:"ž"},a={"#":"♯",b:"♭","=":"♮"},s={201:"♯",202:"♭",203:"♮",241:"¡",242:"¢",252:"a",262:"2",272:"o",302:"Â",312:"Ê",322:"Ò",332:"Ú",342:"â",352:"ê",362:"ò",372:"ú",243:"£",253:"«",263:"3",273:"»",303:"Ã",313:"Ë",323:"Ó",333:"Û",343:"ã",353:"ë",363:"ó",373:"û",244:"¤",254:"¬",264:"  ́",274:"1⁄4",304:"Ä",314:"Ì",324:"Ô",334:"Ü",344:"ä",354:"ì",364:"ô",374:"ü",245:"¥",255:"-",265:"μ",275:"1⁄2",305:"Å",315:"Í",325:"Õ",335:"Ý",345:"å",355:"í",365:"õ",375:"ý",246:"¦",256:"®",266:"¶",276:"3⁄4",306:"Æ",316:"Î",326:"Ö",336:"Þ",346:"æ",356:"î",366:"ö",376:"þ",247:"§",257:" ̄",267:"·",277:"¿",307:"Ç",317:"Ï",327:"×",337:"ß",347:"ç",357:"ï",367:"÷",377:"ÿ",250:" ̈",260:"°",270:" ̧",300:"À",310:"È",320:"Ð",330:"Ø",340:"à",350:"è",360:"ð",370:"ø",251:"©",261:"±",271:"1",301:"Á",311:"É",321:"Ñ",331:"Ù",341:"á",351:"é",361:"ñ",371:"ù"};this.translateString=function(e){var t=e.split("\\");if(1===t.length)return e;var i=null;return r.each(t,(function(e){if(null===i)i=e;else{var t=n[e.substring(0,2)];void 0!==t?i+=t+e.substring(2):void 0!==(t=s[e.substring(0,3)])?i+=t+e.substring(3):(t=a[e.substring(0,1)],i+=void 0!==t?t+e.substring(1):"\\"+e)}})),i},this.getNumber=function(e,t){for(var i=0;t<e.length;)switch(e.charAt(t)){case"0":i*=10,t++;break;case"1":i=10*i+1,t++;break;case"2":i=10*i+2,t++;break;case"3":i=10*i+3,t++;break;case"4":i=10*i+4,t++;break;case"5":i=10*i+5,t++;break;case"6":i=10*i+6,t++;break;case"7":i=10*i+7,t++;break;case"8":i=10*i+8,t++;break;case"9":i=10*i+9,t++;break;default:return{num:i,index:t}}return{num:i,index:t}},this.getFraction=function(e,t){var i=1,r=1;if("/"!==e.charAt(t)){var n=this.getNumber(e,t);i=n.num,t=n.index}if("/"===e.charAt(t)){if(t++,"/"===e.charAt(t)){for(var a=.5;"/"===e.charAt(t++);)a/=2;return{value:i*a,index:t-1}}var s=t,o=this.getNumber(e,t);0===o.num&&s===t&&(o.num=2),0!==o.num&&(r=o.num),t=o.index}return{value:i/r,index:t}},this.theReverser=function(e){return r.endsWith(e,", The")?"The "+e.substring(0,e.length-5):r.endsWith(e,", A")?"A "+e.substring(0,e.length-3):e},this.stripComment=function(e){var t=e.indexOf("%");return t>=0?r.strip(e.substring(0,t)):r.strip(e)},this.getInt=function(e){var t=parseInt(e);if(isNaN(t))return{digits:0};var i=""+t;return{value:t,digits:e.indexOf(i)+i.length}},this.getFloat=function(e){var t=parseFloat(e);if(isNaN(t))return{digits:0};var i=""+t;return{value:t,digits:e.indexOf(i)+i.length}},this.getMeasurement=function(e){if(0===e.length)return{used:0};var t=1,i="";if("-"===e[0].token)e.shift(),i="-",t++;else if("number"!==e[0].type)return{used:0};if(i+=e.shift().token,0===e.length)return{used:1,value:parseInt(i)};var r=e.shift();if("."===r.token){if(t++,0===e.length)return{used:t,value:parseInt(i)};if("number"===e[0].type&&(i=i+"."+(r=e.shift()).token,t++,0===e.length))return{used:t,value:parseFloat(i)};r=e.shift()}switch(r.token){case"pt":return{used:t+1,value:parseFloat(i)};case"cm":return{used:t+1,value:parseFloat(i)/2.54*72};case"in":return{used:t+1,value:72*parseFloat(i)};default:return e.unshift(r),{used:t,value:parseFloat(i)}}return{used:0}};var o=function(e){for(;-1!==e.indexOf("\\n");)e=e.replace("\\n","\n");return e};this.getBrackettedSubstring=function(e,t,i,r){for(var n=r||e.charAt(t),a=t+1;a<e.length&&e.charAt(a)!==n;)++a;return e.charAt(a)===n?[a-t+1,o(e.substring(t+1,a)),!0]:((a=t+i)>e.length-1&&(a=e.length-1),[a-t+1,o(e.substring(t+1,a)),!1])}}},function(e,t,i){var r=i(9),n=i(18),a=i(19),s=i(10),o=i(17),c={};function l(){var e=window.innerWidth;for(var t in c)if(c.hasOwnProperty(t)){var i=c[t];e-=2*i.offsetLeft,i.style.width=e+"px"}}function h(e,t,i,r){i.viewportHorizontal?(e.innerHTML='<div class="abcjs-inner"></div>',i.scrollHorizontal?(e.style.overflowX="auto",e.style.overflowY="hidden"):e.style.overflow="hidden",c[e.id]=e,e=e.children[0]):i.viewportVertical?(e.innerHTML='<div class="abcjs-inner scroll-amount"></div>',e.style.overflowX="hidden",e.style.overflowY="auto",e=e.children[0]):e.innerHTML="";var n=new a(e,i);(n.engraveABC(t,r),t.engraver=n,i.viewportVertical||i.viewportHorizontal)&&(e.parentNode.style.width=e.style.width)}function u(e,t,i,r){function a(e){var t=new n;return t.formatting=e.formatting,t.media=e.media,t.version=e.version,t.metaText={},t.lines=[],t}for(var s,o=[],c=0;c<t.lines.length;c++){var l=t.lines[c];s||(s=a(t)),0===c&&(s.metaText.tempo=t.metaText.tempo,s.metaText.title=t.metaText.title,s.metaText.header=t.metaText.header,s.metaText.rhythm=t.metaText.rhythm,s.metaText.origin=t.metaText.origin,s.metaText.composer=t.metaText.composer,s.metaText.author=t.metaText.author,s.metaText.partOrder=t.metaText.partOrder),s.lines.push(l),l.staff&&(o.push(s),s=void 0)}if(s)for(var u=o[o.length-1],d=0;d<s.lines.length;d++)u.lines.push(s.lines[d]);(s=o[o.length-1]).metaText.unalignedWords=t.metaText.unalignedWords,s.metaText.book=t.metaText.book,s.metaText.source=t.metaText.source,s.metaText.discography=t.metaText.discography,s.metaText.notes=t.metaText.notes,s.metaText.transcription=t.metaText.transcription,s.metaText.history=t.metaText.history,s.metaText["abc-copyright"]=t.metaText["abc-copyright"],s.metaText["abc-creator"]=t.metaText["abc-creator"],s.metaText["abc-edited-by"]=t.metaText["abc-edited-by"],s.metaText.footer=t.metaText.footer;var p={};for(var f in i)i.hasOwnProperty(f)&&(p[f]=i[f]);var m=p.paddingtop,g=p.paddingbottom;e.innerHTML="";for(var v=0;v<o.length;v++){var b=document.createElement("div");e.appendChild(b),0===v?(p.paddingtop=m,p.paddingbottom=-20):v===o.length-1?(p.paddingtop=10,p.paddingbottom=g):(p.paddingtop=10,p.paddingbottom=-20),h(b,o[v],p,r),0===v?t.engraver=o[v].engraver:t.engraver.staffgroups?o[v].engraver.staffgroups.length>0&&t.engraver.staffgroups.push(o[v].engraver.staffgroups[0]):t.engraver.staffgroups=o[v].engraver.staffgroups}}window.addEventListener("resize",l),window.addEventListener("orientationChange",l);e.exports=function(e,t,i,n,c){var l,d={};if(i)for(l in i)i.hasOwnProperty(l)&&(d[l]=i[l]);if(n)for(l in n)n.hasOwnProperty(l)&&("listener"===l?n[l].highlight&&(d.clickListener=n[l].highlight):d[l]=n[l]);if(c)for(l in c)c.hasOwnProperty(l)&&(d[l]=c[l]);return r.renderEngine((function(e,t,i,r){return d.afterParsing&&d.afterParsing(t,i,r),d.wrap&&d.staffwidth?t=function(e,t,i,r,n){var c=new a(e,n),l=c.getMeasureWidths(t),d=o.calcLineWraps(t,l,r,n,s,c);!n.oneSvgPerLine||d.tune.lines.length<2?h(e,d.tune,d.revisedParams,i):u(e,d.tune,d.revisedParams,i);return d.tune.explanation=d.explanation,d.tune}(e,t,i,r,d):(!d.oneSvgPerLine||t.lines.length<2?h(e,t,d,i):u(e,t,d,i),null)}),e,t,d)}},function(e,t,i){var r,n=i(4),a=i(35),s=i(36),o=i(37),c=i(38),l=i(39),h=i(40),u=i(43),d=i(3),p=i(2),f=i(1),m=i(44),g=i(45),v=i(20),b=i(46),y=i(47),w=i(0);!function(){"use strict";var e=function(e){var t=0;return e.duration&&(t=e.duration),t},t=!1,i={rest:{0:"rests.whole",1:"rests.half",2:"rests.quarter",3:"rests.8th",4:"rests.16th",5:"rests.32nd",6:"rests.64th",7:"rests.128th",multi:"rests.multimeasure"},note:{"-1":"noteheads.dbl",0:"noteheads.whole",1:"noteheads.half",2:"noteheads.quarter",3:"noteheads.quarter",4:"noteheads.quarter",5:"noteheads.quarter",6:"noteheads.quarter",7:"noteheads.quarter",nostem:"noteheads.quarter"},rhythm:{"-1":"noteheads.slash.whole",0:"noteheads.slash.whole",1:"noteheads.slash.whole",2:"noteheads.slash.quarter",3:"noteheads.slash.quarter",4:"noteheads.slash.quarter",5:"noteheads.slash.quarter",6:"noteheads.slash.quarter",7:"noteheads.slash.quarter",nostem:"noteheads.slash.nostem"},x:{"-1":"noteheads.indeterminate",0:"noteheads.indeterminate",1:"noteheads.indeterminate",2:"noteheads.indeterminate",3:"noteheads.indeterminate",4:"noteheads.indeterminate",5:"noteheads.indeterminate",6:"noteheads.indeterminate",7:"noteheads.indeterminate",nostem:"noteheads.indeterminate"},harmonic:{"-1":"noteheads.harmonic.quarter",0:"noteheads.harmonic.quarter",1:"noteheads.harmonic.quarter",2:"noteheads.harmonic.quarter",3:"noteheads.harmonic.quarter",4:"noteheads.harmonic.quarter",5:"noteheads.harmonic.quarter",6:"noteheads.harmonic.quarter",7:"noteheads.harmonic.quarter",nostem:"noteheads.harmonic.quarter"},uflags:{3:"flags.u8th",4:"flags.u16th",5:"flags.u32nd",6:"flags.u64th"},dflags:{3:"flags.d8th",4:"flags.d16th",5:"flags.d32nd",6:"flags.d64th"}};function k(e,t){var i=e[t];if("note"!==i.el_type||!i.startBeam||i.endBeam)return{count:1,elem:i};for(var r=[];t<e.length&&"note"===e[t].el_type&&(r.push(e[t]),!e[t].endBeam);)t++;return{count:r.length,elem:r}}function x(e){if(e.pitches){S(e);for(var t=0,i=0;i<e.pitches.length;i++)t+=e.pitches[i].verticalPos;e.averagepitch=t/e.pitches.length,e.minpitch=e.pitches[0].verticalPos,e.maxpitch=e.pitches[e.pitches.length-1].verticalPos}}(r=function(e,t,i){this.decoration=new h,this.renderer=e,this.tuneNumber=t,this.isBagpipes=i.bagpipes,this.flatBeams=i.flatbeams,this.reset()}).prototype.reset=function(){this.slurs={},this.ties=[],this.voiceScale=1,this.slursbyvoice={},this.tiesbyvoice={},this.endingsbyvoice={},this.scaleByVoice={},this.tripletmultiplier=1,this.abcline=void 0,this.accidentalSlot=void 0,this.accidentalshiftx=void 0,this.dotshiftx=void 0,this.hasVocals=!1,this.minY=void 0,this.partstartelem=void 0,this.startlimitelem=void 0,this.stemdir=void 0},r.prototype.setStemHeight=function(e){this.stemHeight=e/f.STEP},r.prototype.getCurrentVoiceId=function(e,t){return"s"+e+"v"+t},r.prototype.pushCrossLineElems=function(e,t){this.slursbyvoice[this.getCurrentVoiceId(e,t)]=this.slurs,this.tiesbyvoice[this.getCurrentVoiceId(e,t)]=this.ties,this.endingsbyvoice[this.getCurrentVoiceId(e,t)]=this.partstartelem,this.scaleByVoice[this.getCurrentVoiceId(e,t)]=this.voiceScale},r.prototype.popCrossLineElems=function(e,t){this.slurs=this.slursbyvoice[this.getCurrentVoiceId(e,t)]||{},this.ties=this.tiesbyvoice[this.getCurrentVoiceId(e,t)]||[],this.partstartelem=this.endingsbyvoice[this.getCurrentVoiceId(e,t)],this.voiceScale=this.scaleByVoice[this.getCurrentVoiceId(e,t)],void 0===this.voiceScale&&(this.voiceScale=1)},r.prototype.containsLyrics=function(e){for(var t=0;t<e.length;t++)for(var i=0;i<e[t].voices.length;i++)for(var r=0;r<e[t].voices[i].length;r++){var n=e[t].voices[i][r];if(n.lyric)return void(n.positioning&&"below"!==n.positioning.vocalPosition||(this.hasVocals=!0))}},r.prototype.createABCLine=function(e,i){this.minY=2,this.containsLyrics(e);var r=new m;this.tempoSet=!1;for(var n=0;n<e.length;n++)t&&this.restoreState(),t=!1,this.createABCStaff(r,e[n],i,n);return r},r.prototype.createABCStaff=function(e,t,i,r){for(var n=0;n<t.voices.length;n++){var a=new y(n,t.voices.length);0===n?(a.barfrom="start"===t.connectBarLines||"continue"===t.connectBarLines,a.barto="continue"===t.connectBarLines||"end"===t.connectBarLines):a.duplicate=!0,t.title&&t.title[n]&&(a.header=t.title[n]);var h=o(t.clef,this.tuneNumber);h&&(0===n&&t.barNumber&&this.addMeasureNumber(t.barNumber,h),a.addChild(h));var u=c(t.key,this.tuneNumber);if(u&&(a.addChild(u),this.startlimitelem=u),t.meter){"specified"===t.meter.type?this.measureLength=t.meter.value[0].num/t.meter.value[0].den:this.measureLength=1;var d=l(t.meter,this.tuneNumber);a.addChild(d),this.startlimitelem=d}a.duplicate&&(a.children=[]);var p=t.clef.stafflines||0===t.clef.stafflines?t.clef.stafflines:5;e.addVoice(a,r,p);var f=1===p;this.createABCVoice(t.voices[n],i,r,n,f,a),e.setStaffLimits(a),"start"===t.brace?e.brace=new s(1,!0):"end"===t.brace&&e.brace?e.brace.increaseStavesIncluded():"continue"===t.brace&&e.brace&&e.brace.increaseStavesIncluded()}},r.prototype.createABCVoice=function(e,i,r,a,s,o){this.popCrossLineElems(r,a),this.stemdir=this.isBagpipes?"down":null,this.abcline=e,this.partstartelem&&(this.partstartelem=new u("",null,null),o.addOther(this.partstartelem));var c=o.voicetotal<2?-1:o.voicenumber;for(var l in this.slurs)this.slurs.hasOwnProperty(l)&&(this.slurs[l]=new v({force:this.slurs[l].force,voiceNumber:c,stemDir:this.slurs[l].stemDir}),t&&this.slurs[l].setHint(),o.addOther(this.slurs[l]));for(var h=0;h<this.ties.length;h++)this.ties[h]=new v({force:this.ties[h].force,stemDir:this.ties[h].stemDir,voiceNumber:c}),t&&this.ties[h].setHint(),o.addOther(this.ties[h]);for(var d=0;d<this.abcline.length;d++)x(this.abcline[d]),this.minY=Math.min(this.abcline[d].minpitch,this.minY);for(var p=0===r,f=0;f<this.abcline.length;){var m=k(this.abcline,f),b=this.createABCElement(p,s,o,m.elem);if(b)for(h=0;h<b.length;h++){if(!this.tempoSet&&i&&!i.suppress){this.tempoSet=!0;var y=new n(m.elem,0,0,"tempo",this.tuneNumber,{});y.addChild(new g(i,this.tuneNumber,A)),o.addChild(y)}o.addChild(b[h])}f+=m.count}this.pushCrossLineElems(r,a)},r.prototype.saveState=function(){this.tiesSave=w.cloneArray(this.ties),this.slursSave=w.cloneHashOfHash(this.slurs),this.slursbyvoiceSave=w.cloneHashOfHash(this.slursbyvoice),this.tiesbyvoiceSave=w.cloneHashOfArrayOfHash(this.tiesbyvoice)},r.prototype.restoreState=function(){this.ties=w.cloneArray(this.tiesSave),this.slurs=w.cloneHashOfHash(this.slursSave),this.slursbyvoice=w.cloneHashOfHash(this.slursbyvoiceSave),this.tiesbyvoice=w.cloneHashOfArrayOfHash(this.tiesbyvoiceSave)},r.prototype.createABCElement=function(e,i,r,a){var s=[];switch(a.el_type){case void 0:s=this.createBeam(i,r,a);break;case"note":s[0]=this.createNote(a,!1,i,r),this.triplet&&this.triplet.isClosed()&&(r.addOther(this.triplet),this.triplet=null,this.tripletmultiplier=1);break;case"bar":s[0]=this.createBarLine(r,a,e),r.duplicate&&s.length>0&&(s[0].invisible=!0);break;case"meter":s[0]=l(a,this.tuneNumber),this.startlimitelem=s[0],r.duplicate&&s.length>0&&(s[0].invisible=!0);break;case"clef":if(s[0]=o(a,this.tuneNumber),!s[0])return null;r.duplicate&&s.length>0&&(s[0].invisible=!0);break;case"key":var h=c(a,this.tuneNumber);h&&(s[0]=h,this.startlimitelem=s[0]),r.duplicate&&s.length>0&&(s[0].invisible=!0);break;case"stem":this.stemdir=a.direction;break;case"part":var u=new n(a,0,0,"part",this.tuneNumber),d=this.renderer.getTextSize(a.title,"partsfont","part");u.addChild(new p(a.title,0,0,void 0,{type:"part",height:d.height/f.STEP})),s[0]=u;break;case"tempo":var m=new n(a,0,0,"tempo",this.tuneNumber);m.addChild(new g(a,this.tuneNumber,A)),s[0]=m;break;case"style":"normal"===a.head?delete this.style:this.style=a.head;break;case"hint":t=!0,this.saveState();break;case"midi":break;case"scale":this.voiceScale=a.size;break;default:var v=new n(a,0,0,"unsupported",this.tuneNumber);v.addChild(new p("element type "+a.el_type,0,0,void 0,{type:"debug"})),s[0]=v}return s},r.prototype.calcBeamDir=function(e,t,i){if(this.stemdir)return this.stemdir;for(var r=new a(this.stemHeight*this.voiceScale,this.stemdir,this.flatBeams),n=0;n<i.length;n++)r.add({abcelem:i[n]});return r.calcDir()?"up":"down"},r.prototype.createBeam=function(e,i,r){var n=[],s=this.calcBeamDir(e,i,r),o=new a(this.stemHeight*this.voiceScale,s,this.flatBeams);t&&o.setHint();var c=this.stemdir;this.stemdir=s;for(var l=0;l<r.length;l++){var h=r[l],u=this.createNote(h,!0,e,i);n.push(u),o.add(u),this.triplet&&this.triplet.isClosed()&&(i.addOther(this.triplet),this.triplet=null,this.tripletmultiplier=1)}return this.stemdir=c,i.addBeam(o),n};var S=function(e){var t;do{t=!0;for(var i=0;i<e.pitches.length-1;i++)if(e.pitches[i].pitch>e.pitches[i+1].pitch){t=!1;var r=e.pitches[i];e.pitches[i]=e.pitches[i+1],e.pitches[i+1]=r}}while(!t)},_=function(e,t,i,r,n,a,s,o,c){for(var l=i;l>11;l--)l%2!=0||r||e.addChild(new p(null,o,(n+4)*c,l,{type:"ledger"}));for(l=t;l<1;l++)l%2!=0||r||e.addChild(new p(null,o,(n+4)*c,l,{type:"ledger"}));for(l=0;l<a.length;l++){var h=n;"down"===s&&(h=-h),e.addChild(new p(null,h+o,(n+4)*c,a[l],{type:"ledger"}))}};function T(e,t){for(var i=0;i<e.length;i++)if(JSON.stringify(e[i])===JSON.stringify(t))return;e.push(t)}r.prototype.addGraceNotes=function(e,r,n,s,o,c,l){var h,u=null;e.gracenotes.length>1&&(u=new a(.7*o,"grace",c),t&&u.setHint(),u.mainNote=n);var f,m=[];for(f=e.gracenotes.length-1;f>=0;f--)l+=10,m[f]=l,e.gracenotes[f].accidental&&(l+=7);for(f=0;f<e.gracenotes.length;f++){var g=e.gracenotes[f].verticalPos;h=u?null:i.uflags[c?5:3];var b=A(n,"noteheads.quarter",e.gracenotes[f],"up",-m[f],-m[f],h,0,0,.6*this.voiceScale,[],!1);b.notehead.highestVert=b.notehead.pitch+.7*o;var y=b.notehead;if(this.addSlursAndTies(n,e.gracenotes[f],y,r,"up",!0),n.addExtra(y),e.gracenotes[f].acciaccatura){var w=e.gracenotes[f].verticalPos+4.2,k=u?5:6;n.addRight(new p("flags.ugrace",-m[f]+k,0,w,{scalex:.6,scaley:.6}))}if(u){var x=e.gracenotes[f].duration/2;c&&(x/=2);var S={heads:[y],abcelem:{averagepitch:g,minpitch:g,maxpitch:g,duration:x}};u.add(S)}else{var T=g+1/3*.6,C=g+4.2,N=y.dx+y.w;n.addExtra(new p(null,N,0,T,{type:"stem",pitch2:C,linewidth:-.6}))}if(_(n,g,g,!1,d.getSymbolWidth("noteheads.quarter"),[],!0,y.dx-1,.6),0===f&&!c&&(!e.rest||"spacer"!==e.rest.type&&"invisible"!==e.rest.type)){1===e.gracenotes.length&&(y.pitch,s.pitch);r.addOther(new v({anchor1:y,anchor2:s,isGrace:!0}))}}return u&&r.addBeam(u),l},r.prototype.addNoteToAbcElement=function(t,r,n,a,s,o,c,l,h){var u,f,m,g,v,b=0,y=0,w=0,k=[],x=[],S=0,_=r.averagepitch>=6?"down":"up";for(a&&(_=a),(s=r.style?r.style:s)&&"normal"!==s||(s="note"),(g=o?i[s].nostem:i[s][-c])||console.log("noteSymbol:",s,c,o),v="down"===_?r.pitches.length-2:1;"down"===_?v>=0:v<r.pitches.length;v="down"===_?v-1:v+1){var C=r.pitches["down"===_?v+1:v-1],N=r.pitches[v],E="down"===_?C.pitch-N.pitch:N.pitch-C.pitch;E<=1&&!C.printer_shift&&(N.printer_shift=E?"different":"same",(N.verticalPos>11||N.verticalPos<1)&&k.push(N.verticalPos-N.verticalPos%2),"down"===_?y=d.getSymbolWidth(g)+2:b=d.getSymbolWidth(g)+2)}var M=r.pitches.length;for(v=0;v<r.pitches.length;v++){var B,P;if(!l)B="down"===_&&0!==v||"up"===_&&v!==M-1?null:i["down"===_?"dflags":"uflags"][-c];if(P=r.pitches[v].style?i[r.pitches[v].style][-c]:g,r.pitches[v].highestVert=r.pitches[v].verticalPos,("up"===a||"up"===_)&&0===v||("down"===a||"down"===_)&&v===M-1){if((r.startSlur||1===M)&&(r.pitches[v].highestVert=r.pitches[M-1].verticalPos,e(r)<1&&("up"===a||"up"===_)&&(r.pitches[v].highestVert+=6)),r.startSlur)for(r.pitches[v].startSlur||(r.pitches[v].startSlur=[]),m=0;m<r.startSlur.length;m++)T(r.pitches[v].startSlur,r.startSlur[m]);if(r.endSlur)for(r.pitches[v].highestVert=r.pitches[M-1].verticalPos,e(r)<1&&("up"===a||"up"===_)&&(r.pitches[v].highestVert+=6),r.pitches[v].endSlur||(r.pitches[v].endSlur=[]),m=0;m<r.endSlur.length;m++)T(r.pitches[v].endSlur,r.endSlur[m])}var L=!l&&c<=-1,H=A(t,P,r.pitches[v],_,0,-y,B,n,b,this.voiceScale,x,!a);S=Math.max(d.getSymbolWidth(P),S),t.extraw-=H.extraLeft,(u=H.notehead)&&(this.addSlursAndTies(t,r.pitches[v],u,h,L?_:null,!1),r.gracenotes&&r.gracenotes.length>0&&(u.bottom=u.bottom-1),t.addHead(u)),y+=H.accidentalshiftx,w=Math.max(w,H.dotshiftx)}if(L){var z=7*this.voiceScale,I="down"===_?r.minpitch-z:r.minpitch+1/3;I>6&&!a&&(I=6);var D="down"===_?r.maxpitch-1/3:r.maxpitch+z;D<6&&!a&&(D=6);var O="down"===_||0===t.heads.length?0:t.heads[0].w,F="down"===_?1:-1;"noteheads.slash.quarter"===u.c&&("down"===_?D-=1:I+=1),t.addExtra(new p(null,O,0,I,{type:"stem",pitch2:D,linewidth:F})),f=Math.min(I,D)}return{noteHead:u,roomTaken:y,roomTakenRight:w,min:f,additionalLedgers:k,dir:_,symbolWidth:S}},r.prototype.addLyric=function(e,t){var i="";w.each(t.lyric,(function(e){var t=" "===e.divider?"":e.divider;i+=e.syllable+t+"\n"}));var r=this.renderer.getTextSize(i,"vocalfont","lyric"),n=t.positioning?t.positioning.vocalPosition:"below";e.addCentered(new p(i,0,r.width,void 0,{type:"lyric",position:n,height:r.height/f.STEP}))},r.prototype.addChord=function(e,t,i,r){for(var n=0;n<t.chord.length;n++){var a,s=0,o=this.renderer.getTextSize(t.chord[n].name,"annotationfont","annotation"),c=o.width,l=o.height/f.STEP;switch(t.chord[n].position){case"left":s=-(i+=c+7),a=t.averagepitch,e.addExtra(new p(t.chord[n].name,s,c+4,a,{type:"text",height:l}));break;case"right":s=r+=4,a=t.averagepitch,e.addRight(new p(t.chord[n].name,s,c+4,a,{type:"text",height:l}));break;case"below":e.addRight(new p(t.chord[n].name,0,c+8,void 0,{type:"text",position:"below",height:l}));break;case"above":e.addRight(new p(t.chord[n].name,0,c+8,void 0,{type:"text",height:l}));break;default:if(t.chord[n].rel_position){var h=t.chord[n].rel_position.y+3*f.STEP;e.addChild(new p(t.chord[n].name,s+t.chord[n].rel_position.x,0,t.minpitch+h/f.STEP,{type:"text",height:l}))}else{var u="above";t.positioning&&t.positioning.chordPosition&&(u=t.positioning.chordPosition),l=(o=this.renderer.getTextSize(t.chord[n].name,"gchordfont","chord")).height/f.STEP,c=o.width,e.addCentered(new p(t.chord[n].name,s,c,void 0,{type:"chord",position:u,height:l}))}}}return{roomTaken:i,roomTakenRight:r}},r.prototype.createNote=function(r,a,s,o){var c,l=null,h=0,u=0,f=0,m=[],g=e(r),v=!1;0===g&&(v=!0,g=.25,a=!0);for(var y=Math.floor(Math.log(g)/Math.log(2)),w=0,k=Math.pow(2,y),x=k/2;k<g;w++,k+=x,x/=2);r.startTriplet&&(this.tripletmultiplier=r.tripletMultiplier);var S=g*this.tripletmultiplier;r.rest&&"multimeasure"===r.rest.type&&(S=1);var T=r.rest?"rest":"note",C=new n(r,S,1,T,this.tuneNumber,{durationClassOveride:r.duration*this.tripletmultiplier});if(t&&C.setHint(),r.rest){this.measureLength===g&&"invisible"!==r.rest.type&&"spacer"!==r.rest.type&&(r.rest.type="whole");var N=function(e,t,r,n,a,s,o,c,l){var h,u,f,m,g=7;switch(a&&("down"===s&&(g=3),"up"===s&&(g=11)),o&&(g=r<.5?7:r<1?7:5),t.rest.type){case"whole":h=i.rest[0],t.averagepitch=g,t.minpitch=g,t.maxpitch=g,n=0;break;case"rest":h="rhythm"===t.style?i.rhythm[-c]:i.rest[-c],t.averagepitch=g,t.minpitch=g,t.maxpitch=g;break;case"invisible":case"spacer":h="",t.averagepitch=g,t.minpitch=g,t.maxpitch=g;break;case"multimeasure":h=i.rest.multi,t.averagepitch=g,t.minpitch=g,t.maxpitch=g,n=0;var v=d.getSymbolWidth(h);e.addHead(new p(h,-v,2*v,7));var b=new p(""+t.duration,0,v,16,{type:"multimeasure-text"});e.addExtra(b)}if("multimeasure"!==t.rest.type){var y=A(e,h,{verticalPos:g},null,0,0,null,n,0,l,[],!1);(u=y.notehead)&&(e.addHead(u),f=y.accidentalshiftx,m=y.dotshiftx)}return{noteHead:u,roomTaken:f,roomTakenRight:m}}(C,r,g,w,o.voicetotal>1,this.stemdir,s,y,this.voiceScale);l=N.noteHead,h=N.roomTaken,u=N.roomTakenRight}else{var E=this.addNoteToAbcElement(C,r,w,this.stemdir,this.style,v,y,a,o);void 0!==E.min&&(this.minY=Math.min(E.min,this.minY)),l=E.noteHead,h=E.roomTaken,u=E.roomTakenRight,m=E.additionalLedgers,c=E.dir,f=E.symbolWidth}if(void 0!==r.lyric&&this.addLyric(C,r),void 0!==r.gracenotes&&(h+=this.addGraceNotes(r,o,C,l,this.stemHeight*this.voiceScale,this.isBagpipes,h)),r.decoration&&this.decoration.createDecoration(o,r.decoration,C.top,l?l.w:0,C,h,c,C.bottom,r.positioning,this.hasVocals),r.barNumber&&C.addChild(new p(r.barNumber,-10,0,0,{type:"barNumber"})),_(C,r.minpitch,r.maxpitch,r.rest,f,m,c,-2,1),void 0!==r.chord){var M=this.addChord(C,r,h,u);h=M.roomTaken,u=M.roomTakenRight}return r.startTriplet&&(this.triplet=new b(r.startTriplet,l,{flatBeams:this.flatBeams})),r.endTriplet&&this.triplet&&this.triplet.setCloseAnchor(l),!this.triplet||r.startTriplet||r.endTriplet||this.triplet.middleNote(l),C};var A=function(e,t,i,r,n,a,s,o,c,l,h,u){var f,m=i.verticalPos,g=0,v=0,b=0;if(void 0===t)e.addChild(new p("pitch is undefined",0,0,0,{type:"debug"}));else if(""===t)f=new p(null,0,0,m);else{var y=n;if(i.printer_shift){var w="same"===i.printer_shift?1:0;y="down"===r?-d.getSymbolWidth(t)*l+w:d.getSymbolWidth(t)*l-w}var k={scalex:l,scaley:l,thickness:d.symbolHeightInPitches(t)*l};if((f=new p(t,y,d.getSymbolWidth(t)*l,m,k)).stemDir=r,s){var x=m+("down"===r?-7:7)*l;u&&("down"===r&&x>6&&(x=6),"up"===r&&x<6&&(x=6));var S="down"===r?n:n+f.w-.6;e.addRight(new p(s,S,d.getSymbolWidth(s)*l,x,{scalex:l,scaley:l}))}for(v=f.w+c-2+5*o;o>0;o--){var _=1-Math.abs(m)%2;e.addRight(new p("dots.dot",f.w+c-2+5*o,d.getSymbolWidth("dots.dot"),m+_))}}if(f&&(f.highestVert=i.highestVert),i.accidental){var T;switch(i.accidental){case"quartersharp":T="accidentals.halfsharp";break;case"dblsharp":T="accidentals.dblsharp";break;case"sharp":T="accidentals.sharp";break;case"quarterflat":T="accidentals.halfflat";break;case"flat":T="accidentals.flat";break;case"dblflat":T="accidentals.dblflat";break;case"natural":T="accidentals.nat"}for(var A=!1,C=a,N=0;N<h.length;N++)if(m-h[N][0]>=6){h[N][0]=m,C=h[N][1],A=!0;break}!1===A&&(C-=d.getSymbolWidth(T)*l+2,h.push([m,C]),g=d.getSymbolWidth(T)*l+2),e.addExtra(new p(T,C,d.getSymbolWidth(T),m,{scalex:l,scaley:l})),b=d.getSymbolWidth(T)/2}return{notehead:f,accidentalshiftx:g,dotshiftx:v,extraLeft:b}};r.prototype.addSlursAndTies=function(e,i,r,n,a,s){if(i.endTie&&this.ties.length>0){for(var o=!1,c=0;c<this.ties.length;c++)if(this.ties[c].anchor1&&this.ties[c].anchor1.pitch===r.pitch){this.ties[c].setEndAnchor(r),this.ties.splice(c,1),o=!0;break}o||(this.ties[0].setEndAnchor(r),this.ties.splice(0,1))}var l=n.voicetotal<2?-1:n.voicenumber;if(i.startTie){var h=new v({anchor1:r,force:"down"===this.stemdir||"up"===this.stemdir,stemDir:this.stemdir,isGrace:s,voiceNumber:l});t&&h.setHint(),this.ties[this.ties.length]=h,n.addOther(h),e.startTie=!0}if(i.endSlur)for(var u=0;u<i.endSlur.length;u++){var d=i.endSlur[u];this.slurs[d]?((f=this.slurs[d]).setEndAnchor(r),delete this.slurs[d]):(f=new v({anchor2:r,stemDir:this.stemdir,voiceNumber:l}),t&&f.setHint(),n.addOther(f)),this.startlimitelem&&f.setStartX(this.startlimitelem)}else if(!s)for(var p in this.slurs)this.slurs.hasOwnProperty(p)&&this.slurs[p].addInternalNote(r);if(i.startSlur)for(u=0;u<i.startSlur.length;u++){d=i.startSlur[u].label;var f=new v({anchor1:r,stemDir:this.stemdir,voiceNumber:l});t&&f.setHint(),this.slurs[d]=f,n.addOther(f)}},r.prototype.addMeasureNumber=function(e,t){var i=this.renderer.getTextSize(e,"measurefont","bar-number");t.addChild(new p(e,0,0,11+i.height/f.STEP,{type:"barNumber"}))},r.prototype.createBarLine=function(e,t,i){var r=new n(t,0,10,"bar",this.tuneNumber),a=null,s=0;t.barNumber&&this.addMeasureNumber(t.barNumber,r);var o="bar_right_repeat"===t.type||"bar_dbl_repeat"===t.type,c="bar_left_repeat"!==t.type&&"bar_thick_thin"!==t.type&&"bar_invisible"!==t.type,l="bar_right_repeat"===t.type||"bar_dbl_repeat"===t.type||"bar_left_repeat"===t.type||"bar_thin_thick"===t.type||"bar_thick_thin"===t.type,h="bar_left_repeat"===t.type||"bar_thick_thin"===t.type||"bar_thin_thin"===t.type||"bar_dbl_repeat"===t.type,d="bar_left_repeat"===t.type||"bar_dbl_repeat"===t.type;if(o||d){for(var f in this.slurs)this.slurs.hasOwnProperty(f)&&this.slurs[f].setEndX(r);this.startlimitelem=r}if(o&&(r.addRight(new p("dots.dot",s,1,7)),r.addRight(new p("dots.dot",s,1,5)),s+=6),c&&(a=new p(null,s,1,2,{type:"bar",pitch2:10,linewidth:.6}),r.addRight(a)),"bar_invisible"===t.type&&(a=new p(null,s,1,2,{type:"none",pitch2:10,linewidth:.6}),r.addRight(a)),t.decoration&&this.decoration.createDecoration(e,t.decoration,12,l?3:1,r,0,"down",2,t.positioning,this.hasVocals),l&&(a=new p(null,s+=4,4,2,{type:"bar",pitch2:10,linewidth:4}),r.addRight(a),s+=5),this.partstartelem&&t.endEnding&&(this.partstartelem.anchor2=a,this.partstartelem=null),h&&(a=new p(null,s+=3,1,2,{type:"bar",pitch2:10,linewidth:.6}),r.addRight(a)),d&&(s+=3,r.addRight(new p("dots.dot",s,1,7)),r.addRight(new p("dots.dot",s,1,5))),t.startEnding&&i){var m=this.renderer.getTextSize(t.startEnding,"repeatfont","").width;r.minspacing+=m+10,this.partstartelem=new u(t.startEnding,a,null),e.addOther(this.partstartelem)}return r.extraw-=5,r}}(),e.exports=r},function(e,t,i){i(4);var r,n=i(2),a=i(1),s=function(e){return void 0===e?0:Math.floor(Math.log(e)/Math.log(2))};!function(){"use strict";(r=function(e,t,i){this.isflat=i,this.isgrace=t&&"grace"===t,this.forceup=this.isgrace||t&&"up"===t,this.forcedown=t&&"down"===t,this.elems=[],this.total=0,this.allrests=!0,this.stemHeight=e,this.beams=[]}).prototype.setHint=function(){this.hint=!0},r.prototype.add=function(e){var t=e.abcelem.averagepitch;void 0!==t&&(this.allrests=this.allrests&&e.abcelem.rest,e.beam=this,this.elems.push(e),this.total+=t,(void 0===this.min||e.abcelem.minpitch<this.min)&&(this.min=e.abcelem.minpitch),(void 0===this.max||e.abcelem.maxpitch>this.max)&&(this.max=e.abcelem.maxpitch))};function e(e,t,i,r){if(!e.children)return r;for(var n=0;n<e.children.length;n++){var a=e.children[n];t&&void 0!==a.top&&"flags.ugrace"===a.c?r=Math.max(r,a.top-i):t||void 0===a.bottom||"flags.ugrace"!==a.c||(r=Math.max(r,i-a.bottom+7))}return r}function t(e,t){return t?e/t:0}function i(e,t,i,r,n){return t+(r-t)/(i-e)*(n-e)}function o(e,t,i,r,n,a,s){var o="beam-elem";s&&(o+=" abcjs-hint");var c="M"+t+" "+(i=e.calcY(i))+" L"+r+" "+(n=e.calcY(n))+"L"+r+" "+(n+a)+" L"+t+" "+(i+a)+"z";e.printPath({path:c,stroke:"none",fill:"#000000",class:e.addClasses(o)})}r.prototype.calcDir=function(){return!!this.forceup||!this.forcedown&&t(this.total,this.elems.length)<6},r.prototype.layout=function(){if(0!==this.elems.length&&!this.allrests){this.stemsUp=this.calcDir();var r=function(e,t){var i=e?a.STEP:-a.STEP;t&&(i*=.4);return i}(this.stemsUp,this.isgrace),o=this.elems[0],c=this.elems[this.elems.length-1],l=0,h=this.stemsUp?o.abcelem.maxpitch:o.abcelem.minpitch;l=e(o,this.stemsUp,h,l),l=e(c,this.stemsUp,h,l),l=Math.max(this.stemHeight,l+3);var u=function(e,i,r,n,a,s,o,c,l,h){var u=t(e,i),d=r-2,p=r-2,f=Math.round(n?Math.max(u+d,l+p):Math.min(u-d,c-p)),m=function(e,t,i,r){if(r)return 0;var n=e-t,a=i/2;n>a&&(n=a);n<-a&&(n=-a);return n}(a,s,i,o),g=f+Math.floor(m/2),v=f+Math.floor(-m/2);h||(n&&f<6?(g=6,v=6):!n&&f>6&&(g=6,v=6));return[g,v]}(this.total,this.elems.length,l,this.stemsUp,o.abcelem.averagepitch,c.abcelem.averagepitch,this.isflat,this.min,this.max,this.isgrace),d=function(e,t,i){var r=t.heads[e?0:t.heads.length-1],n=i.heads[e?0:i.heads.length-1],a=r.x;e&&(a+=r.w-.6);var s=n.x;e&&(s+=n.w);return[a,s]}(this.stemsUp,o,c);this.beams.push({startX:d[0],endX:d[1],startY:u[0],endY:u[1],dy:r});for(var p=function(e,t,r,n,a){for(var o=[],c=[],l=0;l<e.length;l++){var h=e[l];if(!h.abcelem.rest){var u=h.heads[t?0:h.heads.length-1],d=u.x+(t?u.w:0),p=i(r.startX,r.startY,r.endX,r.endY,d),f=t?-1.5:1.5;n&&(f=2*f/3);var m=h.abcelem.duration;0===m&&(m=.25);for(var g=s(m);g<-3;g++)c[-4-g]?c[-4-g].single=!1:c[-4-g]={x:d+(t?-.6:0),y:p+f*(-4-g+1),durlog:g,single:!0};for(var v=c.length-1;v>=0;v--)if(l===e.length-1||s(e[l+1].abcelem.duration)>-v-4){var b=d,y=p+f*(v+1);c[v].single&&(b=0===l?d+5:d-5,y=i(r.startX,r.startY,r.endX,r.endY,b)+f*(v+1)),o.push({startX:c[v].x,endX:b,startY:c[v].y,endY:y,dy:a}),c=c.slice(0,v)}}}return o}(this.elems,this.stemsUp,this.beams[0],this.isgrace,r),f=0;f<p.length;f++)this.beams.push(p[f]);!function(e,t,r,s,o){for(var c=0;c<e.length;c++){var l=e[c];if(!l.abcelem.rest){var h=!l.addExtra,u=h?o:l,d=l.heads[t?0:l.heads.length-1],p=d.pitch+(t?.2:-.2),f=t?d.w:0,m=d.x+f,g=i(r.startX,r.startY,r.endX,r.endY,m),v=t?-.6:.6;t||(g-=s/2/a.STEP),h&&(f+=l.heads[0].dx),"noteheads.slash.quarter"===d.c&&(t?p+=1:p-=1);var b=new n(null,f,0,p,{type:"stem",pitch2:g,linewidth:v});b.setX(u.x),u.addExtra(b)}}}(this.elems,this.stemsUp,this.beams[0],r,this.mainNote)}},r.prototype.isAbove=function(){return this.stemsUp},r.prototype.heightAtMidpoint=function(e,t){if(0===this.beams.length)return 0;var r=this.beams[0],n=e+(t-e)/2;return i(r.startX,r.startY,r.endX,r.endY,n)},r.prototype.yAtNote=function(e){var t=this.beams[0];return i(t.startX,t.startY,t.endX,t.endY,e.x)},r.prototype.xAtMidpoint=function(e,t){return e+(t-e)/2},r.prototype.draw=function(e){if(0!==this.beams.length){e.beginGroup();for(var t=0;t<this.beams.length;t++){var i=this.beams[t];o(e,i.startX,i.startY,i.endX,i.endY,i.dy,this.hint)}e.endGroup("beam-elem")}}}(),e.exports=r},function(e,t){var i=function(){this.length=1};i.prototype.increaseStavesIncluded=function(){this.length++},i.prototype.setLocation=function(e){this.x=e},i.prototype.getWidth=function(){return 10},i.prototype.layout=function(e,t,i){this.startY=t,this.endY=i},i.prototype.draw=function(e,t,i){this.layout(e,t,i),e.drawBrace(this.x,this.startY,this.endY)},e.exports=i},function(e,t,i){var r,n=i(4),a=i(3),s=i(2);!function(){"use strict";r=function(e,t){var i,r=0,o=new n(e,0,10,"staff-extra",t);switch(o.isClef=!0,e.type){case"treble":i="clefs.G";break;case"tenor":case"alto":i="clefs.C";break;case"bass":i="clefs.F";break;case"treble+8":i="clefs.G",r=1;break;case"tenor+8":i="clefs.C",r=1;break;case"bass+8":i="clefs.F",r=1;break;case"alto+8":i="clefs.C",r=1;break;case"treble-8":i="clefs.G",r=-1;break;case"tenor-8":i="clefs.C",r=-1;break;case"bass-8":i="clefs.F",r=-1;break;case"alto-8":i="clefs.C",r=-1;break;case"none":return null;case"perc":i="clefs.perc";break;default:o.addChild(new s("clef="+e.type,0,0,void 0,{type:"debug"}))}if(i&&(o.addRight(new s(i,5,a.getSymbolWidth(i),e.clefPos)),"clefs.G"===i?(o.top=13,o.bottom=-1):(o.top=10,o.bottom=2),0!==r)){var c=(a.getSymbolWidth(i)-a.getSymbolWidth("8")*(2/3))/2;o.addRight(new s("8",5+c,a.getSymbolWidth("8")*(2/3),r>0?o.top+3:o.bottom-1,{scalex:2/3,scaley:2/3})),o.top+=2}return o}}(),e.exports=r},function(e,t,i){var r,n=i(4),a=i(3),s=i(2),o=i(0);!function(){"use strict";r=function(e,t){if(!e.accidentals||0===e.accidentals.length)return null;var i=new n(e,0,10,"staff-extra",t);i.isKeySig=!0;var r=0;return o.each(e.accidentals,(function(e){var t="sharp"===e.acc?"accidentals.sharp":"natural"===e.acc?"accidentals.nat":"accidentals.flat";i.addRight(new s(t,r,a.getSymbolWidth(t),e.verticalPos,{thickness:a.symbolHeightInPitches(t)})),r+=a.getSymbolWidth(t)+2}),this),i}}(),e.exports=r},function(e,t,i){var r,n=i(4),a=i(3),s=i(2);!function(){"use strict";r=function(e,t){var i=new n(e,0,10,"staff-extra",t);if("specified"===e.type)for(var r=0,o=0;o<e.value.length;o++)if(0!==o&&(i.addRight(new s("+",r+1,a.getSymbolWidth("+"),6,{thickness:a.symbolHeightInPitches("+")})),r+=a.getSymbolWidth("+")+2),e.value[o].den){for(var c=0,l=0;l<e.value[o].num.length;l++)c+=a.getSymbolWidth(e.value[o].num.charAt(l));var h=0;for(l=0;l<e.value[o].num.length;l++)h+=a.getSymbolWidth(e.value[o].den.charAt(l));var u=Math.max(c,h);i.addRight(new s(e.value[o].num,r+(u-c)/2,c,8,{thickness:a.symbolHeightInPitches(e.value[o].num.charAt(0))})),i.addRight(new s(e.value[o].den,r+(u-h)/2,h,4,{thickness:a.symbolHeightInPitches(e.value[o].den.charAt(0))})),r+=u}else{for(var d=0,p=0;p<e.value[o].num.length;p++)d+=a.getSymbolWidth(e.value[o].num.charAt(p));i.addRight(new s(e.value[o].num,r,d,6,{thickness:a.symbolHeightInPitches(e.value[o].num.charAt(0))})),r+=d}else"common_time"===e.type?i.addRight(new s("timesig.common",0,a.getSymbolWidth("timesig.common"),6,{thickness:a.symbolHeightInPitches("timesig.common")})):"cut_time"===e.type?i.addRight(new s("timesig.cut",0,a.getSymbolWidth("timesig.cut"),6,{thickness:a.symbolHeightInPitches("timesig.cut")})):"tempus_imperfectum"===e.type?i.addRight(new s("timesig.imperfectum",0,a.getSymbolWidth("timesig.imperfectum"),6,{thickness:a.symbolHeightInPitches("timesig.imperfectum")})):"tempus_imperfectum_prolatio"===e.type?i.addRight(new s("timesig.imperfectum2",0,a.getSymbolWidth("timesig.imperfectum2"),6,{thickness:a.symbolHeightInPitches("timesig.imperfectum2")})):"tempus_perfectum"===e.type?i.addRight(new s("timesig.perfectum",0,a.getSymbolWidth("timesig.perfectum"),6,{thickness:a.symbolHeightInPitches("timesig.perfectum")})):"tempus_perfectum_prolatio"===e.type?i.addRight(new s("timesig.perfectum2",0,a.getSymbolWidth("timesig.perfectum2"),6,{thickness:a.symbolHeightInPitches("timesig.perfectum2")})):console.log("time signature:",e);return i}}(),e.exports=r},function(e,t,i){var r,n=i(41),a=i(42),s=i(3),o=i(2),c=i(20);!function(){"use strict";var e=function(e,t,i,r,n){function a(e,t){var a="down"===n?function(){if(0===r.heads.length)return 2;for(var e=r.heads[0].pitch,t=1;t<r.heads.length;t++)e=Math.min(e,r.heads[t].pitch);return e}()+1:function(){if(0===r.heads.length)return 10;for(var e=r.heads[0].pitch,t=1;t<r.heads.length;t++)e=Math.max(e,r.heads[t].pitch);return e}()+9;"down"!==n&&1===t&&a--;var c=i/2;c+="down"===n?-5:3;for(var l=0;l<t;l++)a-=1,r.addChild(new o(e,c,s.getSymbolWidth(e),a))}for(var c=0;c<e.length;c++)switch(e[c]){case"/":a("flags.ugrace",1);break;case"//":a("flags.ugrace",2);break;case"///":a("flags.ugrace",3);break;case"////":a("flags.ugrace",4)}};(r=function(){this.startDiminuendoX=void 0,this.startCrescendoX=void 0,this.minTop=12,this.minBottom=0}).prototype.dynamicDecoration=function(e,t,i,r){for(var n,s,o=0;o<t.length;o++)switch(t[o]){case"diminuendo(":this.startDiminuendoX=i,n=void 0;break;case"diminuendo)":n={start:this.startDiminuendoX,stop:i},this.startDiminuendoX=void 0;break;case"crescendo(":this.startCrescendoX=i,s=void 0;break;case"crescendo)":s={start:this.startCrescendoX,stop:i},this.startCrescendoX=void 0}n&&e.addOther(new a(n.start,n.stop,">",r)),s&&e.addOther(new a(s.start,s.stop,"<",r))},r.prototype.createDecoration=function(t,i,r,a,l,h,u,d,p,f){p||(p={ornamentPosition:"above",volumePosition:f?"above":"below",dynamicPosition:f?"above":"below"}),function(e,t,i,r){for(var a=0;a<t.length;a++)switch(t[a]){case"p":case"mp":case"pp":case"ppp":case"pppp":case"f":case"ff":case"fff":case"ffff":case"sfz":case"mf":var s=new n(i,t[a],r);e.addOther(s)}}(t,i,l,p.volumePosition),this.dynamicDecoration(t,i,l,p.dynamicPosition),e(i,0,a,l,u);var m=function(e,t,i,r,n,a,l,h){for(var u,d=0;d<t.length;d++){if("staccato"===t[d]||"tenuto"===t[d]||"accent"===t[d]){var p="scripts."+t[d];if("accent"===t[d]&&(p="scripts.sforzato"),u=void 0===u?"down"===l?i+2:h-2:"down"===l?u+2:u-2,"accent"===t[d])"up"===l?u--:u++;else switch(u){case 2:case 4:case 6:case 8:case 10:"up"===l?u--:u++}i>9&&u++;var f=r/2;"center"!==s.getSymbolAlign(p)&&(f-=s.getSymbolWidth(p)/2),n.addChild(new o(p,f,s.getSymbolWidth(p),u))}if("slide"===t[d]&&n.heads[0]){var m=n.heads[0].pitch,g=new o("",-a-15,0,(m-=2)-1),v=new o("",-a-5,0,m+1);n.addChild(g),n.addChild(v),e.addOther(new c({anchor1:g,anchor2:v,fixedY:!0}))}}return void 0===u&&(u=i),{above:u,below:n.bottom}}(t,i,r,a,l,h,u,d);m.above=Math.max(m.above,this.minTop);!function(e,t,i,r,n,a,c){function l(e,t){"above"===e?r.above+=t:r.below-=t}function h(e){var t;return"above"===e?(t=r.above)<a&&(t=a):(t=r.below)>c&&(t=c),t}function u(e,r){var n=h(r);i.addChild(new o(e,t/2,0,n+2,{type:"decoration",klass:"ornament",thickness:3})),l(r,5)}function d(e,r){var n=t/2;"center"!==s.getSymbolAlign(e)&&(n-=s.getSymbolWidth(e)/2);var a=s.symbolHeightInPitches(e)+1,c=h(r);c="above"===r?c+a/2:c-a/2,i.addChild(new o(e,n,s.getSymbolWidth(e),c,{klass:"ornament",thickness:s.symbolHeightInPitches(e)})),l(r,a)}for(var p={"+":"scripts.stopped",open:"scripts.open",snap:"scripts.snap",wedge:"scripts.wedge",thumb:"scripts.thumb",shortphrase:"scripts.shortphrase",mediumphrase:"scripts.mediumphrase",longphrase:"scripts.longphrase",trill:"scripts.trill",roll:"scripts.roll",irishroll:"scripts.roll",marcato:"scripts.umarcato",dmarcato:"scripts.dmarcato",umarcato:"scripts.umarcato",turn:"scripts.turn",uppermordent:"scripts.prall",pralltriller:"scripts.prall",mordent:"scripts.mordent",lowermordent:"scripts.mordent",downbow:"scripts.downbow",upbow:"scripts.upbow",fermata:"scripts.ufermata",invertedfermata:"scripts.dfermata",breath:",",coda:"scripts.coda",segno:"scripts.segno"},f=!1,m=0;m<e.length;m++)switch(e[m]){case"0":case"1":case"2":case"3":case"4":case"5":case"D.C.":case"D.S.":u(e[m],n),f=!0;break;case"fine":u("FINE",n),f=!0;break;case"+":case"open":case"snap":case"wedge":case"thumb":case"shortphrase":case"mediumphrase":case"longphrase":case"trill":case"roll":case"irishroll":case"marcato":case"dmarcato":case"turn":case"uppermordent":case"pralltriller":case"mordent":case"lowermordent":case"downbow":case"upbow":case"fermata":case"breath":case"umarcato":case"coda":case"segno":d(p[e[m]],n),f=!0;break;case"invertedfermata":d(p[e[m]],"below"),f=!0;break;case"mark":i.klass="mark"}}(i,a,l,m,p.ornamentPosition,this.minTop,this.minBottom);!function(e,t,i){for(var r=0;r<e.length;r++)switch(e[r]){case"arpeggio":for(var n=t.abcelem.minpitch-1;n<=t.abcelem.maxpitch;n+=2)t.addExtra(new o("scripts.arpeggio",2*-s.getSymbolWidth("scripts.arpeggio")-i,0,n+2,{klass:"ornament",thickness:s.symbolHeightInPitches("scripts.arpeggio")}))}}(i,l,h)}}(),e.exports=r},function(e,t,i){i(1);var r=function(e,t,i){this.anchor=e,this.dec=t,"below"===i?this.volumeHeightBelow=5:this.volumeHeightAbove=5,this.pitch=void 0};r.prototype.setUpperAndLowerElements=function(e){this.volumeHeightAbove?this.pitch=e.volumeHeightAbove:this.pitch=e.volumeHeightBelow},r.prototype.draw=function(e,t,i){void 0===this.pitch&&window.console.error("Dynamic Element y-coordinate not set.");e.printSymbol(this.anchor.x,this.pitch,this.dec,1,1,e.addClasses("decoration"))},e.exports=r},function(e,t,i){var r=i(5),n=function(e,t,i,r){this.anchor1=e,this.anchor2=t,this.dir=i,"above"===r?this.dynamicHeightAbove=4:this.dynamicHeightBelow=4,this.pitch=void 0};n.prototype.setUpperAndLowerElements=function(e){this.dynamicHeightAbove?this.pitch=e.dynamicHeightAbove:this.pitch=e.dynamicHeightBelow},n.prototype.draw=function(e){void 0===this.pitch&&window.console.error("Crescendo Element y-coordinate not set.");var t=e.calcY(this.pitch)+4;"<"===this.dir?(this.drawLine(e,t+4,t),this.drawLine(e,t+4,t+8)):(this.drawLine(e,t,t+4),this.drawLine(e,t+8,t+4))},n.prototype.drawLine=function(e,t,i){var n=this.anchor1?this.anchor1.x:0,a=this.anchor2?this.anchor2.x:800,s=r("M %f %f L %f %f",n,t,a,i);e.printPath({path:s,stroke:"#000000",class:e.addClasses("decoration")})},e.exports=n},function(e,t,i){var r=i(5),n=function(e,t,i){this.text=e,this.anchor1=t,this.anchor2=i,this.endingHeightAbove=5,this.pitch=void 0};n.prototype.setUpperAndLowerElements=function(e){this.pitch=e.endingHeightAbove-2},n.prototype.draw=function(e,t,i){void 0===this.pitch&&window.console.error("Ending Element y-coordinate not set.");var n,a=e.calcY(this.pitch);this.anchor1&&(t=this.anchor1.x+this.anchor1.w,n=r("M %f %f L %f %f",t,a,t,a+20),e.printPath({path:n,stroke:"#000000",fill:"#000000",class:e.addClasses("ending")}),e.renderText(t+5,e.calcY(this.pitch-.5),this.text,"repeatfont","ending","start")),this.anchor2&&(i=this.anchor2.x,n=r("M %f %f L %f %f",i,a,i,a+20),e.printPath({path:n,stroke:"#000000",fill:"#000000",class:e.addClasses("ending")})),n=r("M %f %f L %f %f",t,a,i,a),e.printPath({path:n,stroke:"#000000",fill:"#000000",class:e.addClasses("ending")})},e.exports=n},function(e,t,i){var r=i(1),n=function(){this.voices=[],this.staffs=[],this.brace=void 0};n.prototype.setLimit=function(e,t){t.specialY[e]&&(t.staff.specialY[e]?t.staff.specialY[e]=Math.max(t.staff.specialY[e],t.specialY[e]):t.staff.specialY[e]=t.specialY[e])},n.prototype.addVoice=function(e,t,i){var r=this.voices.length;this.voices[r]=e,this.staffs[t]?this.staffs[t].voices.push(r):this.staffs[this.staffs.length]={top:10,bottom:2,lines:i,voices:[r],specialY:{tempoHeightAbove:0,partHeightAbove:0,volumeHeightAbove:0,dynamicHeightAbove:0,endingHeightAbove:0,chordHeightAbove:0,lyricHeightAbove:0,lyricHeightBelow:0,chordHeightBelow:0,volumeHeightBelow:0,dynamicHeightBelow:0}},e.staff=this.staffs[t]},n.prototype.setStaffLimits=function(e){e.staff.top=Math.max(e.staff.top,e.top),e.staff.bottom=Math.min(e.staff.bottom,e.bottom),this.setLimit("tempoHeightAbove",e),this.setLimit("partHeightAbove",e),this.setLimit("volumeHeightAbove",e),this.setLimit("dynamicHeightAbove",e),this.setLimit("endingHeightAbove",e),this.setLimit("chordHeightAbove",e),this.setLimit("lyricHeightAbove",e),this.setLimit("lyricHeightBelow",e),this.setLimit("chordHeightBelow",e),this.setLimit("volumeHeightBelow",e),this.setLimit("dynamicHeightBelow",e)},n.prototype.setUpperAndLowerElements=function(e){for(var t,i=0;i<this.staffs.length;i++){var n=this.staffs[i],a={tempoHeightAbove:0,partHeightAbove:0,volumeHeightAbove:0,dynamicHeightAbove:0,endingHeightAbove:0,chordHeightAbove:0,lyricHeightAbove:0,lyricHeightBelow:0,chordHeightBelow:0,volumeHeightBelow:0,dynamicHeightBelow:0};0,n.specialY.lyricHeightAbove&&(n.top+=n.specialY.lyricHeightAbove,a.lyricHeightAbove=n.top),n.specialY.chordHeightAbove&&(n.top+=n.specialY.chordHeightAbove,a.chordHeightAbove=n.top),n.specialY.endingHeightAbove&&(n.specialY.chordHeightAbove?n.top+=2:n.top+=n.specialY.endingHeightAbove,a.endingHeightAbove=n.top),n.specialY.dynamicHeightAbove&&n.specialY.volumeHeightAbove?(n.top+=Math.max(n.specialY.dynamicHeightAbove,n.specialY.volumeHeightAbove),a.dynamicHeightAbove=n.top,a.volumeHeightAbove=n.top):n.specialY.dynamicHeightAbove?(n.top+=n.specialY.dynamicHeightAbove,a.dynamicHeightAbove=n.top):n.specialY.volumeHeightAbove&&(n.top+=n.specialY.volumeHeightAbove,a.volumeHeightAbove=n.top),n.specialY.partHeightAbove&&(n.top+=n.specialY.partHeightAbove,a.partHeightAbove=n.top),n.specialY.tempoHeightAbove&&(n.top+=n.specialY.tempoHeightAbove,a.tempoHeightAbove=n.top),n.specialY.lyricHeightBelow&&(a.lyricHeightBelow=n.bottom,n.bottom-=n.specialY.lyricHeightBelow),n.specialY.chordHeightBelow&&(a.chordHeightBelow=n.bottom,n.bottom-=n.specialY.chordHeightBelow),n.specialY.volumeHeightBelow&&n.specialY.dynamicHeightBelow?(a.volumeHeightBelow=n.bottom,a.dynamicHeightBelow=n.bottom,n.bottom-=Math.max(n.specialY.volumeHeightBelow,n.specialY.dynamicHeightBelow)):n.specialY.volumeHeightBelow?(a.volumeHeightBelow=n.bottom,n.bottom-=n.specialY.volumeHeightBelow):n.specialY.dynamicHeightBelow&&(a.dynamicHeightBelow=n.bottom,n.bottom-=n.specialY.dynamicHeightBelow);for(var s=0;s<n.voices.length;s++){this.voices[n.voices[s]].setUpperAndLowerElements(a)}if(void 0!==t){var o=t+(n.top-10),c=e.spacing.systemStaffSeparation/r.STEP-o;c>0&&(n.top+=c)}t=2-n.bottom}},n.prototype.finished=function(){for(var e=0;e<this.voices.length;e++)if(!this.voices[e].layoutEnded())return!1;return!0},n.prototype.layout=function(e,t,i){var r,n=0,a=1e3,s=function(e,t,i){for(var r=e.padding.left,n=0,a=0;a<t.length;a++)if(t[a].header){var s=e.getTextSize(t[a].header,"voicefont","");n=Math.max(n,s.width)}return n&&(n+=e.getTextSize("A","voicefont","").width),r+=n,i&&(i.setLocation(r),r+=i.getWidth()),r}(t,this.voices,this.brace);this.startx=s;var o=0;for(i&&console.log("init layout",e),r=0;r<this.voices.length;r++)this.voices[r].beginLayout(s);for(var c=0;!this.finished();){for(o=null,r=0;r<this.voices.length;r++)this.voices[r].layoutEnded()||o&&!(this.voices[r].getDurationIndex()<o)||(o=this.voices[r].getDurationIndex());var l=[],h=[];for(r=0;r<this.voices.length;r++){this.voices[r].getDurationIndex()-o>1e-7?h.push(this.voices[r]):l.push(this.voices[r])}c=0;var u=0;for(r=0;r<l.length;r++)l[r].getNextX()>s&&(s=l[r].getNextX(),c=l[r].getSpacingUnits(),u=l[r].spacingduration);for(n+=c,a=Math.min(a,c),i&&console.log("currentduration: ",o,n,a),r=0;r<l.length;r++){var d=l[r].layoutOneItem(s,e),p=d-s;if(p>0){s=d;for(var f=0;f<r;f++)l[f].shiftRight(p)}}for(r=0;r<h.length;r++)h[r].spacingduration-=u,h[r].updateNextX(s,e);for(r=0;r<l.length;r++){l[r].updateIndices()}}for(r=0;r<this.voices.length;r++)this.voices[r].getNextX()>s&&(s=this.voices[r].getNextX(),c=this.voices[r].getSpacingUnits());for(n+=c,this.w=s,r=0;r<this.voices.length;r++)this.voices[r].w=this.w;return{spacingUnits:n,minSpace:a}},n.prototype.calcHeight=function(){for(var e=0,t=0;t<this.voices.length;t++){var i=this.voices[t].staff;this.voices[t].duplicate||(e+=i.top,i.bottom<0&&(e+=-i.bottom))}return e},n.prototype.draw=function(e){e.addInvisibleMarker("abcjs-top-of-system");for(var t,i,n=e.y,a=0;a<this.staffs.length;a++){var s=this.staffs[a];e.moveY(r.STEP,s.top),s.absoluteY=e.y,s.bottom<0&&e.moveY(r.STEP,-s.bottom)}var o=0;e.measureNumber=null,e.noteNumber=null;for(var c=0;c<this.voices.length;c++){var l=this.voices[c].staff;e.y=l.absoluteY,e.voiceNumber=c,this.voices[c].duplicate||(t||(t=e.calcY(10)),i=e.calcY(2),0!==l.lines&&(e.measureNumber=null,e.noteNumber=null,e.printStave(this.startx,this.w,l.lines))),this.voices[c].draw(e,o),e.measureNumber=null,e.noteNumber=null,this.voices[c].duplicate||(o=e.calcY(2)),this.brace&&c===this.brace.length-1&&this.brace&&this.brace.draw(e,t,i)}e.measureNumber=null,e.noteNumber=null,this.staffs.length>1&&e.printStem(this.startx,.6,t,i),e.y=n},e.exports=n},function(e,t,i){var r,n=i(4),a=i(2);!function(){"use strict";(r=function(e,t,i){this.tempo=e,this.tuneNumber=t,this.tempoHeightAbove=5,this.pitch=void 0,this.tempo.duration&&!this.tempo.suppressBpm&&(this.note=this.createNote(i,e,t))}).prototype.setUpperAndLowerElements=function(e){if(this.pitch=e.tempoHeightAbove,this.top=e.tempoHeightAbove,this.bottom=e.tempoHeightAbove,this.note){var t=this.pitch-5+1;this.note.top=t,this.note.bottom=t;for(var i=0;i<this.note.children.length;i++){var r=this.note.children[i];r.top+=t,r.bottom+=t,r.pitch+=t,void 0!==r.pitch2&&(r.pitch2+=t)}}},r.prototype.setX=function(e){this.x=e},r.prototype.createNote=function(e,t,i){var r,s,o,c=t.duration[0],l=new n(t,c,1,"tempo",i);c<=1/32?(o="noteheads.quarter",s="flags.u32nd",r=0):c<=1/16?(o="noteheads.quarter",s="flags.u16th",r=0):c<=3/32?(o="noteheads.quarter",s="flags.u16nd",r=1):c<=1/8?(o="noteheads.quarter",s="flags.u8th",r=0):c<=3/16?(o="noteheads.quarter",s="flags.u8th",r=1):c<=.25?(o="noteheads.quarter",r=0):c<=3/8?(o="noteheads.quarter",r=1):c<=.5?(o="noteheads.half",r=0):c<=.75?(o="noteheads.half",r=1):c<=1?(o="noteheads.whole",r=0):c<=1.5?(o="noteheads.whole",r=1):c<=2?(o="noteheads.dbl",r=0):(o="noteheads.dbl",r=1);var h,u=e(l,o,{verticalPos:0},"up",0,0,s,r,0,.75,[],!1).notehead;if(l.addHead(u),"noteheads.whole"!==o&&"noteheads.dbl"!==o){var d=u.dx+u.w;h=new a(null,d,0,.25,{type:"stem",pitch2:5.25,linewidth:-.6}),l.addExtra(h)}return l},r.prototype.draw=function(e){var t=this.x;void 0===this.pitch&&window.console.error("Tempo Element y-coordinate not set.");var i,r=e.calcY(this.pitch);if(this.tempo.preString){i=e.renderText(t,r,this.tempo.preString,"tempofont","tempo","start");var n=e.getTextSize(this.tempo.preString,"tempofont","tempo",i),a=n.width;t+=a+a/this.tempo.preString.length}if(this.note){this.note&&this.note.setX(t);for(var s=0;s<this.note.children.length;s++)this.note.children[s].draw(e,t);t+=this.note.w+5;var o="= "+this.tempo.bpm;i=e.renderText(t,r,o,"tempofont","tempo","start");var c=(n=e.getTextSize(o,"tempofont","tempo",i)).width;t+=c+c/o.length}this.tempo.postString&&e.renderText(t,r,this.tempo.postString,"tempofont","tempo","start")}}(),e.exports=r},function(e,t,i){var r,n=i(5);!function(){"use strict";function e(e,t,i,r,a,s){var o=n("M %f %f L %f %f",t,i,r,a);e.printPath({path:o,stroke:"#000000",class:e.addClasses("triplet d"+s)})}(r=function(e,t,i){this.anchor1=t,this.number=e,this.duration=(""+t.parent.durationClass).replace(/\./,"-"),this.middleElems=[],this.flatBeams=i.flatBeams}).prototype.isClosed=function(){return this.anchor2},r.prototype.middleNote=function(e){this.middleElems.push(e)},r.prototype.setCloseAnchor=function(e){this.anchor2=e,this.anchor1.parent.beam&&(this.endingHeightAbove=4)},r.prototype.setUpperAndLowerElements=function(){},r.prototype.layout=function(){if(this.anchor1&&this.anchor2)if(this.hasBeam=this.anchor1.parent.beam&&this.anchor1.parent.beam===this.anchor2.parent.beam,this.hasBeam){var e=this.anchor1.parent.beam,t=e.isAbove()?this.anchor1.x+this.anchor1.w:this.anchor1.x;this.yTextPos=e.heightAtMidpoint(t,this.anchor2.x),this.yTextPos+=e.isAbove()?3:-2,this.top=this.yTextPos+1,this.bottom=this.yTextPos-2,e.isAbove()&&(this.endingHeightAbove=4)}else{this.startNote=Math.max(this.anchor1.parent.top,9)+4,this.endNote=Math.max(this.anchor2.parent.top,9)+4,"rest"===this.anchor1.parent.type&&"rest"!==this.anchor2.parent.type?this.startNote=this.endNote:"rest"===this.anchor2.parent.type&&"rest"!==this.anchor1.parent.type&&(this.endNote=this.startNote);for(var i=0,r=0;r<this.middleElems.length;r++)i=Math.max(i,this.middleElems[r].top);((i+=4)>this.startNote||i>this.endNote)&&(this.startNote=i,this.endNote=i),this.flatBeams&&(this.startNote=Math.max(this.startNote,this.endNote),this.endNote=Math.max(this.startNote,this.endNote)),this.yTextPos=this.startNote+(this.endNote-this.startNote)/2,this.top=this.yTextPos+1}delete this.middleElems,delete this.flatBeams},r.prototype.draw=function(t){var i;if(this.hasBeam){var r=this.anchor1.parent.beam.isAbove()?this.anchor1.x+this.anchor1.w:this.anchor1.x;i=this.anchor1.parent.beam.xAtMidpoint(r,this.anchor2.x)}else i=this.anchor1.x+(this.anchor2.x+this.anchor2.w-this.anchor1.x)/2,function(t,i,r,n,a,s){r=t.calcY(r),a=t.calcY(a);e(t,i,r,i,r+5,s),e(t,n,a,n,a+5,s);var o=i+(n-i)/2,c=(a-r)/(n-i),l=o-8;e(t,i,r,l,r+(l-i)*c,s);var h=o+8;e(t,h,r+(h-i)*c,n,a,s)}(t,this.anchor1.x,this.startNote,this.anchor2.x+this.anchor2.w,this.endNote,this.duration);t.renderText(i,t.calcY(this.yTextPos),""+this.number,"tripletfont",t.addClasses("triplet d"+this.duration),"middle",!0)}}(),e.exports=r},function(e,t,i){var r=i(0),n=function(e,t){this.children=[],this.beams=[],this.otherchildren=[],this.w=0,this.duplicate=!1,this.voicenumber=e,this.voicetotal=t,this.bottom=7,this.top=7,this.specialY={tempoHeightAbove:0,partHeightAbove:0,volumeHeightAbove:0,dynamicHeightAbove:0,endingHeightAbove:0,chordHeightAbove:0,lyricHeightAbove:0,lyricHeightBelow:0,chordHeightBelow:0,volumeHeightBelow:0,dynamicHeightBelow:0}};n.prototype.addChild=function(e){if("bar"===e.type){for(var t=!0,i=0;t&&i<this.children.length;i++)"staff-extra"!==this.children[i].type&&"tempo"!==this.children[i].type&&(t=!1);t||(this.beams.push("bar"),this.otherchildren.push("bar"))}this.children[this.children.length]=e,this.setRange(e)},n.prototype.setLimit=function(e,t){var i=t.specialY;i||(i=t),i[e]&&(this.specialY[e]?this.specialY[e]=Math.max(this.specialY[e],i[e]):this.specialY[e]=i[e])},n.prototype.moveDecorations=function(e){for(var t=0;t<e.elems.length;t++){var i=e.elems[t];if(i.top)for(var r=e.yAtNote(i),n=0;n<i.children.length;n++){var a=i.children[n];if("ornament"===a.klass&&a.bottom-1.5<r){var s=r-a.bottom+1.5;a.bottom+=s,a.top+=s,a.pitch+=s,r=i.top=a.top}}}},n.prototype.adjustRange=function(e){void 0!==e.bottom&&(this.bottom=Math.min(this.bottom,e.bottom)),void 0!==e.top&&(this.top=Math.max(this.top,e.top))},n.prototype.setRange=function(e){this.adjustRange(e),this.setLimit("tempoHeightAbove",e),this.setLimit("partHeightAbove",e),this.setLimit("volumeHeightAbove",e),this.setLimit("dynamicHeightAbove",e),this.setLimit("endingHeightAbove",e),this.setLimit("chordHeightAbove",e),this.setLimit("lyricHeightAbove",e),this.setLimit("lyricHeightBelow",e),this.setLimit("chordHeightBelow",e),this.setLimit("volumeHeightBelow",e),this.setLimit("dynamicHeightBelow",e)},n.prototype.setUpperAndLowerElements=function(e){var t;for(t=0;t<this.children.length;t++){(i=this.children[t]).setUpperAndLowerElements(e)}for(t=0;t<this.otherchildren.length;t++){var i;"string"!=typeof(i=this.otherchildren[t])&&i.setUpperAndLowerElements(e)}},n.prototype.addOther=function(e){this.otherchildren.push(e),this.setRange(e)},n.prototype.addBeam=function(e){this.beams.push(e)},n.prototype.updateIndices=function(){this.layoutEnded()||(this.durationindex+=this.children[this.i].duration,"bar"===this.children[this.i].type&&(this.durationindex=Math.round(64*this.durationindex)/64),this.i++)},n.prototype.layoutEnded=function(){return this.i>=this.children.length},n.prototype.getDurationIndex=function(){return this.durationindex-(this.children[this.i]&&this.children[this.i].duration>0?0:5e-7)},n.prototype.getSpacingUnits=function(){return Math.sqrt(8*this.spacingduration)},n.prototype.getNextX=function(){return Math.max(this.minx,this.nextx)},n.prototype.beginLayout=function(e){this.i=0,this.durationindex=0,this.startx=e,this.minx=e,this.nextx=e,this.spacingduration=0},n.prototype.layoutOneItem=function(e,t){var i=this.children[this.i];if(!i)return 0;var r=e-this.minx,n=i.getExtraWidth();return r<n&&(0===this.i||"bar"!==i.type||"part"!==this.children[this.i-1].type&&"tempo"!==this.children[this.i-1].type)&&(e+=n-r),i.setX(e),this.spacingduration=i.duration,this.minx=e+i.getMinWidth(),this.i!==this.children.length-1&&(this.minx+=i.minspacing),this.updateNextX(e,t),e},n.prototype.updateNextX=function(e,t){this.nextx=e+t*Math.sqrt(8*this.spacingduration)},n.prototype.shiftRight=function(e){var t=this.children[this.i];t&&(t.setX(t.x+e),this.minx+=e,this.nextx+=e)},n.prototype.draw=function(e,t){var i,n=this.w-1;if(e.staffbottom=this.staff.bottom,e.measureNumber=null,e.noteNumber=null,this.header){var a=14-(this.voicenumber+1)*(12/(this.voicetotal+1));e.renderText(e.padding.left,e.calcY(a),this.header,"voicefont","staff-extra voice-name","start")}for(var s=0,o=this.children.length;s<o;s++){var c=this.children[s],l=!1;"staff-extra"!==c.type&&null===e.measureNumber&&(e.measureNumber=0,e.noteNumber=0,l=!0),c.draw(e,this.barto||s===o-1?t:0),("note"===c.type||"rest"===(i=c).type&&i.abcelem&&i.abcelem.rest&&"spacer"!==i.abcelem.rest.type)&&e.noteNumber++,"bar"!==c.type||l||(e.measureNumber++,e.noteNumber=0)}e.measureNumber=0,e.noteNumber=0,r.each(this.beams,(function(t){"bar"===t?(e.measureNumber++,e.noteNumber=0):t.draw(e)})),e.measureNumber=0,e.noteNumber=0;var h=this;r.each(this.otherchildren,(function(t){"bar"===t?(e.measureNumber++,e.noteNumber=0):t.draw(e,h.startx+10,n)}))},n.prototype.layoutBeams=function(){for(var e=0;e<this.beams.length;e++)if(this.beams[e].layout){this.beams[e].layout(),this.moveDecorations(this.beams[e]);for(var t=0;t<this.beams[e].elems.length;t++)this.adjustRange(this.beams[e].elems[t])}for(e=0;e<this.otherchildren.length;e++){var i=this.otherchildren[e];i.layout&&(i.layout(),this.adjustRange(i))}this.staff.top=Math.max(this.staff.top,this.top),this.staff.bottom=Math.min(this.staff.bottom,this.bottom)},e.exports=n},function(e,t,i){var r=i(3),n=i(1),a=i(5),s=i(49),o=function(e,t,i){this.paper=new s(e),this.controller=null,this.space=3*n.SPACE,this.padding={},this.doRegression=t,this.shouldAddClasses=i,this.doRegression&&(this.regressionLines=[]),this.reset()};o.prototype.reset=function(){this.paper.clear(),this.y=0,this.abctune=null,this.lastM=null,this.ingroup=!1,this.path=null,this.isPrint=!1,this.initVerticalSpace(),this.doRegression&&(this.regressionLines=[])},o.prototype.newTune=function(e){this.abctune=e,this.setVerticalSpace(e.formatting),this.measureNumber=null,this.noteNumber=null,this.setPrintMode("print"===e.media),this.setPadding(e)},o.prototype.createElemSet=function(){return this.paper.openGroup()},o.prototype.closeElemSet=function(){return this.paper.closeGroup()},o.prototype.setPrintMode=function(e){this.isPrint=e},o.prototype.setPaperSize=function(e,t,i){var r=(e+this.padding.right)*t,n=(this.y+this.padding.bottom)*t;this.isPrint&&(n=Math.max(n,1056)),this.doRegression&&this.regressionLines.push("PAPER SIZE: ("+r+","+n+")");var a="Sheet Music";this.abctune&&this.abctune.metaText&&this.abctune.metaText.title&&(a+=' for "'+this.abctune.metaText.title+'"'),this.paper.setTitle(a);var s={overflow:"hidden"};"resize"===i?this.paper.setResponsiveWidth(r,n):(s.width="",s.height=n+"px",t<1?(s.width=r+"px",this.paper.setSize(r/t,n/t)):this.paper.setSize(r,n)),this.paper.setScale(t),this.paper.setParentStyles(s)},o.prototype.setPaddingOverride=function(e){this.paddingOverride={top:e.paddingtop,bottom:e.paddingbottom,right:e.paddingright,left:e.paddingleft}},o.prototype.setPadding=function(e){function t(t,i,r,n,a){void 0!==e.formatting[r]?t.padding[i]=e.formatting[r]:void 0!==t.paddingOverride[i]?t.padding[i]=t.paddingOverride[i]:t.isPrint?t.padding[i]=n:t.padding[i]=a}t(this,"top","topmargin",38,15),t(this,"bottom","botmargin",38,15),t(this,"left","leftmargin",68,15),t(this,"right","rightmargin",68,15)},o.prototype.adjustNonScaledItems=function(e){this.padding.top/=e,this.padding.bottom/=e,this.padding.left/=e,this.padding.right/=e,this.abctune.formatting.headerfont.size/=e,this.abctune.formatting.footerfont.size/=e},o.prototype.initVerticalSpace=function(){this.spacing={composer:7.56,graceBefore:8.67,graceInside:10.67,graceAfter:16,info:0,lineSkipFactor:1.1,music:7.56,paragraphSkipFactor:.4,parts:11.33,slurHeight:1,staffSeparation:61.33,stemHeight:36.67,subtitle:3.78,systemStaffSeparation:48,text:18.9,title:7.56,top:30.24,vocal:30.67,words:0}},o.prototype.setVerticalSpace=function(e){void 0!==e.staffsep&&(this.spacing.staffSeparation=4*e.staffsep/3),void 0!==e.composerspace&&(this.spacing.composer=4*e.composerspace/3),void 0!==e.partsspace&&(this.spacing.parts=4*e.partsspace/3),void 0!==e.textspace&&(this.spacing.text=4*e.textspace/3),void 0!==e.musicspace&&(this.spacing.music=4*e.musicspace/3),void 0!==e.titlespace&&(this.spacing.title=4*e.titlespace/3),void 0!==e.sysstaffsep&&(this.spacing.systemStaffSeparation=4*e.sysstaffsep/3),void 0!==e.subtitlespace&&(this.spacing.subtitle=4*e.subtitlespace/3),void 0!==e.topspace&&(this.spacing.top=4*e.topspace/3),void 0!==e.vocalspace&&(this.spacing.vocal=4*e.vocalspace/3),void 0!==e.wordsspace&&(this.spacing.words=4*e.wordsspace/3)},o.prototype.topMargin=function(e){this.moveY(this.padding.top)},o.prototype.addMusicPadding=function(){this.moveY(this.spacing.music)},o.prototype.addStaffPadding=function(e,t){var i=-(e.staffs[e.staffs.length-1].bottom-2),r=(t.staffs[0].top-10+i)*n.STEP;r<this.spacing.staffSeparation&&this.moveY(this.spacing.staffSeparation-r)},o.prototype.engraveTopText=function(e,t){if(t.metaText.header&&this.isPrint){var i=this.getTextSize("XXXX","headerfont","abcjs-header abcjs-meta-top").height;this.y-=i,this.outputTextIf(this.padding.left,t.metaText.header.left,"headerfont","header meta-top",0,null,"start"),this.outputTextIf(this.padding.left+e/2,t.metaText.header.center,"headerfont","header meta-top",0,null,"middle"),this.outputTextIf(this.padding.left+e,t.metaText.header.right,"headerfont","header meta-top",0,null,"end"),this.y+=i}if(this.isPrint&&this.moveY(this.spacing.top),this.outputTextIf(this.padding.left+e/2,t.metaText.title,"titlefont","title meta-top",this.spacing.title,0,"middle"),t.lines[0]&&this.outputTextIf(this.padding.left+e/2,t.lines[0].subtitle,"subtitlefont","text meta-top",this.spacing.subtitle,0,"middle"),t.metaText.rhythm||t.metaText.origin||t.metaText.composer){this.moveY(this.spacing.composer);var r=this.outputTextIf(this.padding.left,t.metaText.rhythm,"infofont","meta-top",0,null,"start"),n="";if(t.metaText.composer&&(n+=t.metaText.composer),t.metaText.origin&&(n+=" ("+t.metaText.origin+")"),n.length>0){var a=this.outputTextIf(this.padding.left+e,n,"composerfont","meta-top",0,null,"end");this.moveY(a[1])}else this.moveY(r[1]);this.moveY(-6)}this.outputTextIf(this.padding.left+e,t.metaText.author,"composerfont","meta-top",0,0,"end"),this.outputTextIf(this.padding.left,t.metaText.partOrder,"partsfont","meta-bottom",0,0,"start")},o.prototype.engraveExtraText=function(e,t){if(this.lineNumber=null,this.measureNumber=null,this.noteNumber=null,this.voiceNumber=null,t.metaText.unalignedWords){var i=this.getFontAndAttr("wordsfont","meta-bottom"),r=this.getTextSize("i","wordsfont","meta-bottom");t.metaText.unalignedWords.length>0&&this.moveY(this.spacing.words,1);for(var a=0;a<t.metaText.unalignedWords.length;a++)if(""===t.metaText.unalignedWords[a])this.moveY(i.font.size,1);else if("string"==typeof t.metaText.unalignedWords[a])this.outputTextIf(this.padding.left+n.INDENT,t.metaText.unalignedWords[a],"wordsfont","meta-bottom",0,0,"start");else{for(var s=0,o=0,c=0;c<t.metaText.unalignedWords[a].length;c++){var l=t.metaText.unalignedWords[a][c],h=l.font?l.font:"wordsfont",u=(this.renderText(this.padding.left+n.INDENT+o,this.y,l.text,h,"meta-bottom",!1),this.getTextSize(l.text,h,"meta-bottom"));s=Math.max(s,u.height),o+=u.width," "===l.text[l.text.length-1]&&(o+=r.width)}this.moveY(s,1)}t.metaText.unalignedWords.length>0&&this.moveY(i.font.size,2)}var d="";t.metaText.book&&(d+="Book: "+t.metaText.book+"\n"),t.metaText.source&&(d+="Source: "+t.metaText.source+"\n"),t.metaText.discography&&(d+="Discography: "+t.metaText.discography+"\n"),t.metaText.notes&&(d+="Notes: "+t.metaText.notes+"\n"),t.metaText.transcription&&(d+="Transcription: "+t.metaText.transcription+"\n"),t.metaText.history&&(d+="History: "+t.metaText.history+"\n"),t.metaText["abc-copyright"]&&(d+="Copyright: "+t.metaText["abc-copyright"]+"\n"),t.metaText["abc-creator"]&&(d+="Creator: "+t.metaText["abc-creator"]+"\n"),t.metaText["abc-edited-by"]&&(d+="Edited By: "+t.metaText["abc-edited-by"]+"\n"),this.outputTextIf(this.padding.left,d,"historyfont","meta-bottom",this.spacing.info,0,"start"),t.metaText.footer&&this.isPrint&&(this.outputTextIf(this.padding.left,t.metaText.footer.left,"footerfont","header meta-bottom",0,null,"start"),this.outputTextIf(this.padding.left+e/2,t.metaText.footer.center,"footerfont","header meta-bottom",0,null,"middle"),this.outputTextIf(this.padding.left+e,t.metaText.footer.right,"footerfont","header meta-bottom",0,null,"end"))},o.prototype.outputFreeText=function(e,t){t&&this.moveY(t);var i=this.getFontAndAttr("textfont","defined-text");if(""===e)this.moveY(2*i.attr["font-size"]);else if("string"==typeof e)this.moveY(i.attr["font-size"]/2),this.outputTextIf(this.padding.left,e,"textfont","defined-text",0,0,"start");else{for(var r="",n=!1,a=0;a<e.length;a++)e[a].font&&(r+="FONT("+e[a].font+")"),r+=e[a].text,e[a].center&&(n=!0);var s=n?"middle":"start",o=n?this.controller.width/2:this.padding.left;this.outputTextIf(o,r,"textfont","defined-text",0,1,s)}},o.prototype.outputSeparator=function(e){e.lineLength&&(this.moveY(e.spaceAbove),this.printSeparator(e.lineLength),this.moveY(e.spaceBelow))},o.prototype.outputSubtitle=function(e,t){this.outputTextIf(this.padding.left+e/2,t,"subtitlefont","text meta-top",this.spacing.subtitle,0,"middle")},o.prototype.beginGroup=function(){this.path=[],this.lastM=[0,0],this.ingroup=!0},o.prototype.addPath=function(e){if(0!==(e=e||[]).length){e[0][0]="m",e[0][1]-=this.lastM[0],e[0][2]-=this.lastM[1],this.lastM[0]+=e[0][1],this.lastM[1]+=e[0][2],this.path.push(e[0]);for(var t=1,i=e.length;t<i;t++)"m"===e[t][0]&&(this.lastM[0]+=e[t][1],this.lastM[1]+=e[t][2]),this.path.push(e[t])}},o.prototype.endGroup=function(e){if(this.ingroup=!1,0===this.path.length)return null;for(var t="",i=0;i<this.path.length;i++)t+=this.path[i].join(" ");var r=this.paper.path({path:t,stroke:"none",fill:"#000000",class:this.addClasses(e)});return this.path=[],this.doRegression&&this.addToRegression(r),r},o.prototype.printStaveLine=function(e,t,i,r){var n="staff";void 0!==r&&(n+=" "+r);var s=.35,o="#000000";var c=this.calcY(i),l=a("M %f %f L %f %f L %f %f L %f %f z",e,c-s,t,c-s,t,c+s,e,c+s),h=this.paper.pathToBack({path:l,stroke:"none",fill:o,class:this.addClasses(n)});return this.doRegression&&this.addToRegression(h),h},o.prototype.printStem=function(e,t,i,r){if(t<0){var n=r;r=i,i=n}var a="#000000";~~e===e&&(e+=.05);var s=[["M",e,i],["L",e,r],["L",e+t,r],["L",e+t,i],["z"]];if(!this.ingroup){for(var o="",c=0;c<s.length;c++)o+=s[c].join(" ");var l=this.paper.pathToBack({path:o,stroke:"none",fill:a,class:this.addClasses("stem")});return this.doRegression&&this.addToRegression(l),l}this.addPath(s)},o.prototype.printSymbol=function(e,t,i,n,a,s){var o,c,l,h,u,d;if(!i)return null;if(i.length>1&&i.indexOf(".")<0){this.paper.openGroup();for(var p=0,f=0;f<i.length;f++){var m=i.charAt(f);c=r.getYCorr(m),(o=r.printSymbol(e+p,this.calcY(t+c),m,this.paper,s))?(this.doRegression&&this.addToRegression(o),f<i.length-1&&(p+=(l=m,h=i.charAt(f+1),u=r.getSymbolWidth(m),d=void 0,d=u,"f"===l&&"f"===h&&(d=2*d/3),"p"===l&&"p"===h&&(d=5*d/6),"f"===l&&"z"===h&&(d=5*d/8),d))):this.renderText(e,this.y,"no symbol:"+i,"debugfont","debug-msg","start")}return this.paper.closeGroup()}if(c=r.getYCorr(i),this.ingroup)this.addPath(r.getPathForSymbol(e,this.calcY(t+c),i,n,a));else{if(o=r.printSymbol(e,this.calcY(t+c),i,this.paper,s))return this.doRegression&&this.addToRegression(o),o;this.renderText(e,this.y,"no symbol:"+i,"debugfont","debug-msg","start")}return null},o.prototype.scaleExistingElem=function(e,t,i,r,n){this.paper.setAttributeOnElement(e,{style:"transform:scale("+t+","+i+");transform-origin:"+r+"px "+n+"px;"})},o.prototype.printPath=function(e){var t=this.paper.path(e);return this.doRegression&&this.addToRegression(t),t},o.prototype.drawBrace=function(e,t,i){var r=i-t,n=[7.5,-8,21,0,18.5,-10.5,7.5],s=[0,r/5.5,r/3.14,r/2,r/2.93,r/4.88,0],o=a("M %f %f C %f %f %f %f %f %f C %f %f %f %f %f %f z",e+n[0],t+s[0],e+n[1],t+s[1],e+n[2],t+s[2],e+n[3],t+s[3],e+n[4],t+s[4],e+n[5],t+s[5],e+n[6],t+s[6]),c=this.paper.path({path:o,stroke:"#000000",fill:"#000000",class:this.addClasses("brace")});o=a("M %f %f C %f %f %f %f %f %f C %f %f %f %f %f %f z",e+(n=[0,17.5,-7.5,6.6,-5,20,0])[0],t+(s=[r/2,r/1.46,r/1.22,r,r/1.19,r/1.42,r/2])[0],e+n[1],t+s[1],e+n[2],t+s[2],e+n[3],t+s[3],e+n[4],t+s[4],e+n[5],t+s[5],e+n[6],t+s[6]);var l=this.paper.path({path:o,stroke:"#000000",fill:"#000000",class:this.addClasses("brace")});return this.doRegression&&(this.addToRegression(c),this.addToRegression(l)),c+l},o.prototype.drawArc=function(e,t,i,r,n,s,o){var c=o?1.2:1.5;e+=6,t+=4,i+=n?c:-c,r+=n?c:-c;var l=this.calcY(i),h=this.calcY(r),u=t-e,d=h-l,p=Math.sqrt(u*u+d*d),f=u/p,m=d/p,g=p/3.5,v=o?10:25,b=(n?-1:1)*Math.min(v,Math.max(4,g)),y=e+g*f-b*m,w=l+g*m+b*f,k=t-g*f-b*m,x=h-g*m+b*f,S=a("M %f %f C %f %f %f %f %f %f C %f %f %f %f %f %f z",e,l,y,w,k,x,t,h,k-2*m,x+2*f,y-2*m,w+2*f,e,l);s?s+=" slur":s="slur";var _=this.paper.path({path:S,stroke:"none",fill:"#000000",class:this.addClasses(s)});return this.doRegression&&this.addToRegression(_),_},o.prototype.calcY=function(e){return this.y-e*n.STEP},o.prototype.printStave=function(e,t,i){var r="top-line";if(this.paper.openGroup({prepend:!0}),1!==i){for(var n=i-1;n>=0;n--)this.printStaveLine(e,t,2*(n+1),r),r=void 0;this.paper.closeGroup()}else this.printStaveLine(e,t,6,r)},o.prototype.addClasses=function(e,t){if(!this.shouldAddClasses)return"";var i=[];if(e.length>0&&i.push(e),null!==this.lineNumber&&void 0!==this.lineNumber&&i.push("l"+this.lineNumber),null!==this.measureNumber&&void 0!==this.measureNumber&&i.push("m"+this.measureNumber),null!==this.voiceNumber&&void 0!==this.voiceNumber&&i.push("v"+this.voiceNumber),(e.indexOf("note")>=0||e.indexOf("rest")>=0||e.indexOf("lyric")>=0)&&null!==this.noteNumber&&void 0!==this.noteNumber&&i.push("n"+this.noteNumber),i.length>0){i=(i=i.join(" ")).split(" ");for(var r=0;r<i.length;r++)0!==i[r].indexOf("abcjs-")&&i[r].length>0&&(i[r]="abcjs-"+i[r])}return i.join(" ")},o.prototype.getFontAndAttr=function(e,t){var i,r={"font-size":(i="string"==typeof e?(i=this.abctune.formatting[e])?{face:i.face,size:4*i.size/3,decoration:i.decoration,style:i.style,weight:i.weight,box:i.box}:{face:"Arial",size:16,decoration:"underline",style:"normal",weight:"normal"}:{face:e.face,size:4*e.size/3,decoration:e.decoration,style:e.style,weight:e.weight,box:e.box}).size,"font-style":i.style,"font-family":i.face,"font-weight":i.weight,"text-decoration":i.decoration,class:this.addClasses(t),font:""};return{font:i,attr:r}},o.prototype.getTextSize=function(e,t,i,r){var n=this.getFontAndAttr(t,i),a=this.paper.getTextSize(e,n.attr,r);return n.font.box&&(a.height+=8,a.width+=8),a},o.prototype.renderText=function(e,t,i,r,n,a,s){var o=this.getFontAndAttr(r,n);a&&(o.attr["text-anchor"]=a),o.attr.x=e,o.attr.y=t+7,s||(o.attr.dy="0.5em"),"debugfont"===r&&(console.log("Debug msg: "+i),o.attr.stroke="#ff0000"),i=(i=i.replace(/\n\n/g,"\n \n")).replace(/^\n/," \n"),o.font.box&&(o.attr.x+=2,o.attr.y+=4);var c=this.paper.text(i,o.attr);if(o.font.box){var l=this.getTextSize(i,r,n);this.paper.rect({x:e-2,y:t,width:l.width+4,height:l.height+4-2,stroke:"#888888",fill:"transparent"})}return this.doRegression&&this.addToRegression(c),c},o.prototype.moveY=function(e,t){void 0===t&&(t=1),this.y+=e*t},o.prototype.skipSpaceY=function(){this.y+=this.space},o.prototype.outputTextIf=function(e,t,i,r,n,a,s){if(t){n&&this.moveY(n);this.renderText(e,this.y,t,i,r,s);var o=this.getTextSize(t,i,r),c=isNaN(o.width)?0:o.width,l=isNaN(o.height)?0:o.height;if(this.getFontAndAttr(i,r).font.box&&(c+=8,l+=8),null!==a){var h=t.split("\n").length;isNaN(o.height)||this.moveY(l/h,h+a)}return[c,l]}return[0,0]},o.prototype.addInvisibleMarker=function(e){var t=this.y;t=Math.round(t);var i=a("M %f %f L %f %f L %f %f L %f %f z",0,t-.35,100,t-.35,100,t+.35,0,t+.35);this.paper.pathToBack({path:i,stroke:"none",fill:"rgba(0,0,0,0)","fill-opacity":0,class:this.addClasses(e),"data-vertical":t})},o.prototype.printSeparator=function(e){var t=Math.round(this.y),i=(this.controller.width-e)/2,r=i+e,n="M "+i+" "+t+" L "+r+" "+t+" L "+r+" "+(t+1)+" L "+i+" "+(t+1)+" L "+i+" "+t+" z";this.paper.pathToBack({path:n,stroke:"rgba(0,0,0,0)",fill:"rgba(0,0,0,255)",class:this.addClasses("defined-text")})},o.prototype.printHorizontalLine=function(e,t,i){var r=.35,n="rgba(0,0,255,.4)",s=this.y;t&&(s=t),s=Math.round(s),this.paper.text(""+Math.round(s),{x:10,y:s,"text-anchor":"start","font-size":"18px",fill:n,stroke:n});var o=a("M %f %f L %f %f L %f %f L %f %f z",50,s-r,50+e,s-r,e,s+r,50,s+r);this.paper.pathToBack({path:o,stroke:"none",fill:n,class:this.addClasses("staff")});for(var c=1;c<e/100;c++)o=a("M %f %f L %f %f L %f %f L %f %f z",100*c-r,s-5,100*c-r,s+5,100*c+r,s-5,100*c+r,s+5),this.paper.pathToBack({path:o,stroke:"none",fill:n,class:this.addClasses("staff")});i&&this.paper.text(i,{x:e+70,y:s,"text-anchor":"start","font-size":"18px",fill:n,stroke:n})},o.prototype.printShadedBox=function(e,t,i,r,n,a,s){var o=this.paper.rect({x:e,y:t,width:i,height:r,fill:n,stroke:n,"fill-opacity":a,"stroke-opacity":a});return s&&this.paper.text(s,{x:0,y:t+7,"text-anchor":"start","font-size":"14px",fill:"rgba(0,0,255,.4)",stroke:"rgba(0,0,255,.4)"}),o},o.prototype.printVerticalLine=function(e,t,i){var r=a("M %f %f L %f %f L %f %f L %f %f z",e-.35,t,e-.35,i,e+.35,t,e+.35,i);this.paper.pathToBack({path:r,stroke:"none",fill:"#00aaaa",class:this.addClasses("staff")}),r=a("M %f %f L %f %f L %f %f L %f %f z",e-20,t,e-20,t+3,e,t,e,t+3),this.paper.pathToBack({path:r,stroke:"none",fill:"#00aaaa",class:this.addClasses("staff")}),r=a("M %f %f L %f %f L %f %f L %f %f z",e+20,i,e+20,i+3,e,i,e,i+3),this.paper.pathToBack({path:r,stroke:"none",fill:"#00aaaa",class:this.addClasses("staff")})},o.prototype.addToRegression=function(e){var t;try{t=e.getBBox()}catch(e){t={width:0,height:0}}var i=e.type+" "+t.toString()+" ",r=[];for(var n in e.attrs)e.attrs.hasOwnProperty(n)&&("class"===n?i=e.attrs[n]+" "+i:r.push(n+": "+e.attrs[n]));r.sort(),i+="{ "+r.join(" ")+" }",this.regressionLines.push(i)},e.exports=o},function(e,t){var i="http://www.w3.org/2000/svg";function r(e){this.svg=n(),e.appendChild(this.svg)}function n(){var e=document.createElementNS(i,"svg");return e.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:xlink","http://www.w3.org/1999/xlink"),e.setAttribute("role","img"),e}r.prototype.clear=function(){if(this.svg){var e=this.svg.parentNode;this.svg=n(),e&&(e.innerHTML="",e.appendChild(this.svg))}},r.prototype.setTitle=function(e){var t=document.createElement("title"),i=document.createTextNode(e);t.appendChild(i),this.svg.insertBefore(t,this.svg.firstChild)},r.prototype.setResponsiveWidth=function(e,t){if(this.svg.setAttribute("viewBox","0 0 "+e+" "+t),this.svg.setAttribute("preserveAspectRatio","xMinYMin meet"),this.svg.removeAttribute("height"),this.svg.removeAttribute("width"),this.svg.style.display="inline-block",this.svg.style.position="absolute",this.svg.style.top="0",this.svg.style.left="0",this.svg.parentNode){var i=this.svg.parentNode.getAttribute("class");i?i.indexOf("abcjs-container")<0&&this.svg.parentNode.setAttribute("class",i+" abcjs-container"):this.svg.parentNode.setAttribute("class","abcjs-container"),this.svg.parentNode.style.display="inline-block",this.svg.parentNode.style.position="relative",this.svg.parentNode.style.width="100%";var r=t/e*100;this.svg.parentNode.style["padding-bottom"]=r+"%",this.svg.parentNode.style["vertical-align"]="middle",this.svg.parentNode.style.overflow="hidden"}},r.prototype.setSize=function(e,t){this.svg.setAttribute("width",e),this.svg.setAttribute("height",t)},r.prototype.setScale=function(e){1!==e?(this.svg.style.transform="scale("+e+","+e+")",this.svg.style["-ms-transform"]="scale("+e+","+e+")",this.svg.style["-webkit-transform"]="scale("+e+","+e+")",this.svg.style["transform-origin"]="0 0",this.svg.style["-ms-transform-origin-x"]="0",this.svg.style["-ms-transform-origin-y"]="0",this.svg.style["-webkit-transform-origin-x"]="0",this.svg.style["-webkit-transform-origin-y"]="0"):(this.svg.style.transform="",this.svg.style["-ms-transform"]="",this.svg.style["-webkit-transform"]="")},r.prototype.setParentStyles=function(e){for(var t in e)e.hasOwnProperty(t)&&this.svg.parentNode&&(this.svg.parentNode.style[t]=e[t]);this.dummySvg&&(document.querySelector("body").removeChild(this.dummySvg),this.dummySvg=null)},r.prototype.rect=function(e){var t=document.createElementNS(i,"rect");for(var r in e){if(e.hasOwnProperty(r))(""+e[r]).indexOf("NaN"),t.setAttributeNS(null,r,e[r])}return this.append(t),t},r.prototype.text=function(e,t,r){var n=document.createElementNS(i,"text");for(var a in t)t.hasOwnProperty(a)&&n.setAttribute(a,t[a]);for(var s=(""+e).split("\n"),o=0;o<s.length;o++){var c=document.createElementNS(i,"tspan");c.textContent=s[o],c.setAttribute("x",t.x?t.x:0),0!==o&&c.setAttribute("dy","1.2em"),n.appendChild(c)}return r?r.appendChild(n):this.append(n),n},r.prototype.guessWidth=function(e,t){var i,r=this.createDummySvg(),n=this.text(e,t,r);try{i=n.getBBox(),i=isNaN(i.height)||!i.height?{width:t["font-size"]/2,height:t["font-size"]+2}:{width:i.width,height:i.height}}catch(e){i={width:t["font-size"]/2,height:t["font-size"]+2}}return r.removeChild(n),i},r.prototype.createDummySvg=function(){if(!this.dummySvg){this.dummySvg=n();this.dummySvg.setAttribute("style",["display: block !important;","height: 1px;","width: 1px;","position: absolute;"].join("")),document.querySelector("body").appendChild(this.dummySvg)}return this.dummySvg},r.prototype.getTextSize=function(e,t,i){if("number"==typeof e&&(e=""+e),!e||e.match(/^\s+$/))return{width:0,height:0};var r,n=!i;i||(i=this.text(e,t));try{r=i.getBBox(),r=isNaN(r.height)||!r.height?this.guessWidth(e,t):{width:r.width,height:r.height}}catch(i){r=this.guessWidth(e,t)}return n&&(this.currentGroup?this.currentGroup.removeChild(i):this.svg.removeChild(i)),r},r.prototype.openGroup=function(e){e=e||{};var t=document.createElementNS(i,"g");return e.prepend?this.svg.insertBefore(t,this.svg.firstChild):this.svg.appendChild(t),this.currentGroup=t,t},r.prototype.closeGroup=function(){var e=this.currentGroup;return this.currentGroup=null,e},r.prototype.path=function(e){var t=document.createElementNS(i,"path");for(var r in e)e.hasOwnProperty(r)&&("path"===r?t.setAttributeNS(null,"d",e.path):t.setAttributeNS(null,r,e[r]));return this.append(t),t},r.prototype.pathToBack=function(e){var t=document.createElementNS(i,"path");for(var r in e)e.hasOwnProperty(r)&&("path"===r?t.setAttributeNS(null,"d",e.path):t.setAttributeNS(null,r,e[r]));return this.prepend(t),t},r.prototype.append=function(e){this.currentGroup?this.currentGroup.appendChild(e):this.svg.appendChild(e)},r.prototype.prepend=function(e){this.currentGroup?this.currentGroup.appendChild(e):this.svg.insertBefore(e,this.svg.firstChild)},r.prototype.setAttributeOnElement=function(e,t){for(var i in t)t.hasOwnProperty(i)&&e.setAttributeNS(null,i,t[i])},e.exports=r},function(e,t,i){var r=i(21);e.exports=function(e,t,i,n){return new Promise((function(a,s){r[t]||(r[t]={});var o=r[t];if("error"===o[i])return s(new Error("Unable to load sound font "+e+" "+t+" "+i));if(o[i])return a({instrument:t,name:i});o[i]="pending";var c=new XMLHttpRequest;c.open("GET",e+t+"-mp3/"+i+".mp3",!0),c.responseType="arraybuffer";var l=this;function h(e){o[i]=e,a({instrument:t,name:i})}function u(e){l.debugCallback&&l.debugCallback(e),console.log(e),s(e)}c.onload=function(t){if(200!==this.status){o[i]="error";var r="Onload error loading sound: "+i+" "+e+" "+t.currentTarget.status+" "+t.currentTarget.statusText;return l.debugCallback&&l.debugCallback(r),s(new Error(r))}n.decodeAudioData(this.response,h,u)},c.addEventListener("error",(function(){o[i]="error";var t="Error in loading sound:  "+e;return l.debugCallback&&l.debugCallback(t),s(new Error(t))}),!1),c.send()}))}},function(e,t,i){var r=i(14);e.exports=function(e){for(var t=[],i=0;i<e.tracks.length;i++)t.push([]);var n={},a=r[0];return e.tracks.forEach((function(e,i){var s=0;e.forEach((function(e){switch(e.cmd){case"start":n[e.pitch]={time:s,instrument:a,volume:e.volume};break;case"move":s+=e.duration;break;case"stop":t[i].push({pitch:e.pitch,instrument:n[e.pitch].instrument,start:n[e.pitch].time,end:s,volume:n[e.pitch].volume}),delete n[e.pitch];break;case"program":a=r[e.instrument];break;default:console.log("Unhanded midi event",e)}}))})),t}},function(e,t){e.exports=function(e){return window.URL.createObjectURL(function(e){var t,i,r=e.length,n=e[0].length*r*2+44,a=new ArrayBuffer(n),s=new DataView(a),o=[],c=0,l=0;for(u(1179011410),u(n-8),u(1163280727),u(544501094),u(16),h(1),h(r),u(e[0].sampleRate),u(2*e[0].sampleRate*r),h(2*r),h(16),u(1635017060),u(n-l-4),t=0;t<e.length;t++)o.push(e[t].getChannelData(0));for(;l<n;){for(t=0;t<o.length;t++)i=0|(.5+(i=Math.max(-1,Math.min(1,o[t][c])))<0?32768*i:32767*i),s.setInt16(l,i,!0),l+=2;c++}return new Blob([a],{type:"audio/wav"});function h(e){s.setUint16(l,e,!0),l+=2}function u(e){s.setUint32(l,e,!0),l+=4}}(e.audioBuffers))}},function(e,t){var i;!function(){"use strict";var e;function t(e){var t=.25;e.duration&&(t=e.duration[0]);var i=60;return e.bpm&&(i=e.bpm),t*i/.25}function r(t){var i;switch(t.type){case"common_time":i={el_type:"meter",num:4,den:4};break;case"cut_time":i={el_type:"meter",num:2,den:2};break;case"specified":i={el_type:"meter",num:t.value[0].num,den:t.value[0].den};break;default:i={el_type:"meter"}}return e=i.num/i.den,i}i=function(i,n){var a=180,s=(n=n||{}).program||0,o=n.midiTranspose||0,c=n.channel||0,l=n.drum||"",h=n.drumBars||1,u=n.drumIntro||0,d=""!==l;s=parseInt(s,10),o=parseInt(o,10),10===(c=parseInt(c,10))&&(s=128),l=l.split(" "),h=parseInt(h,10),u=parseInt(u,10);var p=i.formatting.bagpipes;if(p&&(s=71),i.formatting.midi){var f=i.formatting.midi;f.program&&f.program.length>0&&(s=f.program[0],f.program.length>1&&(s=f.program[1],c=f.program[0])),f.transpose&&(o=f.transpose[0]),f.channel&&(c=f.channel[0]),f.drum&&(l=f.drum),f.drumbars&&(h=f.drumbars[0]),f.drumon&&(d=!0),10===c&&(s=128)}i.metaText.tempo&&(a=t(i.metaText.tempo)),n.qpm&&(a=parseInt(n.qpm,10));var m=[];p&&m.push({el_type:"bagpipes"}),m.push({el_type:"instrument",program:s}),c&&m.push({el_type:"channel",channel:c}),o&&m.push({el_type:"transpose",transpose:o}),m.push({el_type:"tempo",qpm:a});for(var g=[],v=[],b=[],y=!1,w=0;w<i.lines.length;w++){var k=i.lines[w];if(k.staff)for(var x=k.staff,S=0,_=0;_<x.length;_++)for(var T=x[_],A=0;A<T.voices.length;A++){var C=T.voices[A];if(g[S]||(g[S]=[].concat(JSON.parse(JSON.stringify(m)))),T.clef&&"perc"===T.clef.type)for(var N=0;N<g[S].length;N++)"instrument"===g[S][N].el_type&&(g[S][N].program=128);else T.key&&("HP"===T.key.root?g[S].push({el_type:"key",accidentals:[{acc:"natural",note:"g"},{acc:"sharp",note:"f"},{acc:"sharp",note:"c"}]}):g[S].push({el_type:"key",accidentals:T.key.accidentals}));T.meter&&g[S].push(r(T.meter)),!y&&d&&(g[S].push({el_type:"drum",params:{pattern:l,bars:h,on:d,intro:u}}),y=!0),T.clef&&T.clef.transpose&&(T.clef.el_type="clef",g[S].push({el_type:"transpose",transpose:T.clef.transpose})),i.formatting.midi&&i.formatting.midi.drumoff&&(g[S].push({el_type:"bar"}),g[S].push({el_type:"drum",params:{pattern:"",on:!1}}));for(var E=0,M=0;M<C.length;M++){var B=C[M];switch(B.el_type){case"note":B.rest&&"spacer"===B.rest.type||(g[S].push(B),E++);break;case"key":"HP"===B.root?g[S].push({el_type:"key",accidentals:[{acc:"natural",note:"g"},{acc:"sharp",note:"f"},{acc:"sharp",note:"c"}]}):g[S].push({el_type:"key",accidentals:B.accidentals});break;case"meter":g[S].push(r(B));break;case"clef":B.transpose&&g[S].push({el_type:"transpose",transpose:B.transpose});break;case"tempo":a=t(B),g[S].push({el_type:"tempo",qpm:a});break;case"bar":E>0&&g[S].push({el_type:"bar"}),E=0;var P="bar_right_repeat"===B.type||"bar_dbl_repeat"===B.type,L="1"===B.startEnding,H="bar_left_repeat"===B.type||"bar_dbl_repeat"===B.type||"bar_right_repeat"===B.type;if(P){var z=v[S];z||(z=0);var I=b[S];I||(I=g[S].length),g[S]=g[S].concat(g[S].slice(z,I)),b[S]=void 0,v[S]=void 0}L&&(b[S]=g[S].length),H&&(v[S]=g[S].length);break;case"style":case"part":break;case"stem":case"scale":break;case"midi":var D=!1;switch(B.cmd){case"drumon":d=!0,D=!0;break;case"drumoff":d=!1,D=!0;break;case"drum":l=B.params,D=!0;break;case"drumbars":h=B.params[0],D=!0;break;case"drummap":break;case"program":g[S].push({el_type:"instrument",program:B.params[0]});break;case"transpose":g[S].push({el_type:"transpose",transpose:B.params[0]});break;case"gchordoff":g[S].push({el_type:"gchord",tacet:!0});break;case"gchordon":g[S].push({el_type:"gchord",tacet:!1});break;case"beat":g[S].push({el_type:"beat",beats:B.params});break;default:console.log("MIDI seq: midi cmd not handled: ",B.cmd,B)}D&&(g[0].push({el_type:"drum",params:{pattern:l,bars:h,intro:u,on:d}}),y=!0);break;default:console.log("MIDI: element type "+B.el_type+" not handled.")}}S++}}if(u)for(var O=i.getPickupLength(),F=0;F<g.length;F++){for(var Y=0;"note"!==g[F][Y].el_type&&g[F].length>Y;)Y++;if(g[F].length>Y)for(var j=0;j<u;j++)0===O||j<u-1?g[F].splice(Y,0,{el_type:"note",rest:{type:"rest"},duration:e},{el_type:"bar"}):g[F].splice(Y,0,{el_type:"note",rest:{type:"rest"},duration:e-O})}return g}}(),e.exports=i},function(e,t){var i;!function(){"use strict";var e,t,r,n,a,s,o,c,l,h,u,d,p,f,m,g,v,b,y,w,k,x=1,S={num:4,den:4},_=0,T=128,A=!1,C=64,N=64,E=64,M=.25,B={},P=1/128;function L(e){for(var t=0,i=0;i<e.length;i++){for(var r=e[i],n=0,a=0;a<r.length;a++){var s=r[a];s.duration&&(n+=s.duration)}t=Math.max(t,n)}return t}function H(e){switch(e.den){case 2:return.5;case 4:return.25;case 8:return.375;case 16:return.125}return.25}i=function(i,_){_||(_={}),e=[],t=[0,0,0,0,0,0,0],n=!1,a=1,s=[],o=void 0,c=void 0,x=1,l=void 0,h=void 0,u=void 0,f=void 0,d={},S={num:4,den:4},m=[],v=i.length,g=!1,b=[],y=void 0,w=0,A=!!_.chordsOff,C=64,N=64,E=64,M=.25,k=[],!1,B={};for(var T=0;T<i.length;T++){r=0,p=-1;var P=i[T];u=[{cmd:"program",channel:T,instrument:l}],f=0,d={};for(var z=0;z<P.length;z++){var I=P[z];switch(I.el_type){case"note":D(I,_.voicesOff);break;case"key":t=Y(I);break;case"meter":c||(c=I),M=H(S=I);break;case"tempo":o?x=I.qpm?o/I.qpm:1:o=I.qpm;break;case"transpose":r=I.transpose;break;case"bar":m.length>0&&0===T&&(Q(),b=[]),w=0,e=[],0===T&&$(i.length+1);break;case"bagpipes":n=!0;break;case"instrument":void 0===l&&(l=I.program),h=I.program,u.push({cmd:"program",channel:T,instrument:I.program});break;case"channel":break;case"drum":B=Z(I.params);break;case"gchord":_.chordsOff||(A=I.tacet);break;case"beat":C=I.beats[0],N=I.beats[1],E=I.beats[2];break;default:console.log("MIDI creation. Unknown el_type: "+I.el_type+"\n")}}void 0===u[0].instrument&&(u[0].instrument=l||0),s.push(u),m.length>0&&(g=!0),k.length>0&&!0}return m.length>0&&s.push(m),k.length>0&&s.push(k),{tempo:o,instrument:l,tracks:s,totalDuration:L(s)}};var z=["break","(break)","no chord","n.c.","tacet"];function I(){for(var e=0,t=0;t<u.length;t++)"move"===u[t].cmd&&(e+=u[t].duration);return e}function D(e,t){var i;i=0===w?C:w%M<.001?N:E;var s=t?0:i,o=function(e){if(A)return"break";if(g||!e.chord||0===e.chord.length)return null;for(var t=0;t<e.chord.length;t++){var i=e.chord[t];if("default"===i.position)return i.name;if(z.indexOf(i.name.toLowerCase())>=0)return"break"}return null}(e);if(o){var c=function(e){if(0===e.length)return;if("break"===e)return{chick:[]};var t=e.substring(0,1);if("("===t){if(0===(e=e.substring(1,e.length-2)).length)return;t=e.substring(0,1)}var i=R[t];if(!i)return;var n,a=(i+=r)-5;1===e.length&&(n=q(i,""));var s=e.substring(1),o=s.substring(0,1);"b"===o||"♭"===o?(i--,a--,s=s.substring(1)):"#"!==o&&"♯"!==o||(i++,a++,s=s.substring(1));var c=s.split("/");if(n=q(i,c[0]),2===c.length){R[c[1]]&&(i=R[c[1]]+r,a=i)}return{boom:i,boom2:a,chick:n}}(o);if(c){if(0===m.length){m.push({cmd:"program",channel:v,instrument:_});var l=I();l>0&&m.push({cmd:"move",duration:l*x})}y=c,b.push({chord:y,beat:w})}}e.startTriplet&&(a=e.tripletMultiplier);var k,S=(e.durationClass?e.durationClass:e.duration)*a;if(w+=S,e.gracenotes){var T=n||p<0||0===u.length,B=T?S:u[p].duration;k=function(e,t){for(var i,r=0,n=[],a=0;a<e.length;a++)i=e[a],r+=i.duration;var s=2*(r/=j)>t?t/(2*r):1;for(a=0;a<e.length;a++){var o=(i=e[a]).midipitch?i.midipitch-60:i.pitch;n.push({pitch:o,duration:i.duration/j*s})}return n}(e.gracenotes,B),n||(S=V(k,T,S,null,s))}if(e.currentTrackMilliseconds=f,e.pitches){k&&n&&(S=V(k,!0,S,null,s));var L=[];e.midiPitches=[];for(var H=0;H<e.pitches.length;H++){var D=e.pitches[H],O=F(D);if(L.push({pitch:O,startTie:D.startTie}),e.midiPitches.push({pitch:O+60,durationInMeasures:S*x,volume:i,instrument:h}),d[""+O]){for(var Y=u.length-1;Y>=0;Y--)if("start"===u[Y].cmd&&u[Y].pitch===O){for(var G=u[Y].elem.midiPitches,W=0;W<G.length;W++)G[W].pitch-60===O&&(G[W].durationInMeasures+=S*x);break}}else u.push({cmd:"start",pitch:O,volume:s});D.startTie?(d[""+O]=!0,u[u.length-1].elem=e):D.endTie&&(d[""+O]=!1)}if(e.gracenotes)for(var U=0;U<e.gracenotes.length;U++){e.midiGraceNotePitches=[];var X=e.gracenotes[U];e.midiGraceNotePitches.push({pitch:F(X)+60,durationInMeasures:0,volume:i,instrument:h})}var K=P,Q=S-P;Q<0&&(Q=0,K=0),u.push({cmd:"move",duration:Q*x}),p=u.length-1,f+=Q*x;for(var Z=0;Z<L.length;Z++)d[""+L[Z].pitch]||u.push({cmd:"stop",pitch:L[Z].pitch});u.push({cmd:"move",duration:K*x}),f+=K*x}else e.rest&&(u.push({cmd:"move",duration:S*x}),f+=S*x);e.endTriplet&&(a=1)}var O=[0,2,4,5,7,9,11];function F(i){if(i.midipitch)return i.midipitch-60;var n=i.pitch;if(i.accidental)switch(i.accidental){case"sharp":e[n]=1;break;case"flat":e[n]=-1;break;case"natural":e[n]=0;break;case"dblsharp":e[n]=2;break;case"dblflat":e[n]=-2}var a=12*function(e){return Math.floor(e/7)}(n)+O[G(n)];return void 0!==e[n]?a+=e[n]:a+=t[G(n)],a+=r}function Y(e){var t=[0,0,0,0,0,0,0];if(!e.accidentals)return t;for(var i=0;i<e.accidentals.length;i++){var r=e.accidentals[i],n="sharp"===r.acc?1:"natural"===r.acc?0:-1;t[G(r.note.toLowerCase().charCodeAt(0)-"c".charCodeAt(0))]+=n}return t}var j=8;function V(e,t,i,r,n){for(var a=0;a<e.length;a++){var s=F(e[a]);s!==r&&u.push({cmd:"start",pitch:s,volume:n}),u.push({cmd:"move",duration:e[a].duration*x}),s!==r&&u.push({cmd:"stop",pitch:s}),t||(u[p].duration-=e[a].duration),i-=e[a].duration}return i}function G(e){return(e%=7)<0&&(e+=7),e}var R={A:-27,B:-25,C:-24,D:-22,E:-20,F:-19,G:-17};var W={dim:[0,3,6],"°":[0,3,6],"˚":[0,3,6],dim7:[0,3,6,9],"°7":[0,3,6,9],"˚7":[0,3,6,9],"ø7":[0,3,6,10],"m7(b5)":[0,3,6,10],m7b5:[0,3,6,10],"-7(b5)":[0,3,6,10],"-7b5":[0,3,6,10],"7b5":[0,4,6,10],"7(b5)":[0,4,6,10],"7♭5":[0,4,6,10],"7(b9,b5)":[0,4,6,10,13],"7b9,b5":[0,4,6,10,13],"7(#9,b5)":[0,4,6,10,15],"7#9b5":[0,4,6,10,15],"maj7(b5)":[0,3,6,11],maj7b5:[0,3,6,11],"13(b5)":[0,4,6,10,14,18],"13b5":[0,4,6,10,14,18],m:[0,3,7],"-":[0,3,7],m6:[0,3,7,9],"-6":[0,3,7,9],m7:[0,3,7,10],"-7":[0,3,7,10],"-(b6)":[0,3,7,8],"-b6":[0,3,7,8],"-6/9":[0,3,7,9,14],"-7(b9)":[0,3,7,10,13],"-7b9":[0,3,7,10,13],"-maj7":[0,3,7,11],"-9+7":[0,3,7,11,13],"-11":[0,3,7,11,14,16],M:[0,4,7],6:[0,4,7,9],"6/9":[0,4,7,9,14],7:[0,4,7,10],9:[0,4,7,10,14],11:[0,4,7,10,14,16],13:[0,4,7,10,14,18],"7b9":[0,4,7,10,13],"7♭9":[0,4,7,10,13],"7(b9)":[0,4,7,10,13],"7(#9)":[0,4,7,10,15],"7#9":[0,4,7,10,15],"(13)":[0,4,7,10,14,18],"7(9,13)":[0,4,7,10,14,18],"7(#9,b13)":[0,4,7,10,15,17],"7(#11)":[0,4,7,10,14,17],"7#11":[0,4,7,10,14,17],"7(b13)":[0,4,7,10,17],"7b13":[0,4,7,10,17],"9(#11)":[0,4,7,10,14,17],"9#11":[0,4,7,10,14,17],"13(#11)":[0,4,7,10,15,18],"13#11":[0,4,7,10,15,18],maj7:[0,4,7,11],"∆7":[0,4,7,11],"Δ7":[0,4,7,11],maj9:[0,4,7,11,14],"maj7(9)":[0,4,7,11,14],"maj7(11)":[0,4,7,11,16],"maj7(#11)":[0,4,7,11,17],"maj7(13)":[0,4,7,11,18],"maj7(9,13)":[0,4,7,11,14,18],"7sus4":[0,5,7,10],m7sus4:[0,5,7,10],sus4:[0,5,7],sus2:[0,2,7],"7sus2":[0,2,7,10],"9sus4":[0,5,7,14],"13sus4":[0,5,7,18],aug7:[0,4,8,10],"+7":[0,4,8,10],"+":[0,4,8],"7#5":[0,4,8,10],"7♯5":[0,4,8,10],"7+5":[0,4,8,10],"9#5":[0,4,8,10,14],"9♯5":[0,4,8,10,14],"9+5":[0,4,8,10,14],"-7(#5)":[0,3,8,10],"-7#5":[0,3,8,10],"7(#5)":[0,4,8,10],"7(b9,#5)":[0,4,8,10,13],"7b9#5":[0,4,8,10,13],"maj7(#5)":[0,4,8,11],"maj7#5":[0,4,8,11],"maj7(#5,#11)":[0,4,8,11,14],"maj7#5#11":[0,4,8,11,14],"9(#5)":[0,4,8,10,14],"13(#5)":[0,4,8,10,14,18],"13#5":[0,4,8,10,14,18]};function q(e,t){var i=W[t];i||(i=W.M),e+=12;for(var r=[],n=0;n<i.length;n++)r.push(e+i[n]);return r}function U(e,t){void 0!==e&&m.push({cmd:"start",pitch:e,volume:64}),m.push({cmd:"move",duration:t/2*x}),void 0!==e&&m.push({cmd:"stop",pitch:e}),m.push({cmd:"move",duration:t/2*x})}function X(e,t){for(var i=0;i<e.length;i++)m.push({cmd:"start",pitch:e[i],volume:48});for(m.push({cmd:"move",duration:t/2*x}),i=0;i<e.length;i++)m.push({cmd:"stop",pitch:e[i]});m.push({cmd:"move",duration:t/2*x})}var K={"2/2":["boom","chick"],"2/4":["boom","chick"],"3/4":["boom","chick","chick"],"4/4":["boom","chick","boom2","chick"],"5/4":["boom","chick","chick","boom2","chick"],"6/8":["boom","","chick","boom2","","chick"],"9/8":["boom","","chick","boom2","","chick","boom2","","chick"],"12/8":["boom","","chick","boom2","","chick","boom2","","chick","boom2","","chick"]};function Q(){var e=S.num,t=S.den,i=1/t,r=K[e+"/"+t],n=parseInt(e,10)/parseInt(t,10),a=Math.abs(n-w);if(!r||a>.0078125){r=[];for(var s=w/i,o=0;o<s;o++)r.push("chick")}if(0===b.length&&b.push({beat:0,chord:y}),0!==b[0].beat&&y&&b.unshift({beat:0,chord:y}),1!==b.length){for(var c={},l=0;l<b.length;l++){var h=b[l];c[""+Math.floor(h.beat/i)]=h}for(var u=0;u<r.length;u++){var d;switch(c[""+u]&&(d=c[""+u]),r[u]){case"boom":c[""+(u+1)]?X(d.chord.chick,i):U(d.chord.boom,i);break;case"boom2":c[""+(u+1)]?X(d.chord.chick,i):U(d.chord.boom2,i);break;case"chick":X(d.chord.chick,i);break;case"":c[""+u]?X(d.chord.chick,i):m.push({cmd:"move",duration:i*x})}}}else for(var p=0;p<r.length;p++)switch(r[p]){case"boom":U(b[0].chord.boom,i);break;case"boom2":U(b[0].chord.boom2,i);break;case"chick":X(b[0].chord.chick,i);break;case"":m.push({cmd:"move",duration:i*x})}}function Z(e){if(0===e.pattern.length||!1===e.on)return{on:!1};for(var t=e.pattern[0],i=[],r="",n=0,a=0;a<t.length;a++)if("d"===t[a]&&n++,"d"===t[a]||"z"===t[a])0!==r.length?(i.push(r),r=t[a]):r+=t[a];else{if(0===r.length)return{on:!1};r+=t[a]}if(0!==r.length&&i.push(r),e.pattern.length!==2*n+1)return{on:!1};for(var s={on:!0,bars:e.bars,pattern:[]},o=1/S.den,c=0,l=0;l<i.length;l++){r=i[l];for(var h=1,u=!1,d=0,p=1;p<r.length;p++)switch(r[p]){case"/":0!==d&&(h*=d),d=0,u=!0;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":d=10*d+r[p];break;default:return{on:!1}}u?(0===d&&(d=2),h/=d):d&&(h*=d),"d"===r[0]?(s.pattern.push({len:h*o,pitch:e.pattern[1+c],velocity:e.pattern[1+c+n]}),c++):s.pattern.push({len:h*o,pitch:null})}for(var f=0,m=S.num/S.den,g=0;g<s.pattern.length;g++)f+=s.pattern[g].len;var v=f/(e.bars?e.bars:1)/m;for(g=0;g<s.pattern.length;g++)s.pattern[g].len=s.pattern[g].len/v;return s}function $(e){if(0!==k.length||B.on){var t,i,r,n=S.num/S.den;if(0===k.length){k.push({cmd:"program",channel:e,instrument:T});var a=I();if(a>0&&a<n-.01)return void k.push({cmd:"move",duration:a*x})}if(B.on)for(var s=0;s<B.pattern.length;s++){var o=B.pattern[s].len*x;B.pattern[s].pitch?(t=B.pattern[s].pitch,i=o,r=B.pattern[s].velocity,k.push({cmd:"start",pitch:t-60,volume:r}),k.push({cmd:"move",duration:i}),k.push({cmd:"stop",pitch:t-60})):k.push({cmd:"move",duration:o})}else k.push({cmd:"move",duration:n*x})}}}(),e.exports=i},function(e,t,i){var r=i(23),n=i(13);e.exports=function(e,t,i){for(var a=new r,s=0;s<e.length;s++){var o=e[s],c=a.addTrack();if(a.setInstrument(c,o.instrument),0===s&&t)for(var l=0;l<t.length;l++){var h=t[l];a.appendNote(c,h.pitch,1/64,h.volume)}a.appendNote(c,o.pitch,o.durationInMeasures,o.volume)}var u=new n;return u.init({sequence:a,millisecondsPerMeasure:i}).then((function(){return u.prime()})).then((function(){return u.start()}))}},function(e,t,i){var r=i(9).TuneBook,n=i(0),a=i(10),s=i(57),o=i(19),c=i(25),l=i(7);if("function"!=typeof window.CustomEvent){var h=function(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var i=document.createEvent("CustomEvent");return i.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),i};h.prototype=window.Event.prototype,window.CustomEvent=h}var u=function(e){this.textarea=document.getElementById(e),this.initialText=this.textarea.value,this.isDragging=!1};u.prototype.addSelectionListener=function(e){this.textarea.onmousemove=function(t){this.isDragging&&e.fireSelectionChanged()}},u.prototype.addChangeListener=function(e){this.changelistener=e,this.textarea.onkeyup=function(){e.fireChanged()},this.textarea.onmousedown=function(){this.isDragging=!0,e.fireSelectionChanged()},this.textarea.onmouseup=function(){this.isDragging=!1,e.fireChanged()},this.textarea.onchange=function(){e.fireChanged()}},u.prototype.getSelection=function(){return{start:this.textarea.selectionStart,end:this.textarea.selectionEnd}},u.prototype.setSelection=function(e,t){if(this.textarea.setSelectionRange)this.textarea.setSelectionRange(e,t);else if(this.textarea.createTextRange){var i=this.textarea.createTextRange();i.collapse(!0),i.moveEnd("character",t),i.moveStart("character",e),i.select()}this.textarea.focus()},u.prototype.getString=function(){return this.textarea.value},u.prototype.setString=function(e){this.textarea.value=e,this.initialText=this.getString(),this.changelistener&&this.changelistener.fireChanged()},u.prototype.getElem=function(){return this.textarea};var d=function(e,t){var i;if(this.abcjsParams={},t.abcjsParams)for(i in t.abcjsParams)t.abcjsParams.hasOwnProperty(i)&&(this.abcjsParams[i]=t.abcjsParams[i]);if(t.midi_options)for(i in t.midi_options)t.midi_options.hasOwnProperty(i)&&(this.abcjsParams[i]=t.midi_options[i]);if(t.parser_options)for(i in t.parser_options)t.parser_options.hasOwnProperty(i)&&(this.abcjsParams[i]=t.parser_options[i]);if(t.render_options)for(i in t.render_options)t.render_options.hasOwnProperty(i)&&(this.abcjsParams[i]=t.render_options[i]);t.indicate_changed&&(this.indicate_changed=!0),this.editarea="string"==typeof e?new u(e):e,this.editarea.addSelectionListener(this),this.editarea.addChangeListener(this),t.canvas_id?this.div=document.getElementById(t.canvas_id):t.paper_id?this.div=document.getElementById(t.paper_id):(this.div=document.createElement("DIV"),this.editarea.getElem().parentNode.insertBefore(this.div,this.editarea.getElem())),t.synth&&l()&&(this.synth={el:t.synth.el,cursorControl:t.synth.cursorControl,options:t.synth.options}),t.generate_midi&&(this.generate_midi=t.generate_midi,this.abcjsParams.generateDownload&&("string"==typeof t.midi_download_id?this.downloadMidi=document.getElementById(t.midi_download_id):t.midi_download_id&&(this.downloadMidi=t.midi_download_id)),!1!==this.abcjsParams.generateInline&&("string"==typeof t.midi_id?this.inlineMidi=document.getElementById(t.midi_id):t.midi_id&&(this.inlineMidi=t.midi_id))),(t.generate_warnings||t.warnings_id)&&(t.warnings_id?this.warningsdiv=document.getElementById(t.warnings_id):this.warningsdiv=this.div),this.onchangeCallback=t.onchange,t.gui&&(this.target=document.getElementById(e),this.abcjsParams.editable=!0),this.oldt="",this.bReentry=!1,this.parseABC(),this.modelChanged(),this.addClassName=function(e,t){return function(e,t){var i=e.className;return i.length>0&&(i===t||new RegExp("(^|\\s)"+t+"(\\s|$)").test(i))}(e,t)||(e.className+=(e.className?" ":"")+t),e},this.removeClassName=function(e,t){return e.className=n.strip(e.className.replace(new RegExp("(^|\\s+)"+t+"(\\s+|$)")," ")),e},this.setReadOnly=function(e){var t=this.editarea.getElem();e?(t.setAttribute("readonly","yes"),this.addClassName(t,"abc_textarea_readonly")):(t.removeAttribute("readonly"),this.removeClassName(t,"abc_textarea_readonly"))}};d.prototype.renderTune=function(e,t,i){var n=new r(e),s=a();s.parse(n.tunes[0].abc,t,n.tunes[0].startPos-n.header.length);var c=s.getTune();new o(i,this.abcjsParams).engraveABC(c)},d.prototype.redrawMidi=function(){if(this.generate_midi&&!this.midiPause){var e=new window.CustomEvent("generateMidi",{detail:{tunes:this.tunes,abcjsParams:this.abcjsParams,downloadMidiEl:this.downloadMidi,inlineMidiEl:this.inlineMidi,engravingEl:this.div}});window.dispatchEvent(e)}this.synth&&(this.synth.synthControl||(this.synth.synthControl=new c,this.synth.synthControl.load(this.synth.el,this.synth.cursorControl,this.synth.options)),this.synth.synthControl.setTune(this.tunes[0],!1))},d.prototype.modelChanged=function(){if(void 0===this.tunes)return void 0!==this.downloadMidi&&(this.downloadMidi.innerHTML=""),void 0!==this.inlineMidi&&(this.inlineMidi.innerHTML=""),void(this.div.innerHTML="");if(!this.bReentry){if(this.bReentry=!0,this.timerId=null,this.div.innerHTML="",this.engraver_controller=new o(this.div,this.abcjsParams),this.engraver_controller.engraveABC(this.tunes),this.tunes[0].engraver=this.engraver_controller,this.redrawMidi(),this.warningsdiv&&(this.warningsdiv.innerHTML=this.warnings?this.warnings.join("<br />"):"No errors"),this.target)new s(this.target,!0).printABC(this.tunes[0]);this.engraver_controller.addSelectListener(this.highlight.bind(this)),this.updateSelection(),this.bReentry=!1}},d.prototype.paramChanged=function(e){if(e)for(var t in e)e.hasOwnProperty(t)&&(this.abcjsParams[t]=e[t]);this.oldt="",this.fireChanged()},d.prototype.parseABC=function(){var e=this.editarea.getString();if(e===this.oldt)return this.updateSelection(),!1;if(this.oldt=e,""===e)return this.tunes=void 0,this.warnings="",!0;var t=new r(e);this.tunes=[],this.startPos=[],this.warnings=[];for(var i=0;i<t.tunes.length;i++){var n=new a;n.parse(t.tunes[i].abc,this.abcjsParams,t.tunes[i].startPos-t.header.length),this.tunes[i]=n.getTune(),this.startPos[i]=t.tunes[i].startPos;for(var s=n.getWarnings()||[],o=0;o<s.length;o++)this.warnings.push(s[o])}return!0},d.prototype.updateSelection=function(){var e=this.editarea.getSelection();try{this.engraver_controller.rangeHighlight(e.start,e.end)}catch(e){}},d.prototype.fireSelectionChanged=function(){this.updateSelection()},d.prototype.setDirtyStyle=function(e){if(void 0!==this.indicate_changed){var t,i,r=this.editarea.getElem();e?function(e,t){var i=e.className;return i.length>0&&(i===t||new RegExp("(^|\\s)"+t+"(\\s|$)").test(i))}(t=r,i="abc_textarea_dirty")||(t.className+=(t.className?" ":"")+i):function(e,t){e.className=n.strip(e.className.replace(new RegExp("(^|\\s+)"+t+"(\\s+|$)")," "))}(r,"abc_textarea_dirty")}},d.prototype.fireChanged=function(){if(!this.bIsPaused&&this.parseABC()){var e=this;this.timerId&&clearTimeout(this.timerId),this.timerId=setTimeout((function(){e.modelChanged()}),300);var t=this.isDirty();this.wasDirty!==t&&(this.wasDirty=t,this.setDirtyStyle(t)),this.onchangeCallback&&this.onchangeCallback(this)}},d.prototype.setNotDirty=function(){this.editarea.initialText=this.editarea.getString(),this.wasDirty=!1,this.setDirtyStyle(!1)},d.prototype.isDirty=function(){return void 0!==this.indicate_changed&&this.editarea.initialText!==this.editarea.getString()},d.prototype.highlight=function(e,t,i){this.editarea.setSelection(e.startChar,e.endChar)},d.prototype.pause=function(e){this.bIsPaused=e,e||this.fireChanged()},d.prototype.millisecondsPerMeasure=function(){return this.synth.synthControl.visualObj.millisecondsPerMeasure()},d.prototype.pauseMidi=function(e){this.midiPause=e,e||this.redrawMidi()},e.exports=d},function(e,t){var i=function(e,t){this.elem=e,this.text="",this.l=1/8,this.reposition=t||!1};i.prototype.printString=function(e,t){this.reposition&&t&&(t.startChar=this.text.length),this.text+=e,this.reposition&&t&&(t.endChar=this.text.length)},i.prototype.printNewLine=function(){this.text+="\n"},i.prototype.printSpace=function(){this.text[this.text.length-1].match(/\s/)||(this.text+=" ")},i.prototype.printABC=function(e){this.text="",this.abctune=e,this.printHeader(),this.printBody(),this.elem.value=this.text},i.prototype.printHeader=function(){this.printHeaderLine("x","X","1"),this.printHeaderLine("title","T"),this.printHeaderLine("composer","C"),this.printHeaderLine("history","H"),this.printHeaderLine("author","A"),this.printHeaderLine("book","B"),this.printHeaderLine("discography","D"),this.printHeaderLine("url","F"),this.printHeaderLine("group","G"),this.printHeaderLine("instruction","I"),this.printHeaderLine("notes","N"),this.printHeaderLine("origin","O"),this.printHeaderLine("rhythm","R"),this.printHeaderLine("source","S"),this.printHeaderLine("unalignedwords","W"),this.printHeaderLine("transcription","Z"),this.printHeaderLine("NULL","L","1/8"),this.printHeaderLine("NULL","M",this.getMeterString(this.abctune.lines[0].staff[0].meter)),this.printHeaderLine("NULL","K",this.getKeyString(this.abctune.lines[0].staff[0].key))},i.prototype.getKeyString=function(e){return e.root+e.acc+e.mode},i.prototype.getMeterString=function(e){switch(e.type){case"cut_time":return"C|";case"common_time":return"C";case"specified":return e.value[0].den?e.value[0].num+"/"+e.value[0].den:e.value[0].num}return""},i.prototype.printHeaderLine=function(e,t,i){var r=this.abctune.metaText[e]||i;if(void 0!==r)for(var n=r.split("\n"),a=0;a<n.length;a++)this.printString(t+": "+n[a]),this.printNewLine()},i.prototype.getElem=function(){return this.abcline.length<=this.pos?null:this.abcline[this.pos]},i.prototype.getNextElem=function(){return this.abcline.length<=this.pos+1?null:this.abcline[this.pos+1]},i.prototype.printBody=function(){for(var e=0;e<this.abctune.lines.length;e++){var t=this.abctune.lines[e];t.staff?this.printABCLine(t.staff):t.subtitle&&0!==e||t.text}},i.prototype.printABCLine=function(e){for(this.s=0;this.s<e.length;this.s++)this.printABCStaff(e[this.s])},i.prototype.printABCStaff=function(e){for(this.v=0;this.v<e.voices.length;this.v++)this.printABCVoice(e.voices[this.v])},i.prototype.printABCVoice=function(e){for(this.abcline=e,this.pos=0;this.pos<this.abcline.length;this.pos++)this.printABCElement();this.printNewLine()},i.prototype.printABCElement=function(){var e=this.getElem();switch(e.el_type){case"note":this.printBeam();break;case"bar":this.printBarLine(e)}},i.prototype.printBeam=function(){if(this.printSpace(),this.getElem().startBeam&&!this.getElem().endBeam)for(;this.getElem()&&(this.printNote(this.getElem()),!this.getElem().endBeam);)this.pos++;else this.printNote(this.getElem());this.printSpace()},i.prototype.printNote=function(e){var t,i="";if(void 0!==e.chord)for(t=0;t<e.chord.length;t++)i+='"'+e.chord[t].name+'"';var r={staccato:".",upbow:"u",downbow:"v",roll:"~",fermata:"H",slide:"J",accent:"L",mordent:"M",pralltriller:"P",trill:"T",lower:"."};if(void 0!==e.decoration)for(t=0;t<e.decoration.length;t++){var n=e.decoration[t];r[n]?i+=r[n]:(i+="!",i+=n,i+="!")}if(void 0!==e.gracenotes){for(i+="{",t=0;t<e.gracenotes.length;t++)i+=this.getNoteString(e.gracenotes[t]);i+="}"}var a=!1;if(1===e.pitches.length&&e.pitches[0].startSlur&&(a=!0,i+=this.multiplyString("(",e.pitches[0].startSlur.length)),e.startSlur&&(i+=this.multiplyString("(",e.startSlur.length)),(1===e.pitches.length&&e.pitches[0].endSlur||e.endSlur)&&(a=!0),e.startTriplet&&(i+="(3"),e.pitches){for(e.pitches.length>1&&(i+="["),t=0;t<e.pitches.length;t++)e.pitches[t].duration=e.duration,i+=this.getNoteString(e.pitches[t],a);e.pitches.length>1&&(i+="]")}1===e.pitches.length&&e.pitches[0].endSlur&&(i+=this.multiplyString(")",e.pitches[0].endSlur.length)),e.endSlur&&(i+=this.multiplyString(")",e.endSlur.length)),this.printString(i,e)},i.prototype.getNoteString=function(e,t){var i="";!t&&e.startSlur&&(i+="(");var r="";switch(e.accidental){case"quartersharp":r="^/";break;case"dblsharp":r="^^";break;case"sharp":r="^";break;case"quarterflat":r="_/";break;case"flat":r="_";break;case"dblflat":r="__";break;case"natural":r="="}i+=r;var n=["C","D","E","F","G","A","B"][this.extractNote(e.pitch)],a=this.extractOctave(e.pitch);if(a>0)for(n=n.toLowerCase(),a--;a>0;)n+="'",a--;else for(;a<0;)n+=",",a++;return i+=n,e.duration&&(i+=this.getDurationString(e.duration)),!t&&e.endSlur&&(i+=")"),e.startTie&&(i+="-"),i},i.prototype.getDurationString=function(e){if(e/this.l>1)return e/this.l;var t="";return this.l/e>1&&(t+="/",this.l/e>2&&(t+=this.l/e)),t},i.prototype.extractNote=function(e){var t=e%7;return t<0&&(t+=7),t},i.prototype.extractOctave=function(e){return Math.floor(e/7)},i.prototype.printBarLine=function(e){var t="";switch(e.type){case"bar_thin":t+="|";break;case"bar_thin_thick":t+="|]";break;case"bar_thin_thin":t+="||";break;case"bar_thick_thin":t+="[|";break;case"bar_dbl_repeat":t+=":||:";break;case"bar_left_repeat":t+="|:";break;case"bar_right_repeat":t+=":|";break;case"bar_invisible":t+=""}this.printString(t,e)},i.prototype.multiplyString=function(e,t){for(var i="";t>0;t--)i+=e;return i},e.exports=i}]);