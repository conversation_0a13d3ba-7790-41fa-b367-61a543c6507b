<script setup lang="ts">
import classicCKEditor from '@/components/editors/Veditor.vue';
import CmpButton from '@/components/CmpButton.vue';
import AddDialog from '@/view/other/editKlg/AddDialog.vue';

import { editCntInfoApi } from '@/apis/path/klg';
import type { params2EditCntInfo } from '@/apis/path/klg';
import { KlgDetail, AreaListItem, RefListItem } from '@/utils/type';
import { onMounted, onUpdated, ref, watch, computed, nextTick } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import hljs from 'highlight.js';
import router from '@/router';
import { convertImgTagLongUrls } from '@/utils/latexUtils';

const props = defineProps({
  showStep3: Boolean,
  detail: Object as () => KlgDetail,
  areaList: Array as () => AreaListItem[],
  refList: Array as () => RefListItem[]
});
const emits = defineEmits(['step', 'refresh']);
const editorNote = ref();
const cntFormRef = ref<FormInstance>();
const cntFormRef2 = ref<FormInstance>();
const noteFlag = ref(false);
const dialogRef = ref();
const cntForm = ref({
  klgCode: '',
  title: '',
  type: '',
  synList: [] as String[],
  areaList: [] as AreaListItem[],
  refList: [] as RefListItem[],
  note: '',
  cnt: ''
});

// 处理打开编者规范
const handleOpenRule = () => {
  window.open('/rule', '_blank');
};
// 处理上一步/下一步
const handleStep = (step: number) => {
  handleSave(step).then((result) => {
    if (result) {
      emits('step', step);
    }
  });
};
// 处理dialog
const handleDialog = (mode: number) => {
  // 1: 领域 | 2: 文献
  if (mode === 1) {
    const list = ref<string[]>([]);
    cntForm.value.areaList.forEach((item) => {
      list.value.push(item.areaCode);
    });
    dialogRef.value.showDialog(mode, list.value);
  } else if (mode === 2) {
    const list = cntForm.value.refList.map((item) => {
      return {
        refId: item.refId,
        cntName: item.cntName,
        indexPage: item.indexPage
      };
    });
    dialogRef.value.showDialog(mode, list);
  }
};
// 处理移除领域tag
const handleRemoveTag = (item: AreaListItem) => {
  if (cntForm.value.areaList.length <= 1) {
    ElMessage.warning('请至少选择一个领域！');
    return;
  }
  const index = cntForm.value.areaList.findIndex((i) => i.areaCode === item.areaCode);
  cntForm.value.areaList.splice(index, 1);
};
// 处理移除文献ref
const handleRemoveRef = (item: RefListItem) => {
  const index = cntForm.value.refList.findIndex((i) => i.refId === item.refId);
  cntForm.value.refList.splice(index, 1);
};
// 处理编辑领域
const handleEditArea = (list: []) => {
  cntForm.value.areaList = list;
};
// 处理编辑文献
const handleEditRef = (list: []) => {
  cntForm.value.refList = list;
};
// 处理保存
const handleSave = async (status?: number, toLink?: boolean): Promise<boolean> => {
  // status: 0: 存草稿 | 1: 提交
  const params: params2EditCntInfo = {
    klgCode: cntForm.value.klgCode,
    cnt: convertImgTagLongUrls(editorNote.value.getHtml()),
    areaCodes: cntForm.value.areaList.map((item) => item.areaCode),
    klgToRefVos: cntForm.value.refList.map((item) => ({
      refId: item.refId,
      indexPage: item.indexPage
    })),
    status: status ? status : 0
  };

  try {
    const res = await editCntInfoApi(params);

    if (res.success) {
      ElMessage.success('保存成功');
      cntForm.value.klgCode = res.data.klgCode;

      // 更新父组件的 klgCode 和 step
      emits('refresh', {
        klgCode: cntForm.value.klgCode,
        step: 2
      });

      if (toLink) {
        window.open(`/linkklg?klgCode=${cntForm.value.klgCode}`, '_self');
      }
      return true;
    } else {
      ElMessage.error(res.message);
      return false;
    }
  } catch (error) {
    return false;
  }
};
// 处理提交
const handleSubmit = () => {
  cntFormRef.value?.validate((valid) => {
    if (valid) {
      cntFormRef2.value?.validate((valid) => {
        if (valid) {
          handleSave(1).then((result) => {
            if (result) {
              ElMessage.success('提交成功');
              window.open('/klg/maintain', '_self');
            }
          });
        }
      });
    }
  });
};
// 预处理 note 内容，使用 hljs 对代码块进行高亮处理
const processedNote = computed(() => {
  if (!cntForm.value.note) return '';

  // 使用正则表达式匹配 <pre><code> 标签
  const codeBlockRegex = /<pre><code(?:\s+class="([^"]*)")?[^>]*>([\s\S]*?)<\/code><\/pre>/g;

  return cntForm.value.note.replace(codeBlockRegex, (match, className, codeContent) => {
    try {
      // 解码 HTML 实体
      const decodedContent = codeContent
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'");

      // 使用 hljs 进行语法高亮
      const highlightResult = hljs.highlightAuto(decodedContent);

      // 构建高亮后的代码块
      const highlightedCode = highlightResult.value;
      const detectedLanguage = highlightResult.language || '';

      // 保持原有的 class 属性，并添加 hljs 相关类
      let classAttr = 'hljs';
      if (className) {
        classAttr = `${className} hljs`;
      }
      if (detectedLanguage) {
        classAttr += ` language-${detectedLanguage}`;
      }

      return `<pre><code class="${classAttr}">${highlightedCode}</code></pre>`;
    } catch (error) {
      console.warn('代码高亮处理失败:', error);
      // 如果处理失败，返回原始内容
      return match;
    }
  });
});
watch(
  () => props,
  async () => {
    if (props.detail) {
      cntForm.value.klgCode = props.detail.klgCode;
      cntForm.value.title = props.detail.title;
      cntForm.value.type = props.detail.sortTitle;
      cntForm.value.note = props.detail.notice ? props.detail.notice : '';
      // 这个不能动，有bug
      if (props.detail.cnt !== '' && props.detail && props.detail.cnt) {
        const str = props.detail.cnt as string;
        // 使用 nextTick 确保编辑器组件已挂载
        await nextTick();
        if (editorNote.value?.html2MdWhenReady) {
          cntForm.value.cnt = await editorNote.value.html2MdWhenReady(str);
        }
      }
      if (props.detail.sysTitles === '') {
        cntForm.value.synList = [];
      } else {
        cntForm.value.synList = props.detail.sysTitles.split('@@');
      }
    }
    if (props.areaList) {
      cntForm.value.areaList = props.areaList;
    }
    if (props.refList) {
      cntForm.value.refList = props.refList;
    }
  },
  { deep: true, immediate: true }
);

// 取消
const handleCancel = () => {
  const curNode = sessionStorage.getItem('currentNode');
  if (curNode) {
    const node = JSON.parse(curNode);
    router.push(`klg/maintain?areaCode=${node.areaCode}&label=${node.label}`);
  } else {
    const defaultNode = sessionStorage.getItem('defaultNode');
    if (defaultNode) {
      const node = JSON.parse(defaultNode);
      router.push(`klg/maintain?areaCode=${node.areaCode}&label=${node.label}`);
    } else {
      router.push(`klg/maintain`);
    }
  }
};
</script>
<template>
  <div class="wrapper">
    <div class="wrapper-up">
      <div class="wrapper-up-left">
        <div class="header-wrapper">
          <el-steps class="header-steps" :active="1" finish-status="success" align-center>
            <el-step title="基础信息" />
            <el-step title="内容信息" />
            <el-step v-if="props.showStep3" title="论证信息" />
          </el-steps>
          <span class="header-tips" @click="handleOpenRule">编者规范</span>
        </div>
        <div class="line"></div>
        <div class="info-container">
          <div class="info-header">
            <span class="info-title ck-content" v-html="cntForm.title"> </span>
            <span class="info-type">{{ cntForm.type }}</span>
          </div>
          <div class="info-footer">
            <span class="info-footer-left">
              <span class="info-syn"
                ><span style="white-space: nowrap; margin-right: 40px">同义词</span>
                <div
                  v-if="cntForm.synList.length === 0"
                  style="white-space: nowrap; color: var(--color-grey)"
                >
                  暂无同义词
                </div>
                <div v-else class="info-syn-text-block">
                  <el-tag
                    :disable-transitions="true"
                    effect="plain"
                    class="info-syn-text"
                    v-for="(item, index) in cntForm.synList"
                    :key="index"
                  >
                    <span class="ck-content" v-html="item"> </span>
                  </el-tag></div
              ></span>
            </span>
            <span class="info-footer-right" @click="noteFlag = !noteFlag">
              <img src="@/assets/image/klg/u1695.svg" />
              <span>查看编者笔记</span>
            </span>
          </div>
        </div>
        <div class="form-container">
          <div class="line"></div>
          <el-form
            ref="cntFormRef"
            :model="cntForm"
            style="width: 100%; margin-top: 15px"
            label-postion="right"
            label-width="100"
          >
            <el-form-item
              label="知识内容"
              prop="cnt"
              :rules="{
                required: true,
                message: '请输入内容',
                trigger: 'blur'
              }"
            >
              <div style="width: 97%; height: 400px">
                <inlineCKEditor
                 
                  ref="editorNote"
                  placeholder="请输入结论"
                  :showToolbar="true"
                  :height="35"
                  :auto-height="true"
                  :max-height="95"
                ></inlineCKEditor>
              </div>
            </el-form-item>
            <el-form-item
              label="所属领域"
              prop="areaList"
              :rules="{
                required: true,
                message: '请选择领域',
                trigger: 'blur'
              }"
            >
              <div class="domain-container">
                <span class="add-block" @click="handleDialog(1)">
                  <img src="@/assets/image/add.svg" />
                  <span class="add-text">添加所属领域</span>
                </span>
                <span>
                  <span v-for="item in cntForm.areaList" :key="item.areaCode">
                    <el-tag
                      class="tag"
                      style="font-size: 14px; margin-left: 5px"
                      effect="plain"
                      size="large"
                      :disable-transitions="true"
                      @close="handleRemoveTag(item)"
                      closable
                    >
                      {{ item.label }}
                    </el-tag>
                  </span>
                </span>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="wrapper-up-right" v-if="noteFlag">
        <div class="note-title">查看编者笔记</div>
        <div class="line"></div>
        <div style="padding: 10px">
          <span class="ck-content" v-if="cntForm.note">
            <ContentRenderer :content="processedNote"></ContentRenderer>
          </span>
          <span v-else style="padding: 10px; font-size: 14px">暂无内容</span>
        </div>
      </div>
    </div>
    <div class="wrapper-mid">
      <el-form
        ref="cntFormRef2"
        :model="cntForm"
        style="width: 100%; margin-top: 15px"
        label-postion="right"
        label-width="100"
      >
        <el-form-item label="参考文献" prop="refList">
          <div class="ref-container">
            <span class="add-block" @click="handleDialog(2)">
              <img src="@/assets/image/add.svg" />
              <span class="add-text">添加参考文献</span>
            </span>
            <div
              class="ref-block"
              :class="
                item.indexPage != '' && item.indexPage != null
                  ? 'active-light-background'
                  : 'active-white-background'
              "
              style="width: 100%; margin-bottom: 15px"
              v-for="(item, index) in cntForm.refList"
              :key="item.refId"
            >
              <span class="ref-title">{{ item.cntName }}</span>

              <span class="ref-chapter">
                <el-form-item
                  :prop="`refList.${index}.indexPage`"
                  :rules="{
                    required: true,
                    message: '请输入章节内容',
                    trigger: 'blur'
                  }"
                >
                  <el-input
                    class="ref-index-page"
                    v-model="item.indexPage"
                    @submit.native.prevent
                  ></el-input>
                </el-form-item>
              </span>

              <span class="rm-block" @click="handleRemoveRef(item)"
                ><img src="@/assets/image/rm.svg"
              /></span>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="wrapper-down">
      <div class="footer">
        <CmpButton type="info" @click="handleCancel">取消</CmpButton>
        <CmpButton type="info" @click="handleStep(0)">上一步</CmpButton>
        <CmpButton type="primary" @click="handleSave()">存草稿</CmpButton>
        <CmpButton type="primary" v-if="!showStep3" @click="handleSave(0, true)"
          >保存并关联</CmpButton
        >
        <CmpButton type="primary" v-if="showStep3" @click="handleStep(2)">下一步</CmpButton>
        <CmpButton type="primary" v-else @click="handleSubmit">提交</CmpButton>
      </div>
    </div>
  </div>
  <!-- other -->
  <add-dialog ref="dialogRef" @edit-area="handleEditArea" @edit-ref="handleEditRef"></add-dialog>
</template>
<style scoped>
.wrapper {
  color: var(--color-black);
  .wrapper-up {
    width: 1200px;
    min-height: 750px;
    font-family: var(--text-family);
    display: flex;
    flex-direction: row;
    .wrapper-up-left {
      flex: 1;
      background-color: white;
      color: var(--color-black);
      display: flex;
      flex-direction: column;
      .header-wrapper {
        width: 100%;
        display: flex;
        justify-content: center;
        position: relative;
        /* el-steps 进行状态 */
        ::v-deep(.is-process) {
          color: var(--color-black);
          font-weight: 400;
          font-size: 14px;
        }
        /* el-steps 等待状态 */
        ::v-deep(.is-wait) {
          color: var(--color-invalid);
          font-weight: 400;
          font-size: 14px;
        }
        .header-steps {
          --el-color-success: var(--color-black);
          margin-top: 10px;
          width: 400px;
        }
        .header-tips {
          color: var(--color-primary);
          font-size: 12px;
          cursor: pointer;
          position: absolute;
          right: 40px;
          bottom: 5px;
          &:hover {
            font-weight: 600;
          }
        }
      }
      .info-container {
        padding: 20px 30px;
        .info-header {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          word-break: break-all;
          .info-title {
            font-size: 24px;
            font-weight: 600;
            margin-right: 50px;
          }
          .info-type {
            min-width: 80px;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-grey);
            border: 1px solid var(--color-grey);
            border-radius: 5px;
            padding: 2px;
            white-space: nowrap;
          }
        }
        .info-footer {
          width: 100%;
          display: flex;
          justify-content: space-between;
          margin-top: 10px;
          .info-footer-left {
            width: 100%;
            display: flex;
            flex-direction: column;
            .info-syn {
              width: 90%;
              font-size: 14px;
              font-weight: 600;
              display: flex;
              flex-direction: row;
              .info-syn-text-block {
                width: 100%;
                white-space: normal;
                overflow-wrap: break-word;
                .info-syn-text {
                  font-weight: 400;
                  white-space: normal;
                }
              }
            }
          }
          .info-footer-right {
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
            font-size: 12px;
            color: var(--color-primary);
            cursor: pointer;
          }
          .note {
            display: flex;
            align-items: center;
            cursor: pointer;
            .note-text {
              text-wrap: nowrap;
              font-size: 12px;
              color: var(--color-primary);
              margin-left: 5px;
            }
          }
        }
      }
      .form-container {
        width: 100%;
        background-color: white;
        .domain-container {
          display: flex;
          flex-direction: column;
        }
      }
    }
    .wrapper-up-right {
      margin-left: 10px;
      background-color: white;
      width: 500px;
      .note-title {
        margin-left: 10px;
        font-weight: 600;
        padding: 10px 5px;
      }
    }
  }
  .wrapper-mid {
    background-color: white;
    margin: 10px 0;
    padding: 5px 0;
    width: 1200px;
    .ref-container {
      width: 100%;
      .ref-block {
        display: flex;
        flex-direction: row;
        width: 100%;
        margin-bottom: 5px;
        .ref-title {
          background-color: var(--color-light);
          padding: 0 10px;
          width: 60%;
          border-radius: 2px;
        }
        .ref-chapter {
          margin-left: 10px;
          background-color: var(--color-light);
          width: 30%;
          border-radius: 2px;
          .ref-index-page {
            border-radius: 5px;
          }
        }
        .rm-block {
          margin-left: 5px;
          display: flex;
          align-items: center;
          cursor: pointer;
        }
      }
    }
  }
  .wrapper-down {
    background-color: white;
    padding: 10px;
    .footer {
      margin: 20px;
      width: 100%;
      display: flex;
      justify-content: center;
      gap: 40px;
    }
  }
  .add-block {
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 90px;
    .add-text {
      margin-left: 5px;
      color: var(--color-primary);
      font-size: 12px;
    }
    .tag {
    }
  }
}
.active-light-background {
  ::v-deep(.el-input__wrapper :focus) {
    background-color: white;
  }
  ::v-deep(.el-input__wrapper) {
    --el-input-border-color: var(--color-light);
    background-color: var(--color-light);
  }
}
.active-white-background {
  ::v-deep(.el-input__wrapper) {
    background-color: white;
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
</style>
