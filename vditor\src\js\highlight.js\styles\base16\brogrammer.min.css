/*!
  Theme: Brogrammer
  Author: <PERSON><PERSON> (http://github.com/piggyslasher)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#4e5ab7;background:#1f1f1f}.hljs ::selection,.hljs::selection{background-color:#2dc55e;color:#4e5ab7}.hljs-comment{color:#ecba0f}.hljs-tag{color:#2a84d2}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#4e5ab7}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#d6dbe5}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#de352e}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#1dd361}.hljs-strong{font-weight:700;color:#1dd361}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#f3bd09}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#1081d6}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#5350b9}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#0f7ddb}.hljs-emphasis{color:#0f7ddb;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#fff}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
