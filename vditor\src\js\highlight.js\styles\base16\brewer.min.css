/*!
  Theme: Brewer
  Author: <PERSON><PERSON><PERSON> (http://github.com/tpoisot)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#b7b8b9;background:#0c0d0e}.hljs ::selection,.hljs::selection{background-color:#515253;color:#b7b8b9}.hljs-comment{color:#737475}.hljs-tag{color:#959697}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#b7b8b9}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#e31a1c}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#e6550d}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#dca060}.hljs-strong{font-weight:700;color:#dca060}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#31a354}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#80b1d3}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#3182bd}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#756bb1}.hljs-emphasis{color:#756bb1;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#b15928}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
