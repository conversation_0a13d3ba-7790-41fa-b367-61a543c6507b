<script setup lang="ts">
import CmpButton from '@/components/CmpButton.vue';
import MyFlipper from '@/components/MyFlipper.vue';
import AuditInfoDrawer from '@/view/other/auditKlg/AuditInfoDrawer.vue';

import { ref, watch } from 'vue';
import { AreaListItem } from '@/utils/type';
import { useRoute } from 'vue-router';
import {
  getAuditKlgInfoApi,
  getAuditRecordListApi,
  params2GetAuditRecordList,
  auditKlgApi,
  params2Audit
} from '@/apis/path/audit';
import { getKlgDetailApi } from '@/apis/path/klg';
import { KlgTypeDict, auditTypeDict, MAX_PAGESIZE } from '@/utils/constant';
import { findKeyByValue } from '@/utils/func';
import { ElMessage, FormInstance } from 'element-plus';

const dataFormRef = ref<FormInstance>();
const drawerRef = ref();
const route = useRoute();
const currentPage = ref(1);
const pageSize = MAX_PAGESIZE;
const total = ref(0);
const tableData = ref();

const dataForm = ref({
  klgCode: '',
  title: '',
  type: '',
  author: '',
  saveTime: '',
  adminName: '',
  step: '',
  auditResult: 0,
  cnt: '',
  synList: [] as String[],
  areaList: [] as AreaListItem[]
});
// 获取审核信息接口
const getAuditKlgInfo = () => {
  getKlgDetailApi(dataForm.value.klgCode).then((res) => {
    if (res.success) {
      dataForm.value.klgCode = res.data.detail.klgCode;
      dataForm.value.title = res.data.detail.title;
      dataForm.value.type = res.data.detail.sortId;
      dataForm.value.author = res.data.detail.whoName;
      dataForm.value.saveTime = res.data.detail.modifiedTime;
      if (res.data.detail.sysTitles !== '') {
        dataForm.value.synList = res.data.detail.sysTitles.split('@@');
      }
      dataForm.value.areaList = res.data.areaList.map((item) => {
        return {
          areaCode: item.areaCode,
          label: item.title
        };
      });
    }
  });
  getAuditKlgInfoApi(dataForm.value.klgCode).then((res) => {
    if (res.success) {
      dataForm.value.adminName = res.data.result.adminName;
      dataForm.value.step = res.data.result.actionName;
    }
  });
  getRecordList();
};

// 获取审核记录接口
const getRecordList = (page?: number) => {
  const params: params2GetAuditRecordList = {
    current: page ? page : currentPage.value,
    limit: pageSize,
    klgCode: dataForm.value.klgCode
  };
  getAuditRecordListApi(params).then((res) => {
    if (res.success) {
      tableData.value = res.data.list;
      // console.log("tableData", tableData);
      total.value = res.data.total;
    }
  });
};
// 处理换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getRecordList(newPage);
};

// 处理打开编者规范
const handleOpenRule = () => {
  window.open('/rule', '_blank');
};
// 处理drawer
const handleDrawer = (row: any) => {
  drawerRef.value.showDrawer(row);
};
// 处理关闭
const handleClose = () => {
  window.close();
};
// 处理提交
const handleSubmit = () => {
  dataFormRef.value?.validate((valid) => {
    if (valid) {
      const params: params2Audit = {
        klgCode: dataForm.value.klgCode,
        auditResult: dataForm.value.auditResult,
        audit_opinion: dataForm.value.cnt
      };
      auditKlgApi(params).then((res) => {
        if (res.success) {
          ElMessage.success('提交成功');
          window.close();
        } else {
          ElMessage.error(res.message);
        }
      });
    }
  });
};
// 查看知识内容
const handleShowKlg = () => {
  window.open(`/klgdetail?klgCode=${dataForm.value.klgCode}`);
};
watch(
  () => route.query.klgCode,
  () => {
    if (route.query.klgCode) {
      dataForm.value.klgCode = route.query.klgCode.toString();
      getAuditKlgInfo();
    }
  },
  { deep: true, immediate: true }
);
</script>
<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <div class="header">
        <span class="header-left">
          <span class="header-title"><span class="ck-content" v-html="dataForm.title"></span></span>
          <span class="header-type">{{ findKeyByValue(dataForm.type, KlgTypeDict) }}</span>
        </span>
      </div>
      <div class="base-info" style="margin: 5px 0">
        <span class="author-info">
          <span style="margin-right: 10px">作者</span>
          <span>{{ dataForm.author }}</span>
        </span>
        <span class="save-time">
          <span style="margin-right: 10px">保存时间</span>
          <span>{{ dataForm.saveTime }}</span>
        </span>
      </div>
      <div class="detail-info">
        <span class="detail-info-left">
          <span class="info-syn"
            ><span style="white-space: nowrap; margin-right: 40px">同义词</span>
            <div
              v-if="dataForm.synList.length === 0"
              style="white-space: nowrap; color: var(--color-grey)"
            >
              暂无同义词
            </div>
            <div v-else class="info-syn-text-block">
              <el-tag
                effect="plain"
                :disable-transitions="true"
                class="info-syn-text"
                v-for="(item, index) in dataForm.synList"
                :key="index"
              >
                <span class="ck-content" v-html="item"></span>
              </el-tag>
            </div>
          </span>
          <span class="info-syn">
            <span style="white-space: nowrap; margin-right: 26px">所属领域</span>
            <div class="info-syn-text-block" style="margin-top: 5px">
              <el-tag
                effect="plain"
                :disable-transitions="true"
                class="info-syn-text"
                v-for="(item, index) in dataForm.areaList"
                :key="index"
              >
                <span class="ck-content" v-html="item.label"> </span>
              </el-tag>
            </div>
          </span>
        </span>
        <span class="detail-info-right">
          <span class="detail-info-right-text" @click="handleOpenRule">编者规范</span>
        </span>
      </div>
    </div>
    <div class="line" style="margin: 5px 0"></div>
    <div class="main-container">
      <div class="btn-bar">
        <span class="btn" @click="handleShowKlg">
          <span class="btn-text">查看知识内容</span>
          <el-icon><ArrowRightBold /></el-icon>
        </span>
      </div>
      <div class="checkTitle">审核操作</div>

      <div class="main-form">
        <el-form ref="dataFormRef" label-position="right" label-width="80" :model="dataForm">
          <div class="form-btn-group">
            <span>
              <el-form-item label="当前环节">{{ dataForm.step }} </el-form-item>
              <el-form-item label="审核人">{{ dataForm.adminName }} </el-form-item>
              <el-form-item label="审核结果">
                <el-radio-group v-model="dataForm.auditResult">
                  <el-radio :value="1">审核通过</el-radio>
                  <el-radio :value="0">退回</el-radio>
                </el-radio-group>
              </el-form-item>
            </span>
            <span class="right-btn">
              <CmpButton type="info" style="margin-right: 20px" @click="handleClose"
                >返回</CmpButton
              >
              <CmpButton type="primary" @click.prevent="handleSubmit">提交</CmpButton>
            </span>
          </div>
          <el-form-item
            label="审核意见"
            prop="cnt"
            :rules="{
              required: true,
              message: '请输入意见',
              trigger: 'blur'
            }"
          >
            <el-input
              placeholder="请输入意见"
              style="
                --el-color-primary: var(--color-primary);
                width: 100%;
                height: 100%;
                overflow-y: hidden;
              "
              show-word-limit
              resize="none"
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 23 }"
              v-model="dataForm.cnt"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="checkTitle">审核记录</div>

      <div class="record-list">
        <el-table :data="tableData">
          <el-table-column prop="createTime" label="时间" align="center"> </el-table-column>
          <el-table-column label="审核人" align="center">
            <template #default="scope">
              {{ scope.row.processorName ? scope.row.processorName : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="actionName" label="审核环节" align="center"> </el-table-column>
          <el-table-column label="审核结果" align="center">
            <template #default="scope">
              <span class="result-btn" @click="handleDrawer(scope.row)">
                {{
                  findKeyByValue(scope.row.auditResult, auditTypeDict)
                    ? findKeyByValue(scope.row.auditResult, auditTypeDict)
                    : '-'
                }}
              </span>
            </template>
          </el-table-column>
        </el-table>
        <my-flipper
          @change-page="handleChangePage"
          :current="currentPage"
          :page-size="pageSize"
          :total="total"
        ></my-flipper>
      </div>
    </div>
  </div>
  <!-- other -->
  <AuditInfoDrawer ref="drawerRef"></AuditInfoDrawer>
</template>
<style scoped>
.wrapper {
  width: 1200px;
  min-height: 750px;
  color: var(--color-black);
  background-color: white;
  padding: 20px;
  .header-wrapper {
    .header {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      .header-left {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;
        .header-title {
          font-size: 24px;
          font-weight: 600;
          margin-right: 10px;
          :deep(p) {
            margin: 0;
          }
        }
        .header-type {
          display: flex;
          align-items: center;
          height: 20px;
          padding: 0 5px;
          border: 1px solid var(--color-grey);
          color: var(--color-grey);
          border-radius: 3px;
          font-size: 14px;
        }
      }
      .header-right {
        display: flex;
        align-items: center;
        cursor: pointer;
        .header-right-text {
          white-space: nowrap;
          margin-left: 5px;
          color: var(--color-primary);
          font-size: 12px;
        }
      }
    }
    .base-info {
      width: 100%;
      color: var(--color-deep);
      font-size: 12px;
      .author-info {
        margin-right: 40px;
      }
      .save-time {
      }
    }
    .detail-info {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      color: var(--color-black);
      font-size: 14px;
      position: relative;
      .detail-info-left {
        width: 100%;
        display: flex;
        flex-direction: column;
        .info-syn {
          width: 90%;
          font-size: 14px;
          font-weight: 600;
          display: flex;
          flex-direction: row;
          .info-syn-text-block {
            width: 100%;
            white-space: normal;
            overflow-wrap: break-word;
            .info-syn-text {
              font-weight: 400;
              white-space: normal;
            }
          }
        }
      }
      .detail-info-right {
        position: absolute;
        bottom: 2px;
        right: 0;
        display: flex;
        align-items: center;
        cursor: pointer;
        .detail-info-right-text {
          color: var(--color-primary);
          margin-left: 5px;
          font-size: 12px;
        }
      }
    }
  }
  .main-container {
    width: 100%;
    .btn-bar {
      width: 100%;
      height: 50px;
      background-color: #f2f2f2;
      border-radius: 4px;
      display: flex;
      .btn {
        margin: 0 auto;
        font-weight: 400;
        color: rgba(22, 97, 171, 0.996);
        font-feature-settings: 'kern';
        cursor: pointer;
        line-height: 1;
        display: flex;
        align-items: center;
        &:hover {
          font-weight: bold;
        }
      }
    }
    .main-form {
      margin-top: 5px;
      padding: 15px;
      border-radius: 5px;
      background-color: var(--color-light);
      :deep(.el-form-item) {
        margin: 0;
      }
      :deep(.el-form-item__label) {
        color: var(--color-black);
        font-size: 14px;
        font-weight: 600;
      }
      .form-btn-group {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        .right-btn {
          display: flex;
        }
      }
    }
    .record-list {
      .result-btn {
        color: var(--color-primary);
        cursor: pointer;
        &:hover {
          font-weight: 600;
        }
      }
    }
  }
}

.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.checkTitle {
  padding: 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  line-height: normal;
  font-feature-settings: 'kern';
}
</style>
