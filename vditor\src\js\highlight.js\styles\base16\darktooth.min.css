/*!
  Theme: Dark<PERSON>
  Author: <PERSON> (https://github.com/jasonm23)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#a89984;background:#1d2021}.hljs ::selection,.hljs::selection{background-color:#504945;color:#a89984}.hljs-comment{color:#665c54}.hljs-tag{color:#928374}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#a89984}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#fb543f}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#fe8625}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#fac03b}.hljs-strong{font-weight:700;color:#fac03b}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#95c085}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#8ba59b}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#0d6678}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#8f4673}.hljs-emphasis{color:#8f4673;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#a87322}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
